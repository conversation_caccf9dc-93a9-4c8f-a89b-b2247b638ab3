{"version": 3, "sources": ["../../../src/server/lib/start-server.ts"], "sourcesContent": ["import { getNetworkHost } from '../../lib/get-network-host'\n\nif (performance.getEntriesByName('next-start').length === 0) {\n  performance.mark('next-start')\n}\nimport '../next'\nimport '../require-hook'\n\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { SelfSignedCertificate } from '../../lib/mkcert'\nimport type { WorkerRequestHandler, WorkerUpgradeHandler } from './types'\n\nimport fs from 'fs'\nimport v8 from 'v8'\nimport path from 'path'\nimport http from 'http'\nimport https from 'https'\nimport os from 'os'\nimport Watchpack from 'next/dist/compiled/watchpack'\nimport * as Log from '../../build/output/log'\nimport setupDebug from 'next/dist/compiled/debug'\nimport {\n  RESTART_EXIT_CODE,\n  getFormattedDebugAddress,\n  getNodeDebugType,\n} from './utils'\nimport { formatHostname } from './format-hostname'\nimport { initialize } from './router-server'\nimport { CONFIG_FILES } from '../../shared/lib/constants'\nimport { getStartServerInfo, logStartInfo } from './app-info-log'\nimport { validateTurboNextConfig } from '../../lib/turbopack-warning'\nimport { type Span, trace, flushAllTraces } from '../../trace'\nimport { isPostpone } from './router-utils/is-postpone'\nimport { isIPv6 } from './is-ipv6'\nimport { AsyncCallbackSet } from './async-callback-set'\nimport type { NextServer } from '../next'\nimport type { ConfiguredExperimentalFeature } from '../config'\n\nconst debug = setupDebug('next:start-server')\nlet startServerSpan: Span | undefined\n\nexport interface StartServerOptions {\n  dir: string\n  port: number\n  isDev: boolean\n  hostname?: string\n  allowRetry?: boolean\n  customServer?: boolean\n  minimalMode?: boolean\n  keepAliveTimeout?: number\n  // this is dev-server only\n  selfSignedCertificate?: SelfSignedCertificate\n}\n\nexport async function getRequestHandlers({\n  dir,\n  port,\n  isDev,\n  onDevServerCleanup,\n  server,\n  hostname,\n  minimalMode,\n  keepAliveTimeout,\n  experimentalHttpsServer,\n  quiet,\n}: {\n  dir: string\n  port: number\n  isDev: boolean\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  server?: import('http').Server\n  hostname?: string\n  minimalMode?: boolean\n  keepAliveTimeout?: number\n  experimentalHttpsServer?: boolean\n  quiet?: boolean\n}): ReturnType<typeof initialize> {\n  return initialize({\n    dir,\n    port,\n    hostname,\n    onDevServerCleanup,\n    dev: isDev,\n    minimalMode,\n    server,\n    keepAliveTimeout,\n    experimentalHttpsServer,\n    startServerSpan,\n    quiet,\n  })\n}\n\nexport async function startServer(\n  serverOptions: StartServerOptions\n): Promise<void> {\n  const {\n    dir,\n    isDev,\n    hostname,\n    minimalMode,\n    allowRetry,\n    keepAliveTimeout,\n    selfSignedCertificate,\n  } = serverOptions\n  let { port } = serverOptions\n\n  process.title = `next-server (v${process.env.__NEXT_VERSION})`\n  let handlersReady = () => {}\n  let handlersError = () => {}\n\n  let handlersPromise: Promise<void> | undefined = new Promise<void>(\n    (resolve, reject) => {\n      handlersReady = resolve\n      handlersError = reject\n    }\n  )\n  let requestHandler: WorkerRequestHandler = async (\n    req: IncomingMessage,\n    res: ServerResponse\n  ): Promise<void> => {\n    if (handlersPromise) {\n      await handlersPromise\n      return requestHandler(req, res)\n    }\n    throw new Error('Invariant request handler was not setup')\n  }\n  let upgradeHandler: WorkerUpgradeHandler = async (\n    req,\n    socket,\n    head\n  ): Promise<void> => {\n    if (handlersPromise) {\n      await handlersPromise\n      return upgradeHandler(req, socket, head)\n    }\n    throw new Error('Invariant upgrade handler was not setup')\n  }\n\n  let nextServer: NextServer | undefined\n\n  // setup server listener as fast as possible\n  if (selfSignedCertificate && !isDev) {\n    throw new Error(\n      'Using a self signed certificate is only supported with `next dev`.'\n    )\n  }\n\n  async function requestListener(req: IncomingMessage, res: ServerResponse) {\n    try {\n      if (handlersPromise) {\n        await handlersPromise\n        handlersPromise = undefined\n      }\n      await requestHandler(req, res)\n    } catch (err) {\n      res.statusCode = 500\n      res.end('Internal Server Error')\n      Log.error(`Failed to handle request for ${req.url}`)\n      console.error(err)\n    } finally {\n      if (isDev) {\n        if (\n          v8.getHeapStatistics().used_heap_size >\n          0.8 * v8.getHeapStatistics().heap_size_limit\n        ) {\n          Log.warn(\n            `Server is approaching the used memory threshold, restarting...`\n          )\n          trace('server-restart-close-to-memory-threshold', undefined, {\n            'memory.heapSizeLimit': String(\n              v8.getHeapStatistics().heap_size_limit\n            ),\n            'memory.heapUsed': String(v8.getHeapStatistics().used_heap_size),\n          }).stop()\n          await flushAllTraces()\n          process.exit(RESTART_EXIT_CODE)\n        }\n      }\n    }\n  }\n\n  const server = selfSignedCertificate\n    ? https.createServer(\n        {\n          key: fs.readFileSync(selfSignedCertificate.key),\n          cert: fs.readFileSync(selfSignedCertificate.cert),\n        },\n        requestListener\n      )\n    : http.createServer(requestListener)\n\n  if (keepAliveTimeout) {\n    server.keepAliveTimeout = keepAliveTimeout\n  }\n  server.on('upgrade', async (req, socket, head) => {\n    try {\n      await upgradeHandler(req, socket, head)\n    } catch (err) {\n      socket.destroy()\n      Log.error(`Failed to handle request for ${req.url}`)\n      console.error(err)\n    }\n  })\n\n  let portRetryCount = 0\n  const originalPort = port\n\n  server.on('error', (err: NodeJS.ErrnoException) => {\n    if (\n      allowRetry &&\n      port &&\n      isDev &&\n      err.code === 'EADDRINUSE' &&\n      portRetryCount < 10\n    ) {\n      port += 1\n      portRetryCount += 1\n      server.listen(port, hostname)\n    } else {\n      Log.error(`Failed to start server`)\n      console.error(err)\n      process.exit(1)\n    }\n  })\n\n  let cleanupListeners = isDev ? new AsyncCallbackSet() : undefined\n\n  await new Promise<void>((resolve) => {\n    server.on('listening', async () => {\n      const nodeDebugType = getNodeDebugType()\n\n      const addr = server.address()\n      const actualHostname = formatHostname(\n        typeof addr === 'object'\n          ? addr?.address || hostname || 'localhost'\n          : addr\n      )\n      const formattedHostname =\n        !hostname || actualHostname === '0.0.0.0'\n          ? 'localhost'\n          : actualHostname === '[::]'\n            ? '[::1]'\n            : formatHostname(hostname)\n\n      port = typeof addr === 'object' ? addr?.port || port : port\n\n      if (portRetryCount) {\n        Log.warn(\n          `Port ${originalPort} is in use, using available port ${port} instead.`\n        )\n      }\n\n      const networkHostname =\n        hostname ?? getNetworkHost(isIPv6(actualHostname) ? 'IPv6' : 'IPv4')\n\n      const protocol = selfSignedCertificate ? 'https' : 'http'\n\n      const networkUrl = networkHostname\n        ? `${protocol}://${formatHostname(networkHostname)}:${port}`\n        : null\n\n      const appUrl = `${protocol}://${formattedHostname}:${port}`\n\n      if (nodeDebugType) {\n        const formattedDebugAddress = getFormattedDebugAddress()\n        Log.info(\n          `the --${nodeDebugType} option was detected, the Next.js router server should be inspected at ${formattedDebugAddress}.`\n        )\n      }\n\n      // Store the selected port to:\n      // - expose it to render workers\n      // - re-use it for automatic dev server restarts with a randomly selected port\n      process.env.PORT = port + ''\n\n      process.env.__NEXT_PRIVATE_ORIGIN = appUrl\n\n      // Only load env and config in dev to for logging purposes\n      let envInfo: string[] | undefined\n      let experimentalFeatures: ConfiguredExperimentalFeature[] | undefined\n      if (isDev) {\n        const startServerInfo = await getStartServerInfo(dir, isDev)\n        envInfo = startServerInfo.envInfo\n        experimentalFeatures = startServerInfo.experimentalFeatures\n      }\n      logStartInfo({\n        networkUrl,\n        appUrl,\n        envInfo,\n        experimentalFeatures,\n        maxExperimentalFeatures: 3,\n      })\n\n      Log.event(`Starting...`)\n\n      try {\n        let cleanupStarted = false\n        let closeUpgraded: (() => void) | null = null\n        const cleanup = () => {\n          if (cleanupStarted) {\n            // We can get duplicate signals, e.g. when `ctrl+c` is used in an\n            // interactive shell (i.e. bash, zsh), the shell will recursively\n            // send SIGINT to children. The parent `next-dev` process will also\n            // send us SIGINT.\n            return\n          }\n          cleanupStarted = true\n          ;(async () => {\n            debug('start-server process cleanup')\n\n            // first, stop accepting new connections and finish pending requests,\n            // because they might affect `nextServer.close()` (e.g. by scheduling an `after`)\n            await new Promise<void>((res) => {\n              server.close((err) => {\n                if (err) console.error(err)\n                res()\n              })\n              if (isDev) {\n                server.closeAllConnections()\n                closeUpgraded?.()\n              }\n            })\n\n            // now that no new requests can come in, clean up the rest\n            await Promise.all([\n              nextServer?.close().catch(console.error),\n              cleanupListeners?.runAll().catch(console.error),\n            ])\n\n            debug('start-server process cleanup finished')\n            process.exit(0)\n          })()\n        }\n        const exception = (err: Error) => {\n          if (isPostpone(err)) {\n            // React postpones that are unhandled might end up logged here but they're\n            // not really errors. They're just part of rendering.\n            return\n          }\n\n          // This is the render worker, we keep the process alive\n          console.error(err)\n        }\n        // Make sure commands gracefully respect termination signals (e.g. from Docker)\n        // Allow the graceful termination to be manually configurable\n        if (!process.env.NEXT_MANUAL_SIG_HANDLE) {\n          process.on('SIGINT', cleanup)\n          process.on('SIGTERM', cleanup)\n        }\n        process.on('rejectionHandled', () => {\n          // It is ok to await a Promise late in Next.js as it allows for better\n          // prefetching patterns to avoid waterfalls. We ignore loggining these.\n          // We should've already errored in anyway unhandledRejection.\n        })\n        process.on('uncaughtException', exception)\n        process.on('unhandledRejection', exception)\n\n        const initResult = await getRequestHandlers({\n          dir,\n          port,\n          isDev,\n          onDevServerCleanup: cleanupListeners\n            ? cleanupListeners.add.bind(cleanupListeners)\n            : undefined,\n          server,\n          hostname,\n          minimalMode,\n          keepAliveTimeout,\n          experimentalHttpsServer: !!selfSignedCertificate,\n        })\n        requestHandler = initResult.requestHandler\n        upgradeHandler = initResult.upgradeHandler\n        nextServer = initResult.server\n        closeUpgraded = initResult.closeUpgraded\n\n        const startServerProcessDuration =\n          performance.mark('next-start-end') &&\n          performance.measure(\n            'next-start-duration',\n            'next-start',\n            'next-start-end'\n          ).duration\n\n        handlersReady()\n        const formatDurationText =\n          startServerProcessDuration > 2000\n            ? `${Math.round(startServerProcessDuration / 100) / 10}s`\n            : `${Math.round(startServerProcessDuration)}ms`\n\n        Log.event(`Ready in ${formatDurationText}`)\n\n        if (process.env.TURBOPACK) {\n          await validateTurboNextConfig({\n            dir: serverOptions.dir,\n            isDev: true,\n          })\n        }\n      } catch (err) {\n        // fatal error if we can't setup\n        handlersError()\n        console.error(err)\n        process.exit(1)\n      }\n\n      resolve()\n    })\n    server.listen(port, hostname)\n  })\n\n  if (isDev) {\n    function watchConfigFiles(\n      dirToWatch: string,\n      onChange: (filename: string) => void\n    ) {\n      const wp = new Watchpack()\n      wp.watch({\n        files: CONFIG_FILES.map((file) => path.join(dirToWatch, file)),\n      })\n      wp.on('change', onChange)\n    }\n    watchConfigFiles(dir, async (filename) => {\n      if (process.env.__NEXT_DISABLE_MEMORY_WATCHER) {\n        Log.info(\n          `Detected change, manual restart required due to '__NEXT_DISABLE_MEMORY_WATCHER' usage`\n        )\n        return\n      }\n\n      Log.warn(\n        `Found a change in ${path.basename(\n          filename\n        )}. Restarting the server to apply the changes...`\n      )\n      process.exit(RESTART_EXIT_CODE)\n    })\n  }\n}\n\nif (process.env.NEXT_PRIVATE_WORKER && process.send) {\n  process.addListener('message', async (msg: any) => {\n    if (\n      msg &&\n      typeof msg === 'object' &&\n      msg.nextWorkerOptions &&\n      process.send\n    ) {\n      startServerSpan = trace('start-dev-server', undefined, {\n        cpus: String(os.cpus().length),\n        platform: os.platform(),\n        'memory.freeMem': String(os.freemem()),\n        'memory.totalMem': String(os.totalmem()),\n        'memory.heapSizeLimit': String(v8.getHeapStatistics().heap_size_limit),\n      })\n      await startServerSpan.traceAsyncFn(() =>\n        startServer(msg.nextWorkerOptions)\n      )\n      const memoryUsage = process.memoryUsage()\n      startServerSpan.setAttribute('memory.rss', String(memoryUsage.rss))\n      startServerSpan.setAttribute(\n        'memory.heapTotal',\n        String(memoryUsage.heapTotal)\n      )\n      startServerSpan.setAttribute(\n        'memory.heapUsed',\n        String(memoryUsage.heapUsed)\n      )\n      process.send({ nextServerReady: true, port: process.env.PORT })\n    }\n  })\n  process.send({ nextWorkerReady: true })\n}\n"], "names": ["getNetworkHost", "performance", "getEntriesByName", "length", "mark", "fs", "v8", "path", "http", "https", "os", "Watchpack", "Log", "setupDebug", "RESTART_EXIT_CODE", "getFormattedDebugAddress", "getNodeDebugType", "formatHostname", "initialize", "CONFIG_FILES", "getStartServerInfo", "logStartInfo", "validateTurboNextConfig", "trace", "flushAllTraces", "isPostpone", "isIPv6", "AsyncCallbackSet", "debug", "startServerSpan", "getRequestHandlers", "dir", "port", "isDev", "onDevServerCleanup", "server", "hostname", "minimalMode", "keepAliveTimeout", "experimentalHttpsServer", "quiet", "dev", "startServer", "serverOptions", "allowRetry", "selfSignedCertificate", "process", "title", "env", "__NEXT_VERSION", "handlersReady", "handlersError", "handlersPromise", "Promise", "resolve", "reject", "requestHandler", "req", "res", "Error", "upgradeHandler", "socket", "head", "nextServer", "requestListener", "undefined", "err", "statusCode", "end", "error", "url", "console", "getHeapStatistics", "used_heap_size", "heap_size_limit", "warn", "String", "stop", "exit", "createServer", "key", "readFileSync", "cert", "on", "destroy", "portRetryCount", "originalPort", "code", "listen", "cleanupListeners", "nodeDebugType", "addr", "address", "actualHostname", "formattedHostname", "networkHostname", "protocol", "networkUrl", "appUrl", "formattedDebugAddress", "info", "PORT", "__NEXT_PRIVATE_ORIGIN", "envInfo", "experimentalFeatures", "startServerInfo", "maxExperimentalFeatures", "event", "cleanupStarted", "closeUpgraded", "cleanup", "close", "closeAllConnections", "all", "catch", "runAll", "exception", "NEXT_MANUAL_SIG_HANDLE", "initResult", "add", "bind", "startServerProcessDuration", "measure", "duration", "formatDurationText", "Math", "round", "TURBOPACK", "watchConfigFiles", "dirToWatch", "onChange", "wp", "watch", "files", "map", "file", "join", "filename", "__NEXT_DISABLE_MEMORY_WATCHER", "basename", "NEXT_PRIVATE_WORKER", "send", "addListener", "msg", "nextWorkerOptions", "cpus", "platform", "freemem", "totalmem", "traceAsyncFn", "memoryUsage", "setAttribute", "rss", "heapTotal", "heapUsed", "nextServerReady", "nextWorkerReady"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA4B;AAE3D,IAAIC,YAAYC,gBAAgB,CAAC,cAAcC,MAAM,KAAK,GAAG;IAC3DF,YAAYG,IAAI,CAAC;AACnB;AACA,OAAO,UAAS;AAChB,OAAO,kBAAiB;AAMxB,OAAOC,QAAQ,KAAI;AACnB,OAAOC,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,OAAOC,UAAU,OAAM;AACvB,OAAOC,WAAW,QAAO;AACzB,OAAOC,QAAQ,KAAI;AACnB,OAAOC,eAAe,+BAA8B;AACpD,YAAYC,SAAS,yBAAwB;AAC7C,OAAOC,gBAAgB,2BAA0B;AACjD,SACEC,iBAAiB,EACjBC,wBAAwB,EACxBC,gBAAgB,QACX,UAAS;AAChB,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,UAAU,QAAQ,kBAAiB;AAC5C,SAASC,YAAY,QAAQ,6BAA4B;AACzD,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,iBAAgB;AACjE,SAASC,uBAAuB,QAAQ,8BAA6B;AACrE,SAAoBC,KAAK,EAAEC,cAAc,QAAQ,cAAa;AAC9D,SAASC,UAAU,QAAQ,6BAA4B;AACvD,SAASC,MAAM,QAAQ,YAAW;AAClC,SAASC,gBAAgB,QAAQ,uBAAsB;AAIvD,MAAMC,QAAQf,WAAW;AACzB,IAAIgB;AAeJ,OAAO,eAAeC,mBAAmB,EACvCC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,kBAAkB,EAClBC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,KAAK,EAYN;IACC,OAAOtB,WAAW;QAChBa;QACAC;QACAI;QACAF;QACAO,KAAKR;QACLI;QACAF;QACAG;QACAC;QACAV;QACAW;IACF;AACF;AAEA,OAAO,eAAeE,YACpBC,aAAiC;IAEjC,MAAM,EACJZ,GAAG,EACHE,KAAK,EACLG,QAAQ,EACRC,WAAW,EACXO,UAAU,EACVN,gBAAgB,EAChBO,qBAAqB,EACtB,GAAGF;IACJ,IAAI,EAAEX,IAAI,EAAE,GAAGW;IAEfG,QAAQC,KAAK,GAAG,CAAC,cAAc,EAAED,QAAQE,GAAG,CAACC,cAAc,CAAC,CAAC,CAAC;IAC9D,IAAIC,gBAAgB,KAAO;IAC3B,IAAIC,gBAAgB,KAAO;IAE3B,IAAIC,kBAA6C,IAAIC,QACnD,CAACC,SAASC;QACRL,gBAAgBI;QAChBH,gBAAgBI;IAClB;IAEF,IAAIC,iBAAuC,OACzCC,KACAC;QAEA,IAAIN,iBAAiB;YACnB,MAAMA;YACN,OAAOI,eAAeC,KAAKC;QAC7B;QACA,MAAM,qBAAoD,CAApD,IAAIC,MAAM,4CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;IACA,IAAIC,iBAAuC,OACzCH,KACAI,QACAC;QAEA,IAAIV,iBAAiB;YACnB,MAAMA;YACN,OAAOQ,eAAeH,KAAKI,QAAQC;QACrC;QACA,MAAM,qBAAoD,CAApD,IAAIH,MAAM,4CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;IAEA,IAAII;IAEJ,4CAA4C;IAC5C,IAAIlB,yBAAyB,CAACZ,OAAO;QACnC,MAAM,qBAEL,CAFK,IAAI0B,MACR,uEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,eAAeK,gBAAgBP,GAAoB,EAAEC,GAAmB;QACtE,IAAI;YACF,IAAIN,iBAAiB;gBACnB,MAAMA;gBACNA,kBAAkBa;YACpB;YACA,MAAMT,eAAeC,KAAKC;QAC5B,EAAE,OAAOQ,KAAK;YACZR,IAAIS,UAAU,GAAG;YACjBT,IAAIU,GAAG,CAAC;YACRxD,IAAIyD,KAAK,CAAC,CAAC,6BAA6B,EAAEZ,IAAIa,GAAG,EAAE;YACnDC,QAAQF,KAAK,CAACH;QAChB,SAAU;YACR,IAAIjC,OAAO;gBACT,IACE3B,GAAGkE,iBAAiB,GAAGC,cAAc,GACrC,MAAMnE,GAAGkE,iBAAiB,GAAGE,eAAe,EAC5C;oBACA9D,IAAI+D,IAAI,CACN,CAAC,8DAA8D,CAAC;oBAElEpD,MAAM,4CAA4C0C,WAAW;wBAC3D,wBAAwBW,OACtBtE,GAAGkE,iBAAiB,GAAGE,eAAe;wBAExC,mBAAmBE,OAAOtE,GAAGkE,iBAAiB,GAAGC,cAAc;oBACjE,GAAGI,IAAI;oBACP,MAAMrD;oBACNsB,QAAQgC,IAAI,CAAChE;gBACf;YACF;QACF;IACF;IAEA,MAAMqB,SAASU,wBACXpC,MAAMsE,YAAY,CAChB;QACEC,KAAK3E,GAAG4E,YAAY,CAACpC,sBAAsBmC,GAAG;QAC9CE,MAAM7E,GAAG4E,YAAY,CAACpC,sBAAsBqC,IAAI;IAClD,GACAlB,mBAEFxD,KAAKuE,YAAY,CAACf;IAEtB,IAAI1B,kBAAkB;QACpBH,OAAOG,gBAAgB,GAAGA;IAC5B;IACAH,OAAOgD,EAAE,CAAC,WAAW,OAAO1B,KAAKI,QAAQC;QACvC,IAAI;YACF,MAAMF,eAAeH,KAAKI,QAAQC;QACpC,EAAE,OAAOI,KAAK;YACZL,OAAOuB,OAAO;YACdxE,IAAIyD,KAAK,CAAC,CAAC,6BAA6B,EAAEZ,IAAIa,GAAG,EAAE;YACnDC,QAAQF,KAAK,CAACH;QAChB;IACF;IAEA,IAAImB,iBAAiB;IACrB,MAAMC,eAAetD;IAErBG,OAAOgD,EAAE,CAAC,SAAS,CAACjB;QAClB,IACEtB,cACAZ,QACAC,SACAiC,IAAIqB,IAAI,KAAK,gBACbF,iBAAiB,IACjB;YACArD,QAAQ;YACRqD,kBAAkB;YAClBlD,OAAOqD,MAAM,CAACxD,MAAMI;QACtB,OAAO;YACLxB,IAAIyD,KAAK,CAAC,CAAC,sBAAsB,CAAC;YAClCE,QAAQF,KAAK,CAACH;YACdpB,QAAQgC,IAAI,CAAC;QACf;IACF;IAEA,IAAIW,mBAAmBxD,QAAQ,IAAIN,qBAAqBsC;IAExD,MAAM,IAAIZ,QAAc,CAACC;QACvBnB,OAAOgD,EAAE,CAAC,aAAa;YACrB,MAAMO,gBAAgB1E;YAEtB,MAAM2E,OAAOxD,OAAOyD,OAAO;YAC3B,MAAMC,iBAAiB5E,eACrB,OAAO0E,SAAS,WACZA,CAAAA,wBAAAA,KAAMC,OAAO,KAAIxD,YAAY,cAC7BuD;YAEN,MAAMG,oBACJ,CAAC1D,YAAYyD,mBAAmB,YAC5B,cACAA,mBAAmB,SACjB,UACA5E,eAAemB;YAEvBJ,OAAO,OAAO2D,SAAS,WAAWA,CAAAA,wBAAAA,KAAM3D,IAAI,KAAIA,OAAOA;YAEvD,IAAIqD,gBAAgB;gBAClBzE,IAAI+D,IAAI,CACN,CAAC,KAAK,EAAEW,aAAa,iCAAiC,EAAEtD,KAAK,SAAS,CAAC;YAE3E;YAEA,MAAM+D,kBACJ3D,YAAYpC,eAAe0B,OAAOmE,kBAAkB,SAAS;YAE/D,MAAMG,WAAWnD,wBAAwB,UAAU;YAEnD,MAAMoD,aAAaF,kBACf,GAAGC,SAAS,GAAG,EAAE/E,eAAe8E,iBAAiB,CAAC,EAAE/D,MAAM,GAC1D;YAEJ,MAAMkE,SAAS,GAAGF,SAAS,GAAG,EAAEF,kBAAkB,CAAC,EAAE9D,MAAM;YAE3D,IAAI0D,eAAe;gBACjB,MAAMS,wBAAwBpF;gBAC9BH,IAAIwF,IAAI,CACN,CAAC,MAAM,EAAEV,cAAc,uEAAuE,EAAES,sBAAsB,CAAC,CAAC;YAE5H;YAEA,8BAA8B;YAC9B,gCAAgC;YAChC,8EAA8E;YAC9ErD,QAAQE,GAAG,CAACqD,IAAI,GAAGrE,OAAO;YAE1Bc,QAAQE,GAAG,CAACsD,qBAAqB,GAAGJ;YAEpC,0DAA0D;YAC1D,IAAIK;YACJ,IAAIC;YACJ,IAAIvE,OAAO;gBACT,MAAMwE,kBAAkB,MAAMrF,mBAAmBW,KAAKE;gBACtDsE,UAAUE,gBAAgBF,OAAO;gBACjCC,uBAAuBC,gBAAgBD,oBAAoB;YAC7D;YACAnF,aAAa;gBACX4E;gBACAC;gBACAK;gBACAC;gBACAE,yBAAyB;YAC3B;YAEA9F,IAAI+F,KAAK,CAAC,CAAC,WAAW,CAAC;YAEvB,IAAI;gBACF,IAAIC,iBAAiB;gBACrB,IAAIC,gBAAqC;gBACzC,MAAMC,UAAU;oBACd,IAAIF,gBAAgB;wBAClB,iEAAiE;wBACjE,iEAAiE;wBACjE,mEAAmE;wBACnE,kBAAkB;wBAClB;oBACF;oBACAA,iBAAiB;oBACf,CAAA;wBACAhF,MAAM;wBAEN,qEAAqE;wBACrE,iFAAiF;wBACjF,MAAM,IAAIyB,QAAc,CAACK;4BACvBvB,OAAO4E,KAAK,CAAC,CAAC7C;gCACZ,IAAIA,KAAKK,QAAQF,KAAK,CAACH;gCACvBR;4BACF;4BACA,IAAIzB,OAAO;gCACTE,OAAO6E,mBAAmB;gCAC1BH,iCAAAA;4BACF;wBACF;wBAEA,0DAA0D;wBAC1D,MAAMxD,QAAQ4D,GAAG,CAAC;4BAChBlD,8BAAAA,WAAYgD,KAAK,GAAGG,KAAK,CAAC3C,QAAQF,KAAK;4BACvCoB,oCAAAA,iBAAkB0B,MAAM,GAAGD,KAAK,CAAC3C,QAAQF,KAAK;yBAC/C;wBAEDzC,MAAM;wBACNkB,QAAQgC,IAAI,CAAC;oBACf,CAAA;gBACF;gBACA,MAAMsC,YAAY,CAAClD;oBACjB,IAAIzC,WAAWyC,MAAM;wBACnB,0EAA0E;wBAC1E,qDAAqD;wBACrD;oBACF;oBAEA,uDAAuD;oBACvDK,QAAQF,KAAK,CAACH;gBAChB;gBACA,+EAA+E;gBAC/E,6DAA6D;gBAC7D,IAAI,CAACpB,QAAQE,GAAG,CAACqE,sBAAsB,EAAE;oBACvCvE,QAAQqC,EAAE,CAAC,UAAU2B;oBACrBhE,QAAQqC,EAAE,CAAC,WAAW2B;gBACxB;gBACAhE,QAAQqC,EAAE,CAAC,oBAAoB;gBAC7B,sEAAsE;gBACtE,uEAAuE;gBACvE,6DAA6D;gBAC/D;gBACArC,QAAQqC,EAAE,CAAC,qBAAqBiC;gBAChCtE,QAAQqC,EAAE,CAAC,sBAAsBiC;gBAEjC,MAAME,aAAa,MAAMxF,mBAAmB;oBAC1CC;oBACAC;oBACAC;oBACAC,oBAAoBuD,mBAChBA,iBAAiB8B,GAAG,CAACC,IAAI,CAAC/B,oBAC1BxB;oBACJ9B;oBACAC;oBACAC;oBACAC;oBACAC,yBAAyB,CAAC,CAACM;gBAC7B;gBACAW,iBAAiB8D,WAAW9D,cAAc;gBAC1CI,iBAAiB0D,WAAW1D,cAAc;gBAC1CG,aAAauD,WAAWnF,MAAM;gBAC9B0E,gBAAgBS,WAAWT,aAAa;gBAExC,MAAMY,6BACJxH,YAAYG,IAAI,CAAC,qBACjBH,YAAYyH,OAAO,CACjB,uBACA,cACA,kBACAC,QAAQ;gBAEZzE;gBACA,MAAM0E,qBACJH,6BAA6B,OACzB,GAAGI,KAAKC,KAAK,CAACL,6BAA6B,OAAO,GAAG,CAAC,CAAC,GACvD,GAAGI,KAAKC,KAAK,CAACL,4BAA4B,EAAE,CAAC;gBAEnD7G,IAAI+F,KAAK,CAAC,CAAC,SAAS,EAAEiB,oBAAoB;gBAE1C,IAAI9E,QAAQE,GAAG,CAAC+E,SAAS,EAAE;oBACzB,MAAMzG,wBAAwB;wBAC5BS,KAAKY,cAAcZ,GAAG;wBACtBE,OAAO;oBACT;gBACF;YACF,EAAE,OAAOiC,KAAK;gBACZ,gCAAgC;gBAChCf;gBACAoB,QAAQF,KAAK,CAACH;gBACdpB,QAAQgC,IAAI,CAAC;YACf;YAEAxB;QACF;QACAnB,OAAOqD,MAAM,CAACxD,MAAMI;IACtB;IAEA,IAAIH,OAAO;QACT,SAAS+F,iBACPC,UAAkB,EAClBC,QAAoC;YAEpC,MAAMC,KAAK,IAAIxH;YACfwH,GAAGC,KAAK,CAAC;gBACPC,OAAOlH,aAAamH,GAAG,CAAC,CAACC,OAAShI,KAAKiI,IAAI,CAACP,YAAYM;YAC1D;YACAJ,GAAGhD,EAAE,CAAC,UAAU+C;QAClB;QACAF,iBAAiBjG,KAAK,OAAO0G;YAC3B,IAAI3F,QAAQE,GAAG,CAAC0F,6BAA6B,EAAE;gBAC7C9H,IAAIwF,IAAI,CACN,CAAC,qFAAqF,CAAC;gBAEzF;YACF;YAEAxF,IAAI+D,IAAI,CACN,CAAC,kBAAkB,EAAEpE,KAAKoI,QAAQ,CAChCF,UACA,+CAA+C,CAAC;YAEpD3F,QAAQgC,IAAI,CAAChE;QACf;IACF;AACF;AAEA,IAAIgC,QAAQE,GAAG,CAAC4F,mBAAmB,IAAI9F,QAAQ+F,IAAI,EAAE;IACnD/F,QAAQgG,WAAW,CAAC,WAAW,OAAOC;QACpC,IACEA,OACA,OAAOA,QAAQ,YACfA,IAAIC,iBAAiB,IACrBlG,QAAQ+F,IAAI,EACZ;YACAhH,kBAAkBN,MAAM,oBAAoB0C,WAAW;gBACrDgF,MAAMrE,OAAOlE,GAAGuI,IAAI,GAAG9I,MAAM;gBAC7B+I,UAAUxI,GAAGwI,QAAQ;gBACrB,kBAAkBtE,OAAOlE,GAAGyI,OAAO;gBACnC,mBAAmBvE,OAAOlE,GAAG0I,QAAQ;gBACrC,wBAAwBxE,OAAOtE,GAAGkE,iBAAiB,GAAGE,eAAe;YACvE;YACA,MAAM7C,gBAAgBwH,YAAY,CAAC,IACjC3G,YAAYqG,IAAIC,iBAAiB;YAEnC,MAAMM,cAAcxG,QAAQwG,WAAW;YACvCzH,gBAAgB0H,YAAY,CAAC,cAAc3E,OAAO0E,YAAYE,GAAG;YACjE3H,gBAAgB0H,YAAY,CAC1B,oBACA3E,OAAO0E,YAAYG,SAAS;YAE9B5H,gBAAgB0H,YAAY,CAC1B,mBACA3E,OAAO0E,YAAYI,QAAQ;YAE7B5G,QAAQ+F,IAAI,CAAC;gBAAEc,iBAAiB;gBAAM3H,MAAMc,QAAQE,GAAG,CAACqD,IAAI;YAAC;QAC/D;IACF;IACAvD,QAAQ+F,IAAI,CAAC;QAAEe,iBAAiB;IAAK;AACvC"}