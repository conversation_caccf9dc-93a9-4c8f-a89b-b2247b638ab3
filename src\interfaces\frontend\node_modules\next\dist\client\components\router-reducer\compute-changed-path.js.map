{"version": 3, "sources": ["../../../../src/client/components/router-reducer/compute-changed-path.ts"], "sourcesContent": ["import type {\n  FlightRouterState,\n  Segment,\n} from '../../../server/app-render/types'\nimport { INTERCEPTION_ROUTE_MARKERS } from '../../../shared/lib/router/utils/interception-routes'\nimport type { Params } from '../../../server/request/params'\nimport {\n  isGroupSegment,\n  DEFAULT_SEGMENT_KEY,\n  PAGE_SEGMENT_KEY,\n} from '../../../shared/lib/segment'\nimport { matchSegment } from '../match-segments'\n\nconst removeLeadingSlash = (segment: string): string => {\n  return segment[0] === '/' ? segment.slice(1) : segment\n}\n\nconst segmentToPathname = (segment: Segment): string => {\n  if (typeof segment === 'string') {\n    // 'children' is not a valid path -- it's technically a parallel route that corresponds with the current segment's page\n    // if we don't skip it, then the computed pathname might be something like `/children` which doesn't make sense.\n    if (segment === 'children') return ''\n\n    return segment\n  }\n\n  return segment[1]\n}\n\nfunction normalizeSegments(segments: string[]): string {\n  return (\n    segments.reduce((acc, segment) => {\n      segment = removeLeadingSlash(segment)\n      if (segment === '' || isGroupSegment(segment)) {\n        return acc\n      }\n\n      return `${acc}/${segment}`\n    }, '') || '/'\n  )\n}\n\nexport function extractPathFromFlightRouterState(\n  flightRouterState: FlightRouterState\n): string | undefined {\n  const segment = Array.isArray(flightRouterState[0])\n    ? flightRouterState[0][1]\n    : flightRouterState[0]\n\n  if (\n    segment === DEFAULT_SEGMENT_KEY ||\n    INTERCEPTION_ROUTE_MARKERS.some((m) => segment.startsWith(m))\n  )\n    return undefined\n\n  if (segment.startsWith(PAGE_SEGMENT_KEY)) return ''\n\n  const segments = [segmentToPathname(segment)]\n  const parallelRoutes = flightRouterState[1] ?? {}\n\n  const childrenPath = parallelRoutes.children\n    ? extractPathFromFlightRouterState(parallelRoutes.children)\n    : undefined\n\n  if (childrenPath !== undefined) {\n    segments.push(childrenPath)\n  } else {\n    for (const [key, value] of Object.entries(parallelRoutes)) {\n      if (key === 'children') continue\n\n      const childPath = extractPathFromFlightRouterState(value)\n\n      if (childPath !== undefined) {\n        segments.push(childPath)\n      }\n    }\n  }\n\n  return normalizeSegments(segments)\n}\n\nfunction computeChangedPathImpl(\n  treeA: FlightRouterState,\n  treeB: FlightRouterState\n): string | null {\n  const [segmentA, parallelRoutesA] = treeA\n  const [segmentB, parallelRoutesB] = treeB\n\n  const normalizedSegmentA = segmentToPathname(segmentA)\n  const normalizedSegmentB = segmentToPathname(segmentB)\n\n  if (\n    INTERCEPTION_ROUTE_MARKERS.some(\n      (m) =>\n        normalizedSegmentA.startsWith(m) || normalizedSegmentB.startsWith(m)\n    )\n  ) {\n    return ''\n  }\n\n  if (!matchSegment(segmentA, segmentB)) {\n    // once we find where the tree changed, we compute the rest of the path by traversing the tree\n    return extractPathFromFlightRouterState(treeB) ?? ''\n  }\n\n  for (const parallelRouterKey in parallelRoutesA) {\n    if (parallelRoutesB[parallelRouterKey]) {\n      const changedPath = computeChangedPathImpl(\n        parallelRoutesA[parallelRouterKey],\n        parallelRoutesB[parallelRouterKey]\n      )\n      if (changedPath !== null) {\n        return `${segmentToPathname(segmentB)}/${changedPath}`\n      }\n    }\n  }\n\n  return null\n}\n\nexport function computeChangedPath(\n  treeA: FlightRouterState,\n  treeB: FlightRouterState\n): string | null {\n  const changedPath = computeChangedPathImpl(treeA, treeB)\n\n  if (changedPath == null || changedPath === '/') {\n    return changedPath\n  }\n\n  // lightweight normalization to remove route groups\n  return normalizeSegments(changedPath.split('/'))\n}\n\n/**\n * Recursively extracts dynamic parameters from FlightRouterState.\n */\nexport function getSelectedParams(\n  currentTree: FlightRouterState,\n  params: Params = {}\n): Params {\n  const parallelRoutes = currentTree[1]\n\n  for (const parallelRoute of Object.values(parallelRoutes)) {\n    const segment = parallelRoute[0]\n    const isDynamicParameter = Array.isArray(segment)\n    const segmentValue = isDynamicParameter ? segment[1] : segment\n    if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) continue\n\n    // Ensure catchAll and optional catchall are turned into an array\n    const isCatchAll =\n      isDynamicParameter && (segment[2] === 'c' || segment[2] === 'oc')\n\n    if (isCatchAll) {\n      params[segment[0]] = segment[1].split('/')\n    } else if (isDynamicParameter) {\n      params[segment[0]] = segment[1]\n    }\n\n    params = getSelectedParams(parallelRoute, params)\n  }\n\n  return params\n}\n"], "names": ["computeChangedPath", "extractPathFromFlightRouterState", "getSelectedParams", "removeLeadingSlash", "segment", "slice", "segmentToPathname", "normalizeSegments", "segments", "reduce", "acc", "isGroupSegment", "flightRouterState", "Array", "isArray", "DEFAULT_SEGMENT_KEY", "INTERCEPTION_ROUTE_MARKERS", "some", "m", "startsWith", "undefined", "PAGE_SEGMENT_KEY", "parallelRoutes", "<PERSON><PERSON><PERSON>", "children", "push", "key", "value", "Object", "entries", "child<PERSON><PERSON>", "computeChangedPathImpl", "treeA", "treeB", "segmentA", "parallelRoutesA", "segmentB", "parallelRoutesB", "normalizedSegmentA", "normalizedSegmentB", "matchSegment", "parallel<PERSON><PERSON>er<PERSON>ey", "changedPath", "split", "currentTree", "params", "parallelRoute", "values", "isDynamicParameter", "segmentValue", "isCatchAll"], "mappings": ";;;;;;;;;;;;;;;;IAwHgBA,kBAAkB;eAAlBA;;IA9EAC,gCAAgC;eAAhCA;;IA+FAC,iBAAiB;eAAjBA;;;oCArI2B;yBAMpC;+BACsB;AAE7B,MAAMC,qBAAqB,CAACC;IAC1B,OAAOA,OAAO,CAAC,EAAE,KAAK,MAAMA,QAAQC,KAAK,CAAC,KAAKD;AACjD;AAEA,MAAME,oBAAoB,CAACF;IACzB,IAAI,OAAOA,YAAY,UAAU;QAC/B,uHAAuH;QACvH,gHAAgH;QAChH,IAAIA,YAAY,YAAY,OAAO;QAEnC,OAAOA;IACT;IAEA,OAAOA,OAAO,CAAC,EAAE;AACnB;AAEA,SAASG,kBAAkBC,QAAkB;IAC3C,OACEA,SAASC,MAAM,CAAC,CAACC,KAAKN;QACpBA,UAAUD,mBAAmBC;QAC7B,IAAIA,YAAY,MAAMO,IAAAA,uBAAc,EAACP,UAAU;YAC7C,OAAOM;QACT;QAEA,OAAO,AAAGA,MAAI,MAAGN;IACnB,GAAG,OAAO;AAEd;AAEO,SAASH,iCACdW,iBAAoC;IAEpC,MAAMR,UAAUS,MAAMC,OAAO,CAACF,iBAAiB,CAAC,EAAE,IAC9CA,iBAAiB,CAAC,EAAE,CAAC,EAAE,GACvBA,iBAAiB,CAAC,EAAE;IAExB,IACER,YAAYW,4BAAmB,IAC/BC,8CAA0B,CAACC,IAAI,CAAC,CAACC,IAAMd,QAAQe,UAAU,CAACD,KAE1D,OAAOE;IAET,IAAIhB,QAAQe,UAAU,CAACE,yBAAgB,GAAG,OAAO;IAEjD,MAAMb,WAAW;QAACF,kBAAkBF;KAAS;QACtBQ;IAAvB,MAAMU,iBAAiBV,CAAAA,sBAAAA,iBAAiB,CAAC,EAAE,YAApBA,sBAAwB,CAAC;IAEhD,MAAMW,eAAeD,eAAeE,QAAQ,GACxCvB,iCAAiCqB,eAAeE,QAAQ,IACxDJ;IAEJ,IAAIG,iBAAiBH,WAAW;QAC9BZ,SAASiB,IAAI,CAACF;IAChB,OAAO;QACL,KAAK,MAAM,CAACG,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACP,gBAAiB;YACzD,IAAII,QAAQ,YAAY;YAExB,MAAMI,YAAY7B,iCAAiC0B;YAEnD,IAAIG,cAAcV,WAAW;gBAC3BZ,SAASiB,IAAI,CAACK;YAChB;QACF;IACF;IAEA,OAAOvB,kBAAkBC;AAC3B;AAEA,SAASuB,uBACPC,KAAwB,EACxBC,KAAwB;IAExB,MAAM,CAACC,UAAUC,gBAAgB,GAAGH;IACpC,MAAM,CAACI,UAAUC,gBAAgB,GAAGJ;IAEpC,MAAMK,qBAAqBhC,kBAAkB4B;IAC7C,MAAMK,qBAAqBjC,kBAAkB8B;IAE7C,IACEpB,8CAA0B,CAACC,IAAI,CAC7B,CAACC,IACCoB,mBAAmBnB,UAAU,CAACD,MAAMqB,mBAAmBpB,UAAU,CAACD,KAEtE;QACA,OAAO;IACT;IAEA,IAAI,CAACsB,IAAAA,2BAAY,EAACN,UAAUE,WAAW;YAE9BnC;QADP,8FAA8F;QAC9F,OAAOA,CAAAA,oCAAAA,iCAAiCgC,kBAAjChC,oCAA2C;IACpD;IAEA,IAAK,MAAMwC,qBAAqBN,gBAAiB;QAC/C,IAAIE,eAAe,CAACI,kBAAkB,EAAE;YACtC,MAAMC,cAAcX,uBAClBI,eAAe,CAACM,kBAAkB,EAClCJ,eAAe,CAACI,kBAAkB;YAEpC,IAAIC,gBAAgB,MAAM;gBACxB,OAAO,AAAGpC,kBAAkB8B,YAAU,MAAGM;YAC3C;QACF;IACF;IAEA,OAAO;AACT;AAEO,SAAS1C,mBACdgC,KAAwB,EACxBC,KAAwB;IAExB,MAAMS,cAAcX,uBAAuBC,OAAOC;IAElD,IAAIS,eAAe,QAAQA,gBAAgB,KAAK;QAC9C,OAAOA;IACT;IAEA,mDAAmD;IACnD,OAAOnC,kBAAkBmC,YAAYC,KAAK,CAAC;AAC7C;AAKO,SAASzC,kBACd0C,WAA8B,EAC9BC,MAAmB;IAAnBA,IAAAA,mBAAAA,SAAiB,CAAC;IAElB,MAAMvB,iBAAiBsB,WAAW,CAAC,EAAE;IAErC,KAAK,MAAME,iBAAiBlB,OAAOmB,MAAM,CAACzB,gBAAiB;QACzD,MAAMlB,UAAU0C,aAAa,CAAC,EAAE;QAChC,MAAME,qBAAqBnC,MAAMC,OAAO,CAACV;QACzC,MAAM6C,eAAeD,qBAAqB5C,OAAO,CAAC,EAAE,GAAGA;QACvD,IAAI,CAAC6C,gBAAgBA,aAAa9B,UAAU,CAACE,yBAAgB,GAAG;QAEhE,iEAAiE;QACjE,MAAM6B,aACJF,sBAAuB5C,CAAAA,OAAO,CAAC,EAAE,KAAK,OAAOA,OAAO,CAAC,EAAE,KAAK,IAAG;QAEjE,IAAI8C,YAAY;YACdL,MAAM,CAACzC,OAAO,CAAC,EAAE,CAAC,GAAGA,OAAO,CAAC,EAAE,CAACuC,KAAK,CAAC;QACxC,OAAO,IAAIK,oBAAoB;YAC7BH,MAAM,CAACzC,OAAO,CAAC,EAAE,CAAC,GAAGA,OAAO,CAAC,EAAE;QACjC;QAEAyC,SAAS3C,kBAAkB4C,eAAeD;IAC5C;IAEA,OAAOA;AACT"}