{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/step.ts"], "sourcesContent": ["import type { TestInfo } from '@playwright/test'\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { test } from '@playwright/test'\n\nexport interface StepProps {\n  category: string\n  title: string\n  apiName?: string\n  params?: Record<string, string | number | boolean | null | undefined>\n}\n\n// Access the internal Playwright API until it's exposed publicly.\n// See https://github.com/microsoft/playwright/issues/27059.\ninterface TestInfoWithRunAsStep extends TestInfo {\n  _runAsStep: <T>(\n    stepInfo: StepProps,\n    handler: (result: { complete: Complete }) => Promise<T>\n  ) => Promise<T>\n}\n\ntype Complete = (result: { error?: any }) => void\n\nfunction isWithRunAsStep(\n  testInfo: TestInfo\n): testInfo is TestInfoWithRunAsStep {\n  return '_runAsStep' in testInfo\n}\n\nexport async function step<T>(\n  testInfo: TestInfo,\n  props: StepProps,\n  handler: (complete: Complete) => Promise<Awaited<T>>\n): Promise<Awaited<T>> {\n  if (isWithRunAsStep(testInfo)) {\n    return testInfo._runAsStep(props, ({ complete }) => handler(complete))\n  }\n\n  // Fallback to the `test.step()`.\n  let result: Awaited<T>\n  let reportedError: any\n  try {\n    await test.step(props.title, async () => {\n      result = await handler(({ error }) => {\n        reportedError = error\n        if (reportedError) {\n          throw reportedError\n        }\n      })\n    })\n  } catch (error) {\n    if (error !== reportedError) {\n      throw error\n    }\n  }\n  return result!\n}\n"], "names": ["step", "isWithRunAsStep", "testInfo", "props", "handler", "_runAsStep", "complete", "result", "reportedError", "test", "title", "error"], "mappings": ";;;;+BA4BsBA;;;eAAAA;;;sBA1BD;AAoBrB,SAASC,gBACPC,QAAkB;IAElB,OAAO,gBAAgBA;AACzB;AAEO,eAAeF,KACpBE,QAAkB,EAClBC,KAAgB,EAChBC,OAAoD;IAEpD,IAAIH,gBAAgBC,WAAW;QAC7B,OAAOA,SAASG,UAAU,CAACF,OAAO,CAAC,EAAEG,QAAQ,EAAE,GAAKF,QAAQE;IAC9D;IAEA,iCAAiC;IACjC,IAAIC;IACJ,IAAIC;IACJ,IAAI;QACF,MAAMC,UAAI,CAACT,IAAI,CAACG,MAAMO,KAAK,EAAE;YAC3BH,SAAS,MAAMH,QAAQ,CAAC,EAAEO,KAAK,EAAE;gBAC/BH,gBAAgBG;gBAChB,IAAIH,eAAe;oBACjB,MAAMA;gBACR;YACF;QACF;IACF,EAAE,OAAOG,OAAO;QACd,IAAIA,UAAUH,eAAe;YAC3B,MAAMG;QACR;IACF;IACA,OAAOJ;AACT"}