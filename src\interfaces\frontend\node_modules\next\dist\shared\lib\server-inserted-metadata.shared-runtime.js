'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ServerInsertedMetadataContext", {
    enumerable: true,
    get: function() {
        return ServerInsertedMetadataContext;
    }
});
const _react = require("react");
const ServerInsertedMetadataContext = (0, _react.createContext)(null);

//# sourceMappingURL=server-inserted-metadata.shared-runtime.js.map