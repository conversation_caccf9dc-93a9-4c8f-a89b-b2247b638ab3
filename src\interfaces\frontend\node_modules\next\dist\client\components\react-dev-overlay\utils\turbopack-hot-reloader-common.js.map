{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/utils/turbopack-hot-reloader-common.ts"], "sourcesContent": ["import type { TurbopackMessageAction } from '../../../../server/dev/hot-reloader-types'\nimport type { Update as TurbopackUpdate } from '../../../../build/swc/types'\n\ndeclare global {\n  interface Window {\n    __NEXT_HMR_TURBOPACK_REPORT_NOISY_NOOP_EVENTS: boolean | undefined\n  }\n}\n\n// How long to wait before reporting the HMR start, used to suppress irrelevant\n// `BUILDING` events. Does not impact reported latency.\nconst TURBOPACK_HMR_START_DELAY_MS = 100\n\ninterface HmrUpdate {\n  hasUpdates: boolean\n  updatedModules: Set<string>\n  startMsSinceEpoch: number\n  endMsSinceEpoch: number\n}\n\nexport class TurbopackHmr {\n  #updatedModules: Set<string>\n  #startMsSinceEpoch: number | undefined\n  #lastUpdateMsSinceEpoch: number | undefined\n  #deferredReportHmrStartId: ReturnType<typeof setTimeout> | undefined\n\n  constructor() {\n    this.#updatedModules = new Set()\n  }\n\n  // HACK: Turbopack tends to generate a lot of irrelevant \"BUILDING\" actions,\n  // as it reports *any* compilation, including fully no-op/cached compilations\n  // and those unrelated to HMR. Fixing this would require significant\n  // architectural changes.\n  //\n  // Work around this by deferring any \"rebuilding\" message by 100ms. If we get\n  // a BUILT event within that threshold and nothing has changed, just suppress\n  // the message entirely.\n  #runDeferredReportHmrStart() {\n    if (this.#deferredReportHmrStartId != null) {\n      console.log('[Fast Refresh] rebuilding')\n      this.#cancelDeferredReportHmrStart()\n    }\n  }\n\n  #cancelDeferredReportHmrStart() {\n    clearTimeout(this.#deferredReportHmrStartId)\n    this.#deferredReportHmrStartId = undefined\n  }\n\n  onBuilding() {\n    this.#lastUpdateMsSinceEpoch = undefined\n    this.#cancelDeferredReportHmrStart()\n    this.#startMsSinceEpoch = Date.now()\n\n    // report the HMR start after a short delay\n    this.#deferredReportHmrStartId = setTimeout(\n      () => this.#runDeferredReportHmrStart(),\n      // debugging feature: don't defer/suppress noisy no-op HMR update messages\n      self.__NEXT_HMR_TURBOPACK_REPORT_NOISY_NOOP_EVENTS\n        ? 0\n        : TURBOPACK_HMR_START_DELAY_MS\n    )\n  }\n\n  /** Helper for other `onEvent` methods. */\n  #onUpdate() {\n    this.#runDeferredReportHmrStart()\n    this.#lastUpdateMsSinceEpoch = Date.now()\n  }\n\n  onTurbopackMessage(msg: TurbopackMessageAction) {\n    this.#onUpdate()\n    const updatedModules = extractModulesFromTurbopackMessage(msg.data)\n    for (const module of updatedModules) {\n      this.#updatedModules.add(module)\n    }\n  }\n\n  onServerComponentChanges() {\n    this.#onUpdate()\n  }\n\n  onReloadPage() {\n    this.#onUpdate()\n  }\n\n  onPageAddRemove() {\n    this.#onUpdate()\n  }\n\n  /**\n   * @returns `null` if the caller should ignore the update entirely. Returns an\n   *   object with `hasUpdates: false` if the caller should report the end of\n   *   the HMR in the browser console, but the HMR was a no-op.\n   */\n  onBuilt(): HmrUpdate | null {\n    // Check that we got *any* `TurbopackMessageAction`, even if\n    // `updatedModules` is empty (not everything gets recorded there).\n    //\n    // There's also a case where `onBuilt` gets called before `onBuilding`,\n    // which can happen during initial page load. Ignore that too!\n    const hasUpdates =\n      this.#lastUpdateMsSinceEpoch != null && this.#startMsSinceEpoch != null\n    if (!hasUpdates && this.#deferredReportHmrStartId != null) {\n      // suppress the update entirely\n      this.#cancelDeferredReportHmrStart()\n      return null\n    }\n    this.#runDeferredReportHmrStart()\n\n    const result = {\n      hasUpdates,\n      updatedModules: this.#updatedModules,\n      startMsSinceEpoch: this.#startMsSinceEpoch!,\n      endMsSinceEpoch: this.#lastUpdateMsSinceEpoch ?? Date.now(),\n    }\n    this.#updatedModules = new Set()\n    return result\n  }\n}\n\nfunction extractModulesFromTurbopackMessage(\n  data: TurbopackUpdate | TurbopackUpdate[]\n): Set<string> {\n  const updatedModules: Set<string> = new Set()\n\n  const updates = Array.isArray(data) ? data : [data]\n  for (const update of updates) {\n    // TODO this won't capture changes to CSS since they don't result in a \"merged\" update\n    if (\n      update.type !== 'partial' ||\n      update.instruction.type !== 'ChunkListUpdate' ||\n      update.instruction.merged === undefined\n    ) {\n      continue\n    }\n\n    for (const mergedUpdate of update.instruction.merged) {\n      for (const name of Object.keys(mergedUpdate.entries)) {\n        const res = /(.*)\\s+\\[.*/.exec(name)\n        if (res === null) {\n          console.error(\n            '[Turbopack HMR] Expected module to match pattern: ' + name\n          )\n          continue\n        }\n\n        updatedModules.add(res[1])\n      }\n    }\n  }\n\n  return updatedModules\n}\n"], "names": ["TurbopackHmr", "TURBOPACK_HMR_START_DELAY_MS", "onBuilding", "undefined", "Date", "now", "setTimeout", "self", "__NEXT_HMR_TURBOPACK_REPORT_NOISY_NOOP_EVENTS", "onTurbopackMessage", "msg", "updatedModules", "extractModulesFromTurbopackMessage", "data", "module", "add", "onServerComponentChanges", "onReloadPage", "onPageAddRemove", "onBuilt", "hasUpdates", "result", "startMsSinceEpoch", "endMsSinceEpoch", "Set", "constructor", "console", "log", "clearTimeout", "updates", "Array", "isArray", "update", "type", "instruction", "merged", "mergedUpdate", "name", "Object", "keys", "entries", "res", "exec", "error"], "mappings": ";;;;+BAoBaA;;;eAAAA;;;;;AAXb,+EAA+E;AAC/E,uDAAuD;AACvD,MAAMC,+BAA+B;IAUnC,qFACA,2FACA,qGACA,yGAMA,4EAA4E;AAC5E,6EAA6E;AAC7E,oEAAoE;AACpE,yBAAyB;AACzB,EAAE;AACF,6EAA6E;AAC7E,6EAA6E;AAC7E,wBAAwB;AACxB,2GAOA,iHAoBA,wCAAwC,GACxC;AA9CK,MAAMD;IA8BXE,aAAa;QACX,kCAAA,IAAI,EAAC,yBAAA,2BAA0BC;QAC/B,kCAAA,IAAI,EAAC,+BAAA;QACL,kCAAA,IAAI,EAAC,oBAAA,sBAAqBC,KAAKC,GAAG;QAElC,2CAA2C;QAC3C,kCAAA,IAAI,EAAC,2BAAA,6BAA4BC,WAC/B,IAAM,kCAAA,IAAI,EAAC,4BAAA,+BACX,0EAA0E;QAC1EC,KAAKC,6CAA6C,GAC9C,IACAP;IAER;IAQAQ,mBAAmBC,GAA2B,EAAE;QAC9C,kCAAA,IAAI,EAAC,WAAA;QACL,MAAMC,iBAAiBC,mCAAmCF,IAAIG,IAAI;QAClE,KAAK,MAAMC,UAAUH,eAAgB;YACnC,kCAAA,IAAI,EAAC,iBAAA,iBAAgBI,GAAG,CAACD;QAC3B;IACF;IAEAE,2BAA2B;QACzB,kCAAA,IAAI,EAAC,WAAA;IACP;IAEAC,eAAe;QACb,kCAAA,IAAI,EAAC,WAAA;IACP;IAEAC,kBAAkB;QAChB,kCAAA,IAAI,EAAC,WAAA;IACP;IAEA;;;;GAIC,GACDC,UAA4B;QAC1B,4DAA4D;QAC5D,kEAAkE;QAClE,EAAE;QACF,uEAAuE;QACvE,8DAA8D;QAC9D,MAAMC,aACJ,kCAAA,IAAI,EAAC,yBAAA,4BAA2B,QAAQ,kCAAA,IAAI,EAAC,oBAAA,uBAAsB;QACrE,IAAI,CAACA,cAAc,kCAAA,IAAI,EAAC,2BAAA,8BAA6B,MAAM;YACzD,+BAA+B;YAC/B,kCAAA,IAAI,EAAC,+BAAA;YACL,OAAO;QACT;QACA,kCAAA,IAAI,EAAC,4BAAA;;QAEL,MAAMC,SAAS;YACbD;YACAT,cAAc,EAAE,kCAAA,IAAI,EAAC,iBAAA;YACrBW,iBAAiB,EAAE,kCAAA,IAAI,EAAC,oBAAA;YACxBC,iBAAiB,2DAAA,kCAAA,IAAI,EAAC,yBAAA,8FAA2BnB,KAAKC,GAAG;QAC3D;QACA,kCAAA,IAAI,EAAC,iBAAA,mBAAkB,IAAImB;QAC3B,OAAOH;IACT;IA7FAI,aAAc;QAYd,4BAAA;mBAAA;;QAOA,4BAAA;mBAAA;;QAqBA,4BAAA;mBAAA;;QA7CA,4BAAA;;mBAAA,KAAA;;QACA,4BAAA;;mBAAA,KAAA;;QACA,4BAAA;;mBAAA,KAAA;;QACA,4BAAA;;mBAAA,KAAA;;QAGE,kCAAA,IAAI,EAAC,iBAAA,mBAAkB,IAAID;IAC7B;AA4FF;AAlFE,SAAA;IACE,IAAI,kCAAA,IAAI,EAAC,2BAAA,8BAA6B,MAAM;QAC1CE,QAAQC,GAAG,CAAC;QACZ,kCAAA,IAAI,EAAC,+BAAA;IACP;AACF;AAEA,SAAA;IACEC,aAAa,kCAAA,IAAI,EAAC,2BAAA;IAClB,kCAAA,IAAI,EAAC,2BAAA,6BAA4BzB;AACnC;AAkBA,SAAA;IACE,kCAAA,IAAI,EAAC,4BAAA;IACL,kCAAA,IAAI,EAAC,yBAAA,2BAA0BC,KAAKC,GAAG;AACzC;AAqDF,SAASO,mCACPC,IAAyC;IAEzC,MAAMF,iBAA8B,IAAIa;IAExC,MAAMK,UAAUC,MAAMC,OAAO,CAAClB,QAAQA,OAAO;QAACA;KAAK;IACnD,KAAK,MAAMmB,UAAUH,QAAS;QAC5B,sFAAsF;QACtF,IACEG,OAAOC,IAAI,KAAK,aAChBD,OAAOE,WAAW,CAACD,IAAI,KAAK,qBAC5BD,OAAOE,WAAW,CAACC,MAAM,KAAKhC,WAC9B;YACA;QACF;QAEA,KAAK,MAAMiC,gBAAgBJ,OAAOE,WAAW,CAACC,MAAM,CAAE;YACpD,KAAK,MAAME,QAAQC,OAAOC,IAAI,CAACH,aAAaI,OAAO,EAAG;gBACpD,MAAMC,MAAM,cAAcC,IAAI,CAACL;gBAC/B,IAAII,QAAQ,MAAM;oBAChBf,QAAQiB,KAAK,CACX,uDAAuDN;oBAEzD;gBACF;gBAEA1B,eAAeI,GAAG,CAAC0B,GAAG,CAAC,EAAE;YAC3B;QACF;IACF;IAEA,OAAO9B;AACT"}