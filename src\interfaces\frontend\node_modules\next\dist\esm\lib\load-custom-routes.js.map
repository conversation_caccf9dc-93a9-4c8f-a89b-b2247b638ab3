{"version": 3, "sources": ["../../src/lib/load-custom-routes.ts"], "sourcesContent": ["import type { NextConfig } from '../server/config'\nimport type { Token } from 'next/dist/compiled/path-to-regexp'\n\nimport { bold, yellow } from './picocolors'\nimport { escapeStringRegexp } from '../shared/lib/escape-regexp'\nimport { tryToParsePath } from './try-to-parse-path'\nimport { allowedStatusCodes } from './redirect-status'\nimport { isFullStringUrl } from './url'\n\nexport type RouteHas =\n  | {\n      type: string\n      key: string\n      value?: string\n    }\n  | {\n      type: 'host'\n      key?: undefined\n      value: string\n    }\n\nexport type Rewrite = {\n  source: string\n  destination: string\n  basePath?: false\n  locale?: false\n  has?: RouteHas[]\n  missing?: RouteHas[]\n\n  /**\n   * @internal - used internally for routing\n   */\n  internal?: boolean\n}\n\nexport type Header = {\n  source: string\n  basePath?: false\n  locale?: false\n  headers: Array<{ key: string; value: string }>\n  has?: RouteHas[]\n  missing?: RouteHas[]\n\n  /**\n   * @internal - used internally for routing\n   */\n  internal?: boolean\n}\n\n// internal type used for validation (not user facing)\nexport type Redirect = {\n  source: string\n  destination: string\n  basePath?: false\n  locale?: false\n  has?: RouteHas[]\n  missing?: RouteHas[]\n\n  /**\n   * @internal - used internally for routing\n   */\n  internal?: boolean\n} & (\n  | {\n      statusCode?: never\n      permanent: boolean\n    }\n  | {\n      statusCode: number\n      permanent?: never\n    }\n)\n\nexport type Middleware = {\n  source: string\n  locale?: false\n  has?: RouteHas[]\n  missing?: RouteHas[]\n}\n\nconst allowedHasTypes = new Set(['header', 'cookie', 'query', 'host'])\nconst namedGroupsRegex = /\\(\\?<([a-zA-Z][a-zA-Z0-9]*)>/g\n\nexport function normalizeRouteRegex(regex: string) {\n  // clean up un-necessary escaping from regex.source which turns / into \\\\/\n  return regex.replace(/\\\\\\//g, '/')\n}\n\nfunction checkRedirect(route: Redirect): {\n  invalidParts: string[]\n  hadInvalidStatus: boolean\n} {\n  const invalidParts: string[] = []\n  let hadInvalidStatus: boolean = false\n\n  if (route.statusCode && !allowedStatusCodes.has(route['statusCode'])) {\n    hadInvalidStatus = true\n    invalidParts.push(`\\`statusCode\\` is not undefined or valid statusCode`)\n  }\n  if (typeof route.permanent !== 'boolean' && !route['statusCode']) {\n    invalidParts.push(`\\`permanent\\` is not set to \\`true\\` or \\`false\\``)\n  }\n\n  return {\n    invalidParts,\n    hadInvalidStatus,\n  }\n}\n\nfunction checkHeader(route: Header): string[] {\n  const invalidParts: string[] = []\n\n  if (!Array.isArray(route.headers)) {\n    invalidParts.push('`headers` field must be an array')\n  } else if (route.headers.length === 0) {\n    invalidParts.push('`headers` field cannot be empty')\n  } else {\n    for (const header of route.headers) {\n      if (!header || typeof header !== 'object') {\n        invalidParts.push(\n          \"`headers` items must be object with { key: '', value: '' }\"\n        )\n        break\n      }\n      if (typeof header.key !== 'string') {\n        invalidParts.push('`key` in header item must be string')\n        break\n      }\n      if (typeof header.value !== 'string') {\n        invalidParts.push('`value` in header item must be string')\n        break\n      }\n    }\n  }\n  return invalidParts\n}\n\nexport type RouteType = 'rewrite' | 'redirect' | 'header'\n\nexport function checkCustomRoutes(\n  routes: Redirect[] | Header[] | Rewrite[] | Middleware[],\n  type: RouteType | 'middleware'\n): void {\n  if (!Array.isArray(routes)) {\n    console.error(\n      `Error: ${type}s must return an array, received ${typeof routes}.\\n` +\n        `See here for more info: https://nextjs.org/docs/messages/routes-must-be-array`\n    )\n    process.exit(1)\n  }\n\n  let numInvalidRoutes = 0\n  let hadInvalidStatus = false\n  let hadInvalidHas = false\n  let hadInvalidMissing = false\n\n  const allowedKeys = new Set<string>(['source', 'locale', 'has', 'missing'])\n\n  if (type === 'rewrite') {\n    allowedKeys.add('basePath')\n    allowedKeys.add('destination')\n  }\n  if (type === 'redirect') {\n    allowedKeys.add('basePath')\n    allowedKeys.add('statusCode')\n    allowedKeys.add('permanent')\n    allowedKeys.add('destination')\n  }\n  if (type === 'header') {\n    allowedKeys.add('basePath')\n    allowedKeys.add('headers')\n  }\n\n  for (const route of routes) {\n    if (!route || typeof route !== 'object') {\n      console.error(\n        `The route ${JSON.stringify(\n          route\n        )} is not a valid object with \\`source\\`${\n          type !== 'middleware'\n            ? ` and \\`${type === 'header' ? 'headers' : 'destination'}\\``\n            : ''\n        }`\n      )\n      numInvalidRoutes++\n      continue\n    }\n\n    if (\n      type === 'rewrite' &&\n      (route as Rewrite).basePath === false &&\n      !(\n        (route as Rewrite).destination.startsWith('http://') ||\n        (route as Rewrite).destination.startsWith('https://')\n      )\n    ) {\n      console.error(\n        `The route ${\n          (route as Rewrite).source\n        } rewrites urls outside of the basePath. Please use a destination that starts with \\`http://\\` or \\`https://\\` https://nextjs.org/docs/messages/invalid-external-rewrite`\n      )\n      numInvalidRoutes++\n      continue\n    }\n\n    const keys = Object.keys(route)\n    const invalidKeys = keys.filter((key) => !allowedKeys.has(key))\n    const invalidParts: string[] = []\n\n    if (\n      'basePath' in route &&\n      typeof route.basePath !== 'undefined' &&\n      route.basePath !== false\n    ) {\n      invalidParts.push('`basePath` must be undefined or false')\n    }\n\n    if (typeof route.locale !== 'undefined' && route.locale !== false) {\n      invalidParts.push('`locale` must be undefined or false')\n    }\n\n    const checkInvalidHasMissing = (\n      items: any,\n      fieldName: 'has' | 'missing'\n    ) => {\n      let hadInvalidItem = false\n\n      if (typeof items !== 'undefined' && !Array.isArray(items)) {\n        invalidParts.push(\n          `\\`${fieldName}\\` must be undefined or valid has object`\n        )\n        hadInvalidItem = true\n      } else if (items) {\n        const invalidHasItems = []\n\n        for (const hasItem of items) {\n          let invalidHasParts = []\n\n          if (!allowedHasTypes.has(hasItem.type)) {\n            invalidHasParts.push(`invalid type \"${hasItem.type}\"`)\n          }\n          if (typeof hasItem.key !== 'string' && hasItem.type !== 'host') {\n            invalidHasParts.push(`invalid key \"${hasItem.key}\"`)\n          }\n          if (\n            typeof hasItem.value !== 'undefined' &&\n            typeof hasItem.value !== 'string'\n          ) {\n            invalidHasParts.push(`invalid value \"${hasItem.value}\"`)\n          }\n          if (typeof hasItem.value === 'undefined' && hasItem.type === 'host') {\n            invalidHasParts.push(`value is required for \"host\" type`)\n          }\n\n          if (invalidHasParts.length > 0) {\n            invalidHasItems.push(\n              `${invalidHasParts.join(', ')} for ${JSON.stringify(hasItem)}`\n            )\n          }\n        }\n\n        if (invalidHasItems.length > 0) {\n          hadInvalidItem = true\n          const itemStr = `item${invalidHasItems.length === 1 ? '' : 's'}`\n\n          console.error(\n            `Invalid \\`${fieldName}\\` ${itemStr}:\\n` +\n              invalidHasItems.join('\\n')\n          )\n          console.error()\n          invalidParts.push(`invalid \\`${fieldName}\\` ${itemStr} found`)\n        }\n      }\n      return hadInvalidItem\n    }\n    if (checkInvalidHasMissing(route.has, 'has')) {\n      hadInvalidHas = true\n    }\n    if (checkInvalidHasMissing(route.missing, 'missing')) {\n      hadInvalidMissing = true\n    }\n\n    if (!route.source) {\n      invalidParts.push('`source` is missing')\n    } else if (typeof route.source !== 'string') {\n      invalidParts.push('`source` is not a string')\n    } else if (!route.source.startsWith('/')) {\n      invalidParts.push('`source` does not start with /')\n    }\n\n    if (type === 'header') {\n      invalidParts.push(...checkHeader(route as Header))\n    } else if (type !== 'middleware') {\n      let _route = route as Rewrite | Redirect\n      if (!_route.destination) {\n        invalidParts.push('`destination` is missing')\n      } else if (typeof _route.destination !== 'string') {\n        invalidParts.push('`destination` is not a string')\n      } else if (\n        type === 'rewrite' &&\n        !_route.destination.match(/^(\\/|https:\\/\\/|http:\\/\\/)/)\n      ) {\n        invalidParts.push(\n          '`destination` does not start with `/`, `http://`, or `https://`'\n        )\n      }\n    }\n\n    if (type === 'redirect') {\n      const result = checkRedirect(route as Redirect)\n      hadInvalidStatus = hadInvalidStatus || result.hadInvalidStatus\n      invalidParts.push(...result.invalidParts)\n    }\n\n    let sourceTokens: Token[] | undefined\n\n    if (typeof route.source === 'string' && route.source.startsWith('/')) {\n      // only show parse error if we didn't already show error\n      // for not being a string\n      const { tokens, error, regexStr } = tryToParsePath(route.source)\n\n      if (error) {\n        invalidParts.push('`source` parse failed')\n      }\n\n      if (regexStr && regexStr.length > 4096) {\n        invalidParts.push('`source` exceeds max built length of 4096')\n      }\n\n      sourceTokens = tokens\n    }\n    const hasSegments = new Set<string>()\n\n    if (route.has) {\n      for (const hasItem of route.has) {\n        if (!hasItem.value && hasItem.key) {\n          hasSegments.add(hasItem.key)\n        }\n\n        if (hasItem.value) {\n          for (const match of hasItem.value.matchAll(namedGroupsRegex)) {\n            if (match[1]) {\n              hasSegments.add(match[1])\n            }\n          }\n\n          if (hasItem.type === 'host') {\n            hasSegments.add('host')\n          }\n        }\n      }\n    }\n\n    // make sure no unnamed patterns are attempted to be used in the\n    // destination as this can cause confusion and is not allowed\n    if (typeof (route as Rewrite).destination === 'string') {\n      if (\n        (route as Rewrite).destination.startsWith('/') &&\n        Array.isArray(sourceTokens)\n      ) {\n        const unnamedInDest = new Set()\n\n        for (const token of sourceTokens) {\n          if (typeof token === 'object' && typeof token.name === 'number') {\n            const unnamedIndex = new RegExp(`:${token.name}(?!\\\\d)`)\n            if ((route as Rewrite).destination.match(unnamedIndex)) {\n              unnamedInDest.add(`:${token.name}`)\n            }\n          }\n        }\n\n        if (unnamedInDest.size > 0) {\n          invalidParts.push(\n            `\\`destination\\` has unnamed params ${[...unnamedInDest].join(\n              ', '\n            )}`\n          )\n        } else {\n          const {\n            tokens: destTokens,\n            regexStr: destRegexStr,\n            error: destinationParseFailed,\n          } = tryToParsePath((route as Rewrite).destination, {\n            handleUrl: true,\n          })\n\n          if (destRegexStr && destRegexStr.length > 4096) {\n            invalidParts.push('`destination` exceeds max built length of 4096')\n          }\n\n          if (destinationParseFailed) {\n            invalidParts.push('`destination` parse failed')\n          } else {\n            const sourceSegments = new Set(\n              sourceTokens\n                .map((item) => typeof item === 'object' && item.name)\n                .filter(Boolean)\n            )\n            const invalidDestSegments = new Set()\n\n            for (const token of destTokens!) {\n              if (\n                typeof token === 'object' &&\n                !sourceSegments.has(token.name) &&\n                !hasSegments.has(token.name as string)\n              ) {\n                invalidDestSegments.add(token.name)\n              }\n            }\n\n            if (invalidDestSegments.size) {\n              invalidParts.push(\n                `\\`destination\\` has segments not in \\`source\\` or \\`has\\` (${[\n                  ...invalidDestSegments,\n                ].join(', ')})`\n              )\n            }\n          }\n        }\n      }\n    }\n\n    const hasInvalidKeys = invalidKeys.length > 0\n    const hasInvalidParts = invalidParts.length > 0\n\n    if (hasInvalidKeys || hasInvalidParts) {\n      console.error(\n        `${invalidParts.join(', ')}${\n          invalidKeys.length\n            ? (hasInvalidParts ? ',' : '') +\n              ` invalid field${invalidKeys.length === 1 ? '' : 's'}: ` +\n              invalidKeys.join(',')\n            : ''\n        } for route ${JSON.stringify(route)}`\n      )\n      console.error()\n      numInvalidRoutes++\n    }\n  }\n\n  if (numInvalidRoutes > 0) {\n    if (hadInvalidStatus) {\n      console.error(\n        `\\nValid redirect statusCode values are ${[...allowedStatusCodes].join(\n          ', '\n        )}`\n      )\n    }\n    if (hadInvalidHas) {\n      console.error(\n        `\\nValid \\`has\\` object shape is ${JSON.stringify(\n          {\n            type: [...allowedHasTypes].join(', '),\n            key: 'the key to check for',\n            value: 'undefined or a value string to match against',\n          },\n          null,\n          2\n        )}`\n      )\n    }\n    if (hadInvalidMissing) {\n      console.error(\n        `\\nValid \\`missing\\` object shape is ${JSON.stringify(\n          {\n            type: [...allowedHasTypes].join(', '),\n            key: 'the key to check for',\n            value: 'undefined or a value string to match against',\n          },\n          null,\n          2\n        )}`\n      )\n    }\n    console.error()\n    console.error(\n      `Error: Invalid ${type}${numInvalidRoutes === 1 ? '' : 's'} found`\n    )\n    process.exit(1)\n  }\n}\n\nexport interface CustomRoutes {\n  headers: Header[]\n  rewrites: {\n    fallback: Rewrite[]\n    afterFiles: Rewrite[]\n    beforeFiles: Rewrite[]\n  }\n  redirects: Redirect[]\n}\n\nfunction processRoutes<T>(\n  routes: T,\n  config: NextConfig,\n  type: 'redirect' | 'rewrite' | 'header'\n): T {\n  const _routes = routes as any as Array<{\n    source: string\n    locale?: false\n    basePath?: false\n    destination?: string\n  }>\n  const newRoutes: typeof _routes = []\n  const defaultLocales: Array<{\n    locale: string\n    base: string\n  }> = []\n\n  if (config.i18n && type === 'redirect') {\n    for (const item of config.i18n?.domains || []) {\n      defaultLocales.push({\n        locale: item.defaultLocale,\n        base: `http${item.http ? '' : 's'}://${item.domain}`,\n      })\n    }\n\n    defaultLocales.push({\n      locale: config.i18n.defaultLocale,\n      base: '',\n    })\n  }\n\n  for (const r of _routes) {\n    const srcBasePath =\n      config.basePath && r.basePath !== false ? config.basePath : ''\n    const isExternal = !r.destination?.startsWith('/')\n    const destBasePath = srcBasePath && !isExternal ? srcBasePath : ''\n\n    if (config.i18n && r.locale !== false) {\n      if (!isExternal) {\n        defaultLocales.forEach((item) => {\n          let destination\n\n          if (r.destination) {\n            destination = item.base\n              ? `${item.base}${destBasePath}${r.destination}`\n              : `${destBasePath}${r.destination}`\n          }\n\n          newRoutes.push({\n            ...r,\n            destination,\n            source: `${srcBasePath}/${item.locale}${\n              r.source === '/' && !config.trailingSlash ? '' : r.source\n            }`,\n          })\n        })\n      }\n\n      r.source = `/:nextInternalLocale(${config.i18n.locales\n        .map((locale: string) => escapeStringRegexp(locale))\n        .join('|')})${\n        r.source === '/' && !config.trailingSlash ? '' : r.source\n      }`\n\n      if (r.destination && r.destination?.startsWith('/')) {\n        r.destination = `/:nextInternalLocale${\n          r.destination === '/' && !config.trailingSlash ? '' : r.destination\n        }`\n      }\n    }\n    r.source = `${srcBasePath}${\n      r.source === '/' && srcBasePath ? '' : r.source\n    }`\n\n    if (r.destination) {\n      r.destination = `${destBasePath}${\n        r.destination === '/' && destBasePath ? '' : r.destination\n      }`\n    }\n    newRoutes.push(r)\n  }\n  return newRoutes as any as T\n}\n\nasync function loadRedirects(config: NextConfig) {\n  if (typeof config.redirects !== 'function') {\n    return []\n  }\n  let redirects = await config.redirects()\n  // check before we process the routes and after to ensure\n  // they are still valid\n  checkCustomRoutes(redirects, 'redirect')\n\n  // save original redirects before transforms\n  if (Array.isArray(redirects)) {\n    config._originalRedirects = redirects.map((r) => ({ ...r }))\n  }\n  redirects = processRoutes(redirects, config, 'redirect')\n  checkCustomRoutes(redirects, 'redirect')\n  return redirects\n}\n\nasync function loadRewrites(config: NextConfig) {\n  // If assetPrefix is set, add a rewrite for `/${assetPrefix}/_next/*`\n  // requests so that they are handled in any of dev, start, or deploy\n  // automatically without the user having to configure this.\n  // If the assetPrefix is an absolute URL, we still consider the path for automatic rewrite.\n  // but hostname routing must be handled by the user\n  let maybeAssetPrefixRewrite: Rewrite[] = []\n  if (config.assetPrefix) {\n    let prefix = config.assetPrefix\n    if (\n      isFullStringUrl(config.assetPrefix) &&\n      URL.canParse(config.assetPrefix)\n    ) {\n      prefix = new URL(config.assetPrefix).pathname\n    }\n\n    if (prefix && prefix !== '/') {\n      const assetPrefix = prefix.startsWith('/') ? prefix : `/${prefix}`\n      const basePath = config.basePath || ''\n      // If these are the same, then this would result in an infinite rewrite.\n      if (assetPrefix !== basePath) {\n        maybeAssetPrefixRewrite.push({\n          source: `${assetPrefix}/_next/:path+`,\n          destination: `${basePath}/_next/:path+`,\n        })\n      }\n    }\n  }\n\n  if (typeof config.rewrites !== 'function') {\n    return {\n      beforeFiles: [...maybeAssetPrefixRewrite],\n      afterFiles: [],\n      fallback: [],\n    }\n  }\n  const _rewrites = await config.rewrites()\n  let beforeFiles: Rewrite[] = []\n  let afterFiles: Rewrite[] = []\n  let fallback: Rewrite[] = []\n\n  if (\n    !Array.isArray(_rewrites) &&\n    typeof _rewrites === 'object' &&\n    Object.keys(_rewrites).every(\n      (key) =>\n        key === 'beforeFiles' || key === 'afterFiles' || key === 'fallback'\n    )\n  ) {\n    beforeFiles = _rewrites.beforeFiles || []\n    afterFiles = _rewrites.afterFiles || []\n    fallback = _rewrites.fallback || []\n  } else {\n    afterFiles = _rewrites as any\n  }\n  // check before we process the routes and after to ensure\n  // they are still valid\n  checkCustomRoutes(beforeFiles, 'rewrite')\n  checkCustomRoutes(afterFiles, 'rewrite')\n  checkCustomRoutes(fallback, 'rewrite')\n\n  // save original rewrites before transforms\n  config._originalRewrites = {\n    beforeFiles: beforeFiles.map((r) => ({ ...r })),\n    afterFiles: afterFiles.map((r) => ({ ...r })),\n    fallback: fallback.map((r) => ({ ...r })),\n  }\n\n  beforeFiles = [\n    ...maybeAssetPrefixRewrite,\n    ...processRoutes(beforeFiles, config, 'rewrite'),\n  ]\n  afterFiles = processRoutes(afterFiles, config, 'rewrite')\n  fallback = processRoutes(fallback, config, 'rewrite')\n\n  checkCustomRoutes(beforeFiles, 'rewrite')\n  checkCustomRoutes(afterFiles, 'rewrite')\n  checkCustomRoutes(fallback, 'rewrite')\n\n  return {\n    beforeFiles,\n    afterFiles,\n    fallback,\n  }\n}\n\nasync function loadHeaders(config: NextConfig) {\n  if (typeof config.headers !== 'function') {\n    return []\n  }\n  let headers = await config.headers()\n  // check before we process the routes and after to ensure\n  // they are still valid\n  checkCustomRoutes(headers, 'header')\n\n  headers = processRoutes(headers, config, 'header')\n  checkCustomRoutes(headers, 'header')\n  return headers\n}\n\nexport default async function loadCustomRoutes(\n  config: NextConfig\n): Promise<CustomRoutes> {\n  const [headers, rewrites, redirects] = await Promise.all([\n    loadHeaders(config),\n    loadRewrites(config),\n    loadRedirects(config),\n  ])\n\n  const totalRewrites =\n    rewrites.beforeFiles.length +\n    rewrites.afterFiles.length +\n    rewrites.fallback.length\n\n  const totalRoutes = headers.length + redirects.length + totalRewrites\n\n  if (totalRoutes > 1000) {\n    console.warn(\n      bold(yellow(`Warning: `)) +\n        `total number of custom routes exceeds 1000, this can reduce performance. Route counts:\\n` +\n        `headers: ${headers.length}\\n` +\n        `rewrites: ${totalRewrites}\\n` +\n        `redirects: ${redirects.length}\\n` +\n        `See more info: https://nextjs.org/docs/messages/max-custom-routes-reached`\n    )\n  }\n\n  if (!config.skipTrailingSlashRedirect) {\n    if (config.trailingSlash) {\n      redirects.unshift(\n        {\n          source: '/:file((?!\\\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\\\.\\\\w+)/',\n          destination: '/:file',\n          permanent: true,\n          locale: config.i18n ? false : undefined,\n          internal: true,\n          // don't run this redirect for _next/data requests\n          missing: [\n            {\n              type: 'header',\n              key: 'x-nextjs-data',\n            },\n          ],\n        },\n        {\n          source: '/:notfile((?!\\\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\\\.]+)',\n          destination: '/:notfile/',\n          permanent: true,\n          locale: config.i18n ? false : undefined,\n          internal: true,\n        }\n      )\n      if (config.basePath) {\n        redirects.unshift({\n          source: config.basePath,\n          destination: config.basePath + '/',\n          permanent: true,\n          basePath: false,\n          locale: config.i18n ? false : undefined,\n          internal: true,\n        })\n      }\n    } else {\n      redirects.unshift({\n        source: '/:path+/',\n        destination: '/:path+',\n        permanent: true,\n        locale: config.i18n ? false : undefined,\n        internal: true,\n      })\n      if (config.basePath) {\n        redirects.unshift({\n          source: config.basePath + '/',\n          destination: config.basePath,\n          permanent: true,\n          basePath: false,\n          locale: config.i18n ? false : undefined,\n          internal: true,\n        })\n      }\n    }\n  }\n\n  return {\n    headers,\n    rewrites,\n    redirects,\n  }\n}\n"], "names": ["bold", "yellow", "escapeStringRegexp", "tryToParsePath", "allowedStatusCodes", "isFullStringUrl", "allowedHasTypes", "Set", "namedGroupsRegex", "normalizeRouteRegex", "regex", "replace", "checkRedirect", "route", "invalidParts", "hadInvalidStatus", "statusCode", "has", "push", "permanent", "checkHeader", "Array", "isArray", "headers", "length", "header", "key", "value", "checkCustomRoutes", "routes", "type", "console", "error", "process", "exit", "numInvalidRoutes", "hadInvalidHas", "hadInvalidMissing", "<PERSON><PERSON><PERSON><PERSON>", "add", "JSON", "stringify", "basePath", "destination", "startsWith", "source", "keys", "Object", "<PERSON><PERSON><PERSON><PERSON>", "filter", "locale", "checkInvalidHasMissing", "items", "fieldName", "hadInvalidItem", "invalidHasItems", "hasItem", "invalidHasParts", "join", "itemStr", "missing", "_route", "match", "result", "sourceTokens", "tokens", "regexStr", "hasSegments", "matchAll", "unnamedInDest", "token", "name", "unnamedIndex", "RegExp", "size", "destTokens", "destRegexStr", "destinationParseFailed", "handleUrl", "sourceSegments", "map", "item", "Boolean", "invalidDestSegments", "hasInvalidKeys", "hasInvalidParts", "processRoutes", "config", "_routes", "newRoutes", "defaultLocales", "i18n", "domains", "defaultLocale", "base", "http", "domain", "r", "srcBasePath", "isExternal", "destBasePath", "for<PERSON>ach", "trailingSlash", "locales", "loadRedirects", "redirects", "_originalRedirects", "loadRewrites", "maybeAssetPrefixRewrite", "assetPrefix", "prefix", "URL", "canParse", "pathname", "rewrites", "beforeFiles", "afterFiles", "fallback", "_rewrites", "every", "_originalRewrites", "loadHeaders", "loadCustomRoutes", "Promise", "all", "totalRewrites", "totalRoutes", "warn", "skipTrailingSlashRedirect", "unshift", "undefined", "internal"], "mappings": "AAGA,SAASA,IAAI,EAAEC,MAAM,QAAQ,eAAc;AAC3C,SAASC,kBAAkB,QAAQ,8BAA6B;AAChE,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,kBAAkB,QAAQ,oBAAmB;AACtD,SAASC,eAAe,QAAQ,QAAO;AAyEvC,MAAMC,kBAAkB,IAAIC,IAAI;IAAC;IAAU;IAAU;IAAS;CAAO;AACrE,MAAMC,mBAAmB;AAEzB,OAAO,SAASC,oBAAoBC,KAAa;IAC/C,0EAA0E;IAC1E,OAAOA,MAAMC,OAAO,CAAC,SAAS;AAChC;AAEA,SAASC,cAAcC,KAAe;IAIpC,MAAMC,eAAyB,EAAE;IACjC,IAAIC,mBAA4B;IAEhC,IAAIF,MAAMG,UAAU,IAAI,CAACZ,mBAAmBa,GAAG,CAACJ,KAAK,CAAC,aAAa,GAAG;QACpEE,mBAAmB;QACnBD,aAAaI,IAAI,CAAC,CAAC,mDAAmD,CAAC;IACzE;IACA,IAAI,OAAOL,MAAMM,SAAS,KAAK,aAAa,CAACN,KAAK,CAAC,aAAa,EAAE;QAChEC,aAAaI,IAAI,CAAC,CAAC,iDAAiD,CAAC;IACvE;IAEA,OAAO;QACLJ;QACAC;IACF;AACF;AAEA,SAASK,YAAYP,KAAa;IAChC,MAAMC,eAAyB,EAAE;IAEjC,IAAI,CAACO,MAAMC,OAAO,CAACT,MAAMU,OAAO,GAAG;QACjCT,aAAaI,IAAI,CAAC;IACpB,OAAO,IAAIL,MAAMU,OAAO,CAACC,MAAM,KAAK,GAAG;QACrCV,aAAaI,IAAI,CAAC;IACpB,OAAO;QACL,KAAK,MAAMO,UAAUZ,MAAMU,OAAO,CAAE;YAClC,IAAI,CAACE,UAAU,OAAOA,WAAW,UAAU;gBACzCX,aAAaI,IAAI,CACf;gBAEF;YACF;YACA,IAAI,OAAOO,OAAOC,GAAG,KAAK,UAAU;gBAClCZ,aAAaI,IAAI,CAAC;gBAClB;YACF;YACA,IAAI,OAAOO,OAAOE,KAAK,KAAK,UAAU;gBACpCb,aAAaI,IAAI,CAAC;gBAClB;YACF;QACF;IACF;IACA,OAAOJ;AACT;AAIA,OAAO,SAASc,kBACdC,MAAwD,EACxDC,IAA8B;IAE9B,IAAI,CAACT,MAAMC,OAAO,CAACO,SAAS;QAC1BE,QAAQC,KAAK,CACX,CAAC,OAAO,EAAEF,KAAK,iCAAiC,EAAE,OAAOD,OAAO,GAAG,CAAC,GAClE,CAAC,6EAA6E,CAAC;QAEnFI,QAAQC,IAAI,CAAC;IACf;IAEA,IAAIC,mBAAmB;IACvB,IAAIpB,mBAAmB;IACvB,IAAIqB,gBAAgB;IACpB,IAAIC,oBAAoB;IAExB,MAAMC,cAAc,IAAI/B,IAAY;QAAC;QAAU;QAAU;QAAO;KAAU;IAE1E,IAAIuB,SAAS,WAAW;QACtBQ,YAAYC,GAAG,CAAC;QAChBD,YAAYC,GAAG,CAAC;IAClB;IACA,IAAIT,SAAS,YAAY;QACvBQ,YAAYC,GAAG,CAAC;QAChBD,YAAYC,GAAG,CAAC;QAChBD,YAAYC,GAAG,CAAC;QAChBD,YAAYC,GAAG,CAAC;IAClB;IACA,IAAIT,SAAS,UAAU;QACrBQ,YAAYC,GAAG,CAAC;QAChBD,YAAYC,GAAG,CAAC;IAClB;IAEA,KAAK,MAAM1B,SAASgB,OAAQ;QAC1B,IAAI,CAAChB,SAAS,OAAOA,UAAU,UAAU;YACvCkB,QAAQC,KAAK,CACX,CAAC,UAAU,EAAEQ,KAAKC,SAAS,CACzB5B,OACA,sCAAsC,EACtCiB,SAAS,eACL,CAAC,OAAO,EAAEA,SAAS,WAAW,YAAY,cAAc,EAAE,CAAC,GAC3D,IACJ;YAEJK;YACA;QACF;QAEA,IACEL,SAAS,aACT,AAACjB,MAAkB6B,QAAQ,KAAK,SAChC,CACE,CAAA,AAAC7B,MAAkB8B,WAAW,CAACC,UAAU,CAAC,cAC1C,AAAC/B,MAAkB8B,WAAW,CAACC,UAAU,CAAC,WAAU,GAEtD;YACAb,QAAQC,KAAK,CACX,CAAC,UAAU,EACT,AAACnB,MAAkBgC,MAAM,CAC1B,uKAAuK,CAAC;YAE3KV;YACA;QACF;QAEA,MAAMW,OAAOC,OAAOD,IAAI,CAACjC;QACzB,MAAMmC,cAAcF,KAAKG,MAAM,CAAC,CAACvB,MAAQ,CAACY,YAAYrB,GAAG,CAACS;QAC1D,MAAMZ,eAAyB,EAAE;QAEjC,IACE,cAAcD,SACd,OAAOA,MAAM6B,QAAQ,KAAK,eAC1B7B,MAAM6B,QAAQ,KAAK,OACnB;YACA5B,aAAaI,IAAI,CAAC;QACpB;QAEA,IAAI,OAAOL,MAAMqC,MAAM,KAAK,eAAerC,MAAMqC,MAAM,KAAK,OAAO;YACjEpC,aAAaI,IAAI,CAAC;QACpB;QAEA,MAAMiC,yBAAyB,CAC7BC,OACAC;YAEA,IAAIC,iBAAiB;YAErB,IAAI,OAAOF,UAAU,eAAe,CAAC/B,MAAMC,OAAO,CAAC8B,QAAQ;gBACzDtC,aAAaI,IAAI,CACf,CAAC,EAAE,EAAEmC,UAAU,wCAAwC,CAAC;gBAE1DC,iBAAiB;YACnB,OAAO,IAAIF,OAAO;gBAChB,MAAMG,kBAAkB,EAAE;gBAE1B,KAAK,MAAMC,WAAWJ,MAAO;oBAC3B,IAAIK,kBAAkB,EAAE;oBAExB,IAAI,CAACnD,gBAAgBW,GAAG,CAACuC,QAAQ1B,IAAI,GAAG;wBACtC2B,gBAAgBvC,IAAI,CAAC,CAAC,cAAc,EAAEsC,QAAQ1B,IAAI,CAAC,CAAC,CAAC;oBACvD;oBACA,IAAI,OAAO0B,QAAQ9B,GAAG,KAAK,YAAY8B,QAAQ1B,IAAI,KAAK,QAAQ;wBAC9D2B,gBAAgBvC,IAAI,CAAC,CAAC,aAAa,EAAEsC,QAAQ9B,GAAG,CAAC,CAAC,CAAC;oBACrD;oBACA,IACE,OAAO8B,QAAQ7B,KAAK,KAAK,eACzB,OAAO6B,QAAQ7B,KAAK,KAAK,UACzB;wBACA8B,gBAAgBvC,IAAI,CAAC,CAAC,eAAe,EAAEsC,QAAQ7B,KAAK,CAAC,CAAC,CAAC;oBACzD;oBACA,IAAI,OAAO6B,QAAQ7B,KAAK,KAAK,eAAe6B,QAAQ1B,IAAI,KAAK,QAAQ;wBACnE2B,gBAAgBvC,IAAI,CAAC,CAAC,iCAAiC,CAAC;oBAC1D;oBAEA,IAAIuC,gBAAgBjC,MAAM,GAAG,GAAG;wBAC9B+B,gBAAgBrC,IAAI,CAClB,GAAGuC,gBAAgBC,IAAI,CAAC,MAAM,KAAK,EAAElB,KAAKC,SAAS,CAACe,UAAU;oBAElE;gBACF;gBAEA,IAAID,gBAAgB/B,MAAM,GAAG,GAAG;oBAC9B8B,iBAAiB;oBACjB,MAAMK,UAAU,CAAC,IAAI,EAAEJ,gBAAgB/B,MAAM,KAAK,IAAI,KAAK,KAAK;oBAEhEO,QAAQC,KAAK,CACX,CAAC,UAAU,EAAEqB,UAAU,GAAG,EAAEM,QAAQ,GAAG,CAAC,GACtCJ,gBAAgBG,IAAI,CAAC;oBAEzB3B,QAAQC,KAAK;oBACblB,aAAaI,IAAI,CAAC,CAAC,UAAU,EAAEmC,UAAU,GAAG,EAAEM,QAAQ,MAAM,CAAC;gBAC/D;YACF;YACA,OAAOL;QACT;QACA,IAAIH,uBAAuBtC,MAAMI,GAAG,EAAE,QAAQ;YAC5CmB,gBAAgB;QAClB;QACA,IAAIe,uBAAuBtC,MAAM+C,OAAO,EAAE,YAAY;YACpDvB,oBAAoB;QACtB;QAEA,IAAI,CAACxB,MAAMgC,MAAM,EAAE;YACjB/B,aAAaI,IAAI,CAAC;QACpB,OAAO,IAAI,OAAOL,MAAMgC,MAAM,KAAK,UAAU;YAC3C/B,aAAaI,IAAI,CAAC;QACpB,OAAO,IAAI,CAACL,MAAMgC,MAAM,CAACD,UAAU,CAAC,MAAM;YACxC9B,aAAaI,IAAI,CAAC;QACpB;QAEA,IAAIY,SAAS,UAAU;YACrBhB,aAAaI,IAAI,IAAIE,YAAYP;QACnC,OAAO,IAAIiB,SAAS,cAAc;YAChC,IAAI+B,SAAShD;YACb,IAAI,CAACgD,OAAOlB,WAAW,EAAE;gBACvB7B,aAAaI,IAAI,CAAC;YACpB,OAAO,IAAI,OAAO2C,OAAOlB,WAAW,KAAK,UAAU;gBACjD7B,aAAaI,IAAI,CAAC;YACpB,OAAO,IACLY,SAAS,aACT,CAAC+B,OAAOlB,WAAW,CAACmB,KAAK,CAAC,+BAC1B;gBACAhD,aAAaI,IAAI,CACf;YAEJ;QACF;QAEA,IAAIY,SAAS,YAAY;YACvB,MAAMiC,SAASnD,cAAcC;YAC7BE,mBAAmBA,oBAAoBgD,OAAOhD,gBAAgB;YAC9DD,aAAaI,IAAI,IAAI6C,OAAOjD,YAAY;QAC1C;QAEA,IAAIkD;QAEJ,IAAI,OAAOnD,MAAMgC,MAAM,KAAK,YAAYhC,MAAMgC,MAAM,CAACD,UAAU,CAAC,MAAM;YACpE,wDAAwD;YACxD,yBAAyB;YACzB,MAAM,EAAEqB,MAAM,EAAEjC,KAAK,EAAEkC,QAAQ,EAAE,GAAG/D,eAAeU,MAAMgC,MAAM;YAE/D,IAAIb,OAAO;gBACTlB,aAAaI,IAAI,CAAC;YACpB;YAEA,IAAIgD,YAAYA,SAAS1C,MAAM,GAAG,MAAM;gBACtCV,aAAaI,IAAI,CAAC;YACpB;YAEA8C,eAAeC;QACjB;QACA,MAAME,cAAc,IAAI5D;QAExB,IAAIM,MAAMI,GAAG,EAAE;YACb,KAAK,MAAMuC,WAAW3C,MAAMI,GAAG,CAAE;gBAC/B,IAAI,CAACuC,QAAQ7B,KAAK,IAAI6B,QAAQ9B,GAAG,EAAE;oBACjCyC,YAAY5B,GAAG,CAACiB,QAAQ9B,GAAG;gBAC7B;gBAEA,IAAI8B,QAAQ7B,KAAK,EAAE;oBACjB,KAAK,MAAMmC,SAASN,QAAQ7B,KAAK,CAACyC,QAAQ,CAAC5D,kBAAmB;wBAC5D,IAAIsD,KAAK,CAAC,EAAE,EAAE;4BACZK,YAAY5B,GAAG,CAACuB,KAAK,CAAC,EAAE;wBAC1B;oBACF;oBAEA,IAAIN,QAAQ1B,IAAI,KAAK,QAAQ;wBAC3BqC,YAAY5B,GAAG,CAAC;oBAClB;gBACF;YACF;QACF;QAEA,gEAAgE;QAChE,6DAA6D;QAC7D,IAAI,OAAO,AAAC1B,MAAkB8B,WAAW,KAAK,UAAU;YACtD,IACE,AAAC9B,MAAkB8B,WAAW,CAACC,UAAU,CAAC,QAC1CvB,MAAMC,OAAO,CAAC0C,eACd;gBACA,MAAMK,gBAAgB,IAAI9D;gBAE1B,KAAK,MAAM+D,SAASN,aAAc;oBAChC,IAAI,OAAOM,UAAU,YAAY,OAAOA,MAAMC,IAAI,KAAK,UAAU;wBAC/D,MAAMC,eAAe,IAAIC,OAAO,CAAC,CAAC,EAAEH,MAAMC,IAAI,CAAC,OAAO,CAAC;wBACvD,IAAI,AAAC1D,MAAkB8B,WAAW,CAACmB,KAAK,CAACU,eAAe;4BACtDH,cAAc9B,GAAG,CAAC,CAAC,CAAC,EAAE+B,MAAMC,IAAI,EAAE;wBACpC;oBACF;gBACF;gBAEA,IAAIF,cAAcK,IAAI,GAAG,GAAG;oBAC1B5D,aAAaI,IAAI,CACf,CAAC,mCAAmC,EAAE;2BAAImD;qBAAc,CAACX,IAAI,CAC3D,OACC;gBAEP,OAAO;oBACL,MAAM,EACJO,QAAQU,UAAU,EAClBT,UAAUU,YAAY,EACtB5C,OAAO6C,sBAAsB,EAC9B,GAAG1E,eAAe,AAACU,MAAkB8B,WAAW,EAAE;wBACjDmC,WAAW;oBACb;oBAEA,IAAIF,gBAAgBA,aAAapD,MAAM,GAAG,MAAM;wBAC9CV,aAAaI,IAAI,CAAC;oBACpB;oBAEA,IAAI2D,wBAAwB;wBAC1B/D,aAAaI,IAAI,CAAC;oBACpB,OAAO;wBACL,MAAM6D,iBAAiB,IAAIxE,IACzByD,aACGgB,GAAG,CAAC,CAACC,OAAS,OAAOA,SAAS,YAAYA,KAAKV,IAAI,EACnDtB,MAAM,CAACiC;wBAEZ,MAAMC,sBAAsB,IAAI5E;wBAEhC,KAAK,MAAM+D,SAASK,WAAa;4BAC/B,IACE,OAAOL,UAAU,YACjB,CAACS,eAAe9D,GAAG,CAACqD,MAAMC,IAAI,KAC9B,CAACJ,YAAYlD,GAAG,CAACqD,MAAMC,IAAI,GAC3B;gCACAY,oBAAoB5C,GAAG,CAAC+B,MAAMC,IAAI;4BACpC;wBACF;wBAEA,IAAIY,oBAAoBT,IAAI,EAAE;4BAC5B5D,aAAaI,IAAI,CACf,CAAC,2DAA2D,EAAE;mCACzDiE;6BACJ,CAACzB,IAAI,CAAC,MAAM,CAAC,CAAC;wBAEnB;oBACF;gBACF;YACF;QACF;QAEA,MAAM0B,iBAAiBpC,YAAYxB,MAAM,GAAG;QAC5C,MAAM6D,kBAAkBvE,aAAaU,MAAM,GAAG;QAE9C,IAAI4D,kBAAkBC,iBAAiB;YACrCtD,QAAQC,KAAK,CACX,GAAGlB,aAAa4C,IAAI,CAAC,QACnBV,YAAYxB,MAAM,GACd,AAAC6D,CAAAA,kBAAkB,MAAM,EAAC,IAC1B,CAAC,cAAc,EAAErC,YAAYxB,MAAM,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,GACxDwB,YAAYU,IAAI,CAAC,OACjB,GACL,WAAW,EAAElB,KAAKC,SAAS,CAAC5B,QAAQ;YAEvCkB,QAAQC,KAAK;YACbG;QACF;IACF;IAEA,IAAIA,mBAAmB,GAAG;QACxB,IAAIpB,kBAAkB;YACpBgB,QAAQC,KAAK,CACX,CAAC,uCAAuC,EAAE;mBAAI5B;aAAmB,CAACsD,IAAI,CACpE,OACC;QAEP;QACA,IAAItB,eAAe;YACjBL,QAAQC,KAAK,CACX,CAAC,gCAAgC,EAAEQ,KAAKC,SAAS,CAC/C;gBACEX,MAAM;uBAAIxB;iBAAgB,CAACoD,IAAI,CAAC;gBAChChC,KAAK;gBACLC,OAAO;YACT,GACA,MACA,IACC;QAEP;QACA,IAAIU,mBAAmB;YACrBN,QAAQC,KAAK,CACX,CAAC,oCAAoC,EAAEQ,KAAKC,SAAS,CACnD;gBACEX,MAAM;uBAAIxB;iBAAgB,CAACoD,IAAI,CAAC;gBAChChC,KAAK;gBACLC,OAAO;YACT,GACA,MACA,IACC;QAEP;QACAI,QAAQC,KAAK;QACbD,QAAQC,KAAK,CACX,CAAC,eAAe,EAAEF,OAAOK,qBAAqB,IAAI,KAAK,IAAI,MAAM,CAAC;QAEpEF,QAAQC,IAAI,CAAC;IACf;AACF;AAYA,SAASoD,cACPzD,MAAS,EACT0D,MAAkB,EAClBzD,IAAuC;IAEvC,MAAM0D,UAAU3D;IAMhB,MAAM4D,YAA4B,EAAE;IACpC,MAAMC,iBAGD,EAAE;IAEP,IAAIH,OAAOI,IAAI,IAAI7D,SAAS,YAAY;YACnByD;QAAnB,KAAK,MAAMN,QAAQM,EAAAA,eAAAA,OAAOI,IAAI,qBAAXJ,aAAaK,OAAO,KAAI,EAAE,CAAE;YAC7CF,eAAexE,IAAI,CAAC;gBAClBgC,QAAQ+B,KAAKY,aAAa;gBAC1BC,MAAM,CAAC,IAAI,EAAEb,KAAKc,IAAI,GAAG,KAAK,IAAI,GAAG,EAAEd,KAAKe,MAAM,EAAE;YACtD;QACF;QAEAN,eAAexE,IAAI,CAAC;YAClBgC,QAAQqC,OAAOI,IAAI,CAACE,aAAa;YACjCC,MAAM;QACR;IACF;IAEA,KAAK,MAAMG,KAAKT,QAAS;YAGHS;QAFpB,MAAMC,cACJX,OAAO7C,QAAQ,IAAIuD,EAAEvD,QAAQ,KAAK,QAAQ6C,OAAO7C,QAAQ,GAAG;QAC9D,MAAMyD,aAAa,GAACF,iBAAAA,EAAEtD,WAAW,qBAAbsD,eAAerD,UAAU,CAAC;QAC9C,MAAMwD,eAAeF,eAAe,CAACC,aAAaD,cAAc;QAEhE,IAAIX,OAAOI,IAAI,IAAIM,EAAE/C,MAAM,KAAK,OAAO;gBA2BhB+C;YA1BrB,IAAI,CAACE,YAAY;gBACfT,eAAeW,OAAO,CAAC,CAACpB;oBACtB,IAAItC;oBAEJ,IAAIsD,EAAEtD,WAAW,EAAE;wBACjBA,cAAcsC,KAAKa,IAAI,GACnB,GAAGb,KAAKa,IAAI,GAAGM,eAAeH,EAAEtD,WAAW,EAAE,GAC7C,GAAGyD,eAAeH,EAAEtD,WAAW,EAAE;oBACvC;oBAEA8C,UAAUvE,IAAI,CAAC;wBACb,GAAG+E,CAAC;wBACJtD;wBACAE,QAAQ,GAAGqD,YAAY,CAAC,EAAEjB,KAAK/B,MAAM,GACnC+C,EAAEpD,MAAM,KAAK,OAAO,CAAC0C,OAAOe,aAAa,GAAG,KAAKL,EAAEpD,MAAM,EACzD;oBACJ;gBACF;YACF;YAEAoD,EAAEpD,MAAM,GAAG,CAAC,qBAAqB,EAAE0C,OAAOI,IAAI,CAACY,OAAO,CACnDvB,GAAG,CAAC,CAAC9B,SAAmBhD,mBAAmBgD,SAC3CQ,IAAI,CAAC,KAAK,CAAC,EACZuC,EAAEpD,MAAM,KAAK,OAAO,CAAC0C,OAAOe,aAAa,GAAG,KAAKL,EAAEpD,MAAM,EACzD;YAEF,IAAIoD,EAAEtD,WAAW,MAAIsD,kBAAAA,EAAEtD,WAAW,qBAAbsD,gBAAerD,UAAU,CAAC,OAAM;gBACnDqD,EAAEtD,WAAW,GAAG,CAAC,oBAAoB,EACnCsD,EAAEtD,WAAW,KAAK,OAAO,CAAC4C,OAAOe,aAAa,GAAG,KAAKL,EAAEtD,WAAW,EACnE;YACJ;QACF;QACAsD,EAAEpD,MAAM,GAAG,GAAGqD,cACZD,EAAEpD,MAAM,KAAK,OAAOqD,cAAc,KAAKD,EAAEpD,MAAM,EAC/C;QAEF,IAAIoD,EAAEtD,WAAW,EAAE;YACjBsD,EAAEtD,WAAW,GAAG,GAAGyD,eACjBH,EAAEtD,WAAW,KAAK,OAAOyD,eAAe,KAAKH,EAAEtD,WAAW,EAC1D;QACJ;QACA8C,UAAUvE,IAAI,CAAC+E;IACjB;IACA,OAAOR;AACT;AAEA,eAAee,cAAcjB,MAAkB;IAC7C,IAAI,OAAOA,OAAOkB,SAAS,KAAK,YAAY;QAC1C,OAAO,EAAE;IACX;IACA,IAAIA,YAAY,MAAMlB,OAAOkB,SAAS;IACtC,yDAAyD;IACzD,uBAAuB;IACvB7E,kBAAkB6E,WAAW;IAE7B,4CAA4C;IAC5C,IAAIpF,MAAMC,OAAO,CAACmF,YAAY;QAC5BlB,OAAOmB,kBAAkB,GAAGD,UAAUzB,GAAG,CAAC,CAACiB,IAAO,CAAA;gBAAE,GAAGA,CAAC;YAAC,CAAA;IAC3D;IACAQ,YAAYnB,cAAcmB,WAAWlB,QAAQ;IAC7C3D,kBAAkB6E,WAAW;IAC7B,OAAOA;AACT;AAEA,eAAeE,aAAapB,MAAkB;IAC5C,qEAAqE;IACrE,oEAAoE;IACpE,2DAA2D;IAC3D,2FAA2F;IAC3F,mDAAmD;IACnD,IAAIqB,0BAAqC,EAAE;IAC3C,IAAIrB,OAAOsB,WAAW,EAAE;QACtB,IAAIC,SAASvB,OAAOsB,WAAW;QAC/B,IACExG,gBAAgBkF,OAAOsB,WAAW,KAClCE,IAAIC,QAAQ,CAACzB,OAAOsB,WAAW,GAC/B;YACAC,SAAS,IAAIC,IAAIxB,OAAOsB,WAAW,EAAEI,QAAQ;QAC/C;QAEA,IAAIH,UAAUA,WAAW,KAAK;YAC5B,MAAMD,cAAcC,OAAOlE,UAAU,CAAC,OAAOkE,SAAS,CAAC,CAAC,EAAEA,QAAQ;YAClE,MAAMpE,WAAW6C,OAAO7C,QAAQ,IAAI;YACpC,wEAAwE;YACxE,IAAImE,gBAAgBnE,UAAU;gBAC5BkE,wBAAwB1F,IAAI,CAAC;oBAC3B2B,QAAQ,GAAGgE,YAAY,aAAa,CAAC;oBACrClE,aAAa,GAAGD,SAAS,aAAa,CAAC;gBACzC;YACF;QACF;IACF;IAEA,IAAI,OAAO6C,OAAO2B,QAAQ,KAAK,YAAY;QACzC,OAAO;YACLC,aAAa;mBAAIP;aAAwB;YACzCQ,YAAY,EAAE;YACdC,UAAU,EAAE;QACd;IACF;IACA,MAAMC,YAAY,MAAM/B,OAAO2B,QAAQ;IACvC,IAAIC,cAAyB,EAAE;IAC/B,IAAIC,aAAwB,EAAE;IAC9B,IAAIC,WAAsB,EAAE;IAE5B,IACE,CAAChG,MAAMC,OAAO,CAACgG,cACf,OAAOA,cAAc,YACrBvE,OAAOD,IAAI,CAACwE,WAAWC,KAAK,CAC1B,CAAC7F,MACCA,QAAQ,iBAAiBA,QAAQ,gBAAgBA,QAAQ,aAE7D;QACAyF,cAAcG,UAAUH,WAAW,IAAI,EAAE;QACzCC,aAAaE,UAAUF,UAAU,IAAI,EAAE;QACvCC,WAAWC,UAAUD,QAAQ,IAAI,EAAE;IACrC,OAAO;QACLD,aAAaE;IACf;IACA,yDAAyD;IACzD,uBAAuB;IACvB1F,kBAAkBuF,aAAa;IAC/BvF,kBAAkBwF,YAAY;IAC9BxF,kBAAkByF,UAAU;IAE5B,2CAA2C;IAC3C9B,OAAOiC,iBAAiB,GAAG;QACzBL,aAAaA,YAAYnC,GAAG,CAAC,CAACiB,IAAO,CAAA;gBAAE,GAAGA,CAAC;YAAC,CAAA;QAC5CmB,YAAYA,WAAWpC,GAAG,CAAC,CAACiB,IAAO,CAAA;gBAAE,GAAGA,CAAC;YAAC,CAAA;QAC1CoB,UAAUA,SAASrC,GAAG,CAAC,CAACiB,IAAO,CAAA;gBAAE,GAAGA,CAAC;YAAC,CAAA;IACxC;IAEAkB,cAAc;WACTP;WACAtB,cAAc6B,aAAa5B,QAAQ;KACvC;IACD6B,aAAa9B,cAAc8B,YAAY7B,QAAQ;IAC/C8B,WAAW/B,cAAc+B,UAAU9B,QAAQ;IAE3C3D,kBAAkBuF,aAAa;IAC/BvF,kBAAkBwF,YAAY;IAC9BxF,kBAAkByF,UAAU;IAE5B,OAAO;QACLF;QACAC;QACAC;IACF;AACF;AAEA,eAAeI,YAAYlC,MAAkB;IAC3C,IAAI,OAAOA,OAAOhE,OAAO,KAAK,YAAY;QACxC,OAAO,EAAE;IACX;IACA,IAAIA,UAAU,MAAMgE,OAAOhE,OAAO;IAClC,yDAAyD;IACzD,uBAAuB;IACvBK,kBAAkBL,SAAS;IAE3BA,UAAU+D,cAAc/D,SAASgE,QAAQ;IACzC3D,kBAAkBL,SAAS;IAC3B,OAAOA;AACT;AAEA,eAAe,eAAemG,iBAC5BnC,MAAkB;IAElB,MAAM,CAAChE,SAAS2F,UAAUT,UAAU,GAAG,MAAMkB,QAAQC,GAAG,CAAC;QACvDH,YAAYlC;QACZoB,aAAapB;QACbiB,cAAcjB;KACf;IAED,MAAMsC,gBACJX,SAASC,WAAW,CAAC3F,MAAM,GAC3B0F,SAASE,UAAU,CAAC5F,MAAM,GAC1B0F,SAASG,QAAQ,CAAC7F,MAAM;IAE1B,MAAMsG,cAAcvG,QAAQC,MAAM,GAAGiF,UAAUjF,MAAM,GAAGqG;IAExD,IAAIC,cAAc,MAAM;QACtB/F,QAAQgG,IAAI,CACV/H,KAAKC,OAAO,CAAC,SAAS,CAAC,KACrB,CAAC,wFAAwF,CAAC,GAC1F,CAAC,SAAS,EAAEsB,QAAQC,MAAM,CAAC,EAAE,CAAC,GAC9B,CAAC,UAAU,EAAEqG,cAAc,EAAE,CAAC,GAC9B,CAAC,WAAW,EAAEpB,UAAUjF,MAAM,CAAC,EAAE,CAAC,GAClC,CAAC,yEAAyE,CAAC;IAEjF;IAEA,IAAI,CAAC+D,OAAOyC,yBAAyB,EAAE;QACrC,IAAIzC,OAAOe,aAAa,EAAE;YACxBG,UAAUwB,OAAO,CACf;gBACEpF,QAAQ;gBACRF,aAAa;gBACbxB,WAAW;gBACX+B,QAAQqC,OAAOI,IAAI,GAAG,QAAQuC;gBAC9BC,UAAU;gBACV,kDAAkD;gBAClDvE,SAAS;oBACP;wBACE9B,MAAM;wBACNJ,KAAK;oBACP;iBACD;YACH,GACA;gBACEmB,QAAQ;gBACRF,aAAa;gBACbxB,WAAW;gBACX+B,QAAQqC,OAAOI,IAAI,GAAG,QAAQuC;gBAC9BC,UAAU;YACZ;YAEF,IAAI5C,OAAO7C,QAAQ,EAAE;gBACnB+D,UAAUwB,OAAO,CAAC;oBAChBpF,QAAQ0C,OAAO7C,QAAQ;oBACvBC,aAAa4C,OAAO7C,QAAQ,GAAG;oBAC/BvB,WAAW;oBACXuB,UAAU;oBACVQ,QAAQqC,OAAOI,IAAI,GAAG,QAAQuC;oBAC9BC,UAAU;gBACZ;YACF;QACF,OAAO;YACL1B,UAAUwB,OAAO,CAAC;gBAChBpF,QAAQ;gBACRF,aAAa;gBACbxB,WAAW;gBACX+B,QAAQqC,OAAOI,IAAI,GAAG,QAAQuC;gBAC9BC,UAAU;YACZ;YACA,IAAI5C,OAAO7C,QAAQ,EAAE;gBACnB+D,UAAUwB,OAAO,CAAC;oBAChBpF,QAAQ0C,OAAO7C,QAAQ,GAAG;oBAC1BC,aAAa4C,OAAO7C,QAAQ;oBAC5BvB,WAAW;oBACXuB,UAAU;oBACVQ,QAAQqC,OAAOI,IAAI,GAAG,QAAQuC;oBAC9BC,UAAU;gBACZ;YACF;QACF;IACF;IAEA,OAAO;QACL5G;QACA2F;QACAT;IACF;AACF"}