# src/mcp/mcp_server_simple.py
# Version simplifiée du serveur MCP sans dépendances problématiques

import sys
import os
import importlib
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.mcp.fastmcp import FastMCP

# Créer le serveur MCP
mcp = FastMCP("NewsletterAgent")

def safe_import_and_call(module_name, function_name, *args, **kwargs):
    """Import sécurisé avec gestion d'erreur"""
    try:
        module = importlib.import_module(module_name)
        func = getattr(module, function_name)
        return func(*args, **kwargs)
    except Exception as e:
        return f"Erreur lors de l'exécution de {module_name}.{function_name}: {str(e)}"

# 🔧 MCP tools - Version sécurisée

@mcp.tool()
def test_connection() -> str:
    """Test simple de connexion MCP"""
    return "✅ Connexion MCP réussie ! Le serveur NewsletterAgent fonctionne parfaitement."

@mcp.tool()
def scrape_news(theme: str, description: str) -> str:
    """Scraper du contenu sur un thème donné"""
    result = safe_import_and_call('src.tools.scraping_tool', 'scrape_content', theme, description)
    if isinstance(result, list):
        return f"✅ Scraping réussi: {len(result)} articles trouvés sur le thème '{theme}'"
    return str(result)

@mcp.tool()
def vectorize_news(chunks: list[str]) -> str:
    """Vectoriser les données scrapées"""
    result = safe_import_and_call('src.tools.vectorisation_tool', 'vectorize_scraped_data', chunks)
    if "Erreur" not in str(result):
        return f"✅ Vectorisation réussie: {len(chunks)} documents vectorisés."
    return str(result)

@mcp.tool()
def generate_newsletter(chunks: list[str], theme: str) -> str:
    """Générer une newsletter à partir de chunks"""
    result = safe_import_and_call('src.tools.newsletter_generator', 'generate_newsletter_with_context', chunks, theme)
    if "Erreur" not in str(result):
        return f"✅ Newsletter générée avec succès sur le thème '{theme}'"
    return str(result)

@mcp.tool()
def revise_newsletter(original_newsletter: str, feedback: str) -> str:
    """Réviser une newsletter avec des commentaires"""
    try:
        # Import des modules nécessaires
        revise_module = importlib.import_module('src.prompts.revise_prompt')
        generator_module = importlib.import_module('src.tools.newsletter_generator')
        
        get_prompt = getattr(revise_module, 'get_prompt')
        generate_from_prompt = getattr(generator_module, 'generate_newsletter_with_context')
        
        prompt = get_prompt(original_newsletter, feedback)
        result = generate_from_prompt([prompt])
        return f"✅ Newsletter révisée avec succès selon le feedback fourni"
    except Exception as e:
        return f"Erreur lors de la révision: {str(e)}"

@mcp.tool()
def generate_newsletter_from_file(text: str, theme: str, description: str) -> str:
    """Générer une newsletter à partir d'un fichier"""
    result = safe_import_and_call('src.rag.file_to_newsletter', 'generate_newsletter_from_uploaded_file', text, theme, description)
    if "Erreur" not in str(result):
        return f"✅ Newsletter générée avec succès à partir du fichier sur le thème '{theme}'"
    return str(result)

@mcp.tool()
def list_available_tools() -> str:
    """Lister tous les outils disponibles"""
    tools = [
        "test_connection - Test de connexion MCP",
        "scrape_news - Scraper du contenu sur un thème",
        "vectorize_news - Vectoriser les données scrapées", 
        "generate_newsletter - Générer une newsletter",
        "revise_newsletter - Réviser une newsletter",
        "generate_newsletter_from_file - Générer depuis un fichier",
        "list_available_tools - Lister les outils disponibles"
    ]
    return "🛠️ Outils MCP disponibles:\n" + "\n".join(f"• {tool}" for tool in tools)

# 🚀 Démarrer le serveur
if __name__ == "__main__":
    print("🚀 Démarrage du serveur MCP NewsletterAgent...")
    print("📡 En attente de requêtes JSON sur stdin...")
    mcp.run(transport="stdio")
