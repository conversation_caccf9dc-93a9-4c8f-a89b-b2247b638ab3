{"version": 3, "sources": ["../../../../src/server/lib/router-utils/build-prefetch-segment-data-route.ts"], "sourcesContent": ["import path from '../../../shared/lib/isomorphic/path'\nimport { normalizePagePath } from '../../../shared/lib/page-path/normalize-page-path'\nimport { getNamedRouteRegex } from '../../../shared/lib/router/utils/route-regex'\nimport {\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n} from '../../../lib/constants'\n\nexport const SEGMENT_PATH_KEY = 'nextSegmentPath'\n\nexport type PrefetchSegmentDataRoute = {\n  source: string\n  destination: string\n}\n\nexport function buildPrefetchSegmentDataRoute(\n  page: string,\n  segmentPath: string\n): PrefetchSegmentDataRoute {\n  const pagePath = normalizePagePath(page)\n\n  const destination = path.posix.join(\n    `${pagePath}${RSC_SEGMENTS_DIR_SUFFIX}`,\n    `${segmentPath}${RSC_SEGMENT_SUFFIX}`\n  )\n\n  const { namedRegex } = getNamedRouteRegex(destination, {\n    prefixRouteKeys: true,\n    includePrefix: true,\n    includeSuffix: true,\n    excludeOptionalTrailingSlash: true,\n    backreferenceDuplicateKeys: true,\n  })\n\n  return {\n    destination,\n    source: namedRegex,\n  }\n}\n"], "names": ["SEGMENT_PATH_KEY", "buildPrefetchSegmentDataRoute", "page", "segmentPath", "pagePath", "normalizePagePath", "destination", "path", "posix", "join", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "namedRegex", "getNamedRouteRegex", "prefixRouteKeys", "includePrefix", "includeSuffix", "excludeOptionalTrailingSlash", "backreferenceDuplicateKeys", "source"], "mappings": ";;;;;;;;;;;;;;;IAQaA,gBAAgB;eAAhBA;;IAOGC,6BAA6B;eAA7BA;;;6DAfC;mCACiB;4BACC;2BAI5B;;;;;;AAEA,MAAMD,mBAAmB;AAOzB,SAASC,8BACdC,IAAY,EACZC,WAAmB;IAEnB,MAAMC,WAAWC,IAAAA,oCAAiB,EAACH;IAEnC,MAAMI,cAAcC,aAAI,CAACC,KAAK,CAACC,IAAI,CACjC,GAAGL,WAAWM,kCAAuB,EAAE,EACvC,GAAGP,cAAcQ,6BAAkB,EAAE;IAGvC,MAAM,EAAEC,UAAU,EAAE,GAAGC,IAAAA,8BAAkB,EAACP,aAAa;QACrDQ,iBAAiB;QACjBC,eAAe;QACfC,eAAe;QACfC,8BAA8B;QAC9BC,4BAA4B;IAC9B;IAEA,OAAO;QACLZ;QACAa,QAAQP;IACV;AACF"}