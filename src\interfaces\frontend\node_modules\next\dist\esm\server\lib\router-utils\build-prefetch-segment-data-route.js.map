{"version": 3, "sources": ["../../../../src/server/lib/router-utils/build-prefetch-segment-data-route.ts"], "sourcesContent": ["import path from '../../../shared/lib/isomorphic/path'\nimport { normalizePagePath } from '../../../shared/lib/page-path/normalize-page-path'\nimport { getNamedRouteRegex } from '../../../shared/lib/router/utils/route-regex'\nimport {\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n} from '../../../lib/constants'\n\nexport const SEGMENT_PATH_KEY = 'nextSegmentPath'\n\nexport type PrefetchSegmentDataRoute = {\n  source: string\n  destination: string\n}\n\nexport function buildPrefetchSegmentDataRoute(\n  page: string,\n  segmentPath: string\n): PrefetchSegmentDataRoute {\n  const pagePath = normalizePagePath(page)\n\n  const destination = path.posix.join(\n    `${pagePath}${RSC_SEGMENTS_DIR_SUFFIX}`,\n    `${segmentPath}${RSC_SEGMENT_SUFFIX}`\n  )\n\n  const { namedRegex } = getNamedRouteRegex(destination, {\n    prefixRouteKeys: true,\n    includePrefix: true,\n    includeSuffix: true,\n    excludeOptionalTrailingSlash: true,\n    backreferenceDuplicateKeys: true,\n  })\n\n  return {\n    destination,\n    source: namedRegex,\n  }\n}\n"], "names": ["path", "normalizePagePath", "getNamedRouteRegex", "RSC_SEGMENT_SUFFIX", "RSC_SEGMENTS_DIR_SUFFIX", "SEGMENT_PATH_KEY", "buildPrefetchSegmentDataRoute", "page", "segmentPath", "pagePath", "destination", "posix", "join", "namedRegex", "prefixRouteKeys", "includePrefix", "includeSuffix", "excludeOptionalTrailingSlash", "backreferenceDuplicateKeys", "source"], "mappings": "AAAA,OAAOA,UAAU,sCAAqC;AACtD,SAASC,iBAAiB,QAAQ,oDAAmD;AACrF,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SACEC,kBAAkB,EAClBC,uBAAuB,QAClB,yBAAwB;AAE/B,OAAO,MAAMC,mBAAmB,kBAAiB;AAOjD,OAAO,SAASC,8BACdC,IAAY,EACZC,WAAmB;IAEnB,MAAMC,WAAWR,kBAAkBM;IAEnC,MAAMG,cAAcV,KAAKW,KAAK,CAACC,IAAI,CACjC,GAAGH,WAAWL,yBAAyB,EACvC,GAAGI,cAAcL,oBAAoB;IAGvC,MAAM,EAAEU,UAAU,EAAE,GAAGX,mBAAmBQ,aAAa;QACrDI,iBAAiB;QACjBC,eAAe;QACfC,eAAe;QACfC,8BAA8B;QAC9BC,4BAA4B;IAC9B;IAEA,OAAO;QACLR;QACAS,QAAQN;IACV;AACF"}