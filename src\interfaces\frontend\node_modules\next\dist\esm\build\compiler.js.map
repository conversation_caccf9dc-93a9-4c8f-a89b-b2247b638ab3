{"version": 3, "sources": ["../../src/build/compiler.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { Span } from '../trace'\nimport getWebpackBundler from '../shared/lib/get-webpack-bundler'\n\nexport type CompilerResult = {\n  errors: webpack.StatsError[]\n  warnings: webpack.StatsError[]\n  stats: webpack.Stats | undefined\n}\n\nfunction generateStats(\n  result: CompilerResult,\n  stat: webpack.Stats\n): CompilerResult {\n  const { errors, warnings } = stat.toJson({\n    preset: 'errors-warnings',\n    moduleTrace: true,\n  })\n  if (errors && errors.length > 0) {\n    result.errors.push(...errors)\n  }\n\n  if (warnings && warnings.length > 0) {\n    result.warnings.push(...warnings)\n  }\n\n  return result\n}\n\n// Webpack 5 requires the compiler to be closed (to save caches)\n// Webpack 4 does not have this close method so in order to be backwards compatible we check if it exists\nfunction closeCompiler(compiler: webpack.Compiler | webpack.MultiCompiler) {\n  return new Promise<void>((resolve, reject) => {\n    // @ts-ignore Close only exists on the compiler in webpack 5\n    return compiler.close((err: any) => (err ? reject(err) : resolve()))\n  })\n}\n\nexport function runCompiler(\n  config: webpack.Configuration,\n  {\n    runWebpackSpan,\n    inputFileSystem,\n  }: {\n    runWebpackSpan: Span\n    inputFileSystem?: webpack.Compiler['inputFileSystem']\n  }\n): Promise<\n  [\n    result: CompilerResult,\n    inputFileSystem?: webpack.Compiler['inputFileSystem'],\n  ]\n> {\n  return new Promise((resolve, reject) => {\n    const compiler = getWebpackBundler()(config)\n\n    // Ensure we use the previous inputFileSystem\n    if (inputFileSystem) {\n      compiler.inputFileSystem = inputFileSystem\n    }\n    compiler.fsStartTime = Date.now()\n    compiler.run((err, stats) => {\n      const webpackCloseSpan = runWebpackSpan.traceChild('webpack-close', {\n        name: config.name || 'unknown',\n      })\n      webpackCloseSpan\n        .traceAsyncFn(() => closeCompiler(compiler))\n        .then(() => {\n          if (err) {\n            const reason = err.stack ?? err.toString()\n            if (reason) {\n              return resolve([\n                {\n                  errors: [{ message: reason, details: (err as any).details }],\n                  warnings: [],\n                  stats,\n                },\n                compiler.inputFileSystem,\n              ])\n            }\n            return reject(err)\n          } else if (!stats) throw new Error('No Stats from webpack')\n\n          const result = webpackCloseSpan\n            .traceChild('webpack-generate-error-stats')\n            .traceFn(() =>\n              generateStats({ errors: [], warnings: [], stats }, stats)\n            )\n          return resolve([result, compiler.inputFileSystem])\n        })\n    })\n  })\n}\n"], "names": ["getWebpackBundler", "generateStats", "result", "stat", "errors", "warnings", "to<PERSON><PERSON>", "preset", "moduleTrace", "length", "push", "closeCompiler", "compiler", "Promise", "resolve", "reject", "close", "err", "runCompiler", "config", "runWebpackSpan", "inputFileSystem", "fsStartTime", "Date", "now", "run", "stats", "webpackCloseSpan", "<PERSON><PERSON><PERSON><PERSON>", "name", "traceAsyncFn", "then", "reason", "stack", "toString", "message", "details", "Error", "traceFn"], "mappings": "AAEA,OAAOA,uBAAuB,oCAAmC;AAQjE,SAASC,cACPC,MAAsB,EACtBC,IAAmB;IAEnB,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE,GAAGF,KAAKG,MAAM,CAAC;QACvCC,QAAQ;QACRC,aAAa;IACf;IACA,IAAIJ,UAAUA,OAAOK,MAAM,GAAG,GAAG;QAC/BP,OAAOE,MAAM,CAACM,IAAI,IAAIN;IACxB;IAEA,IAAIC,YAAYA,SAASI,MAAM,GAAG,GAAG;QACnCP,OAAOG,QAAQ,CAACK,IAAI,IAAIL;IAC1B;IAEA,OAAOH;AACT;AAEA,gEAAgE;AAChE,yGAAyG;AACzG,SAASS,cAAcC,QAAkD;IACvE,OAAO,IAAIC,QAAc,CAACC,SAASC;QACjC,4DAA4D;QAC5D,OAAOH,SAASI,KAAK,CAAC,CAACC,MAAcA,MAAMF,OAAOE,OAAOH;IAC3D;AACF;AAEA,OAAO,SAASI,YACdC,MAA6B,EAC7B,EACEC,cAAc,EACdC,eAAe,EAIhB;IAOD,OAAO,IAAIR,QAAQ,CAACC,SAASC;QAC3B,MAAMH,WAAWZ,oBAAoBmB;QAErC,6CAA6C;QAC7C,IAAIE,iBAAiB;YACnBT,SAASS,eAAe,GAAGA;QAC7B;QACAT,SAASU,WAAW,GAAGC,KAAKC,GAAG;QAC/BZ,SAASa,GAAG,CAAC,CAACR,KAAKS;YACjB,MAAMC,mBAAmBP,eAAeQ,UAAU,CAAC,iBAAiB;gBAClEC,MAAMV,OAAOU,IAAI,IAAI;YACvB;YACAF,iBACGG,YAAY,CAAC,IAAMnB,cAAcC,WACjCmB,IAAI,CAAC;gBACJ,IAAId,KAAK;oBACP,MAAMe,SAASf,IAAIgB,KAAK,IAAIhB,IAAIiB,QAAQ;oBACxC,IAAIF,QAAQ;wBACV,OAAOlB,QAAQ;4BACb;gCACEV,QAAQ;oCAAC;wCAAE+B,SAASH;wCAAQI,SAAS,AAACnB,IAAYmB,OAAO;oCAAC;iCAAE;gCAC5D/B,UAAU,EAAE;gCACZqB;4BACF;4BACAd,SAASS,eAAe;yBACzB;oBACH;oBACA,OAAON,OAAOE;gBAChB,OAAO,IAAI,CAACS,OAAO,MAAM,qBAAkC,CAAlC,IAAIW,MAAM,0BAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiC;gBAE1D,MAAMnC,SAASyB,iBACZC,UAAU,CAAC,gCACXU,OAAO,CAAC,IACPrC,cAAc;wBAAEG,QAAQ,EAAE;wBAAEC,UAAU,EAAE;wBAAEqB;oBAAM,GAAGA;gBAEvD,OAAOZ,QAAQ;oBAACZ;oBAAQU,SAASS,eAAe;iBAAC;YACnD;QACJ;IACF;AACF"}