{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/css-loader/src/camelcase.ts"], "sourcesContent": ["/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (https://sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\nconst preserveCamelCase = (string: string, locale: string) => {\n  let isLastCharLower = false\n  let isLastCharUpper = false\n  let isLastLastCharUpper = false\n\n  for (let i = 0; i < string.length; i++) {\n    const character = string[i]\n\n    if (isLastCharLower && /[\\p{Lu}]/u.test(character)) {\n      string = string.slice(0, i) + '-' + string.slice(i)\n      isLastCharLower = false\n      isLastLastCharUpper = isLastCharUpper\n      isLastCharUpper = true\n      i++\n    } else if (\n      isLastCharUpper &&\n      isLastLastCharUpper &&\n      /[\\p{Ll}]/u.test(character)\n    ) {\n      string = string.slice(0, i - 1) + '-' + string.slice(i - 1)\n      isLastLastCharUpper = isLastCharUpper\n      isLastCharUpper = false\n      isLastCharLower = true\n    } else {\n      isLastCharLower =\n        character.toLocaleLowerCase(locale) === character &&\n        character.toLocaleUpperCase(locale) !== character\n      isLastLastCharUpper = isLastCharUpper\n      isLastCharUpper =\n        character.toLocaleUpperCase(locale) === character &&\n        character.toLocaleLowerCase(locale) !== character\n    }\n  }\n\n  return string\n}\n\nconst preserveConsecutiveUppercase = (input: string) => {\n  return input.replace(/^[\\p{Lu}](?![\\p{Lu}])/gu, (m1) => m1.toLowerCase())\n}\n\nconst postProcess = (input: string, options: { locale: string }) => {\n  return input\n    .replace(/[_.\\- ]+([\\p{Alpha}\\p{N}_]|$)/gu, (_, p1) =>\n      p1.toLocaleUpperCase(options.locale)\n    )\n    .replace(/\\d+([\\p{Alpha}\\p{N}_]|$)/gu, (m) =>\n      m.toLocaleUpperCase(options.locale)\n    )\n}\n\nconst camelCase = (input: string | string[], options?: any) => {\n  if (!(typeof input === 'string' || Array.isArray(input))) {\n    throw new TypeError('Expected the input to be `string | string[]`')\n  }\n\n  options = {\n    pascalCase: false,\n    preserveConsecutiveUppercase: false,\n    ...options,\n  }\n\n  if (Array.isArray(input)) {\n    input = input\n      .map((x) => x.trim())\n      .filter((x) => x.length)\n      .join('-')\n  } else {\n    input = input.trim()\n  }\n\n  if (input.length === 0) {\n    return ''\n  }\n\n  if (input.length === 1) {\n    return options.pascalCase\n      ? input.toLocaleUpperCase(options.locale)\n      : input.toLocaleLowerCase(options.locale)\n  }\n\n  const hasUpperCase = input !== input.toLocaleLowerCase(options.locale)\n\n  if (hasUpperCase) {\n    input = preserveCamelCase(input, options.locale)\n  }\n\n  input = input.replace(/^[_.\\- ]+/, '')\n\n  if (options.preserveConsecutiveUppercase) {\n    input = preserveConsecutiveUppercase(input)\n  } else {\n    input = input.toLocaleLowerCase()\n  }\n\n  if (options.pascalCase) {\n    input = input.charAt(0).toLocaleUpperCase(options.locale) + input.slice(1)\n  }\n\n  return postProcess(input, options)\n}\n\nexport default camelCase\n"], "names": ["preserveCamelCase", "string", "locale", "isLastCharLower", "isLastCharUpper", "isLastLastCharUpper", "i", "length", "character", "test", "slice", "toLocaleLowerCase", "toLocaleUpperCase", "preserveConsecutiveUppercase", "input", "replace", "m1", "toLowerCase", "postProcess", "options", "_", "p1", "m", "camelCase", "Array", "isArray", "TypeError", "pascalCase", "map", "x", "trim", "filter", "join", "hasUpperCase", "char<PERSON>t"], "mappings": "AAAA;;;;;;;;;;AAUA,GAEA,MAAMA,oBAAoB,CAACC,QAAgBC;IACzC,IAAIC,kBAAkB;IACtB,IAAIC,kBAAkB;IACtB,IAAIC,sBAAsB;IAE1B,IAAK,IAAIC,IAAI,GAAGA,IAAIL,OAAOM,MAAM,EAAED,IAAK;QACtC,MAAME,YAAYP,MAAM,CAACK,EAAE;QAE3B,IAAIH,mBAAmB,YAAYM,IAAI,CAACD,YAAY;YAClDP,SAASA,OAAOS,KAAK,CAAC,GAAGJ,KAAK,MAAML,OAAOS,KAAK,CAACJ;YACjDH,kBAAkB;YAClBE,sBAAsBD;YACtBA,kBAAkB;YAClBE;QACF,OAAO,IACLF,mBACAC,uBACA,YAAYI,IAAI,CAACD,YACjB;YACAP,SAASA,OAAOS,KAAK,CAAC,GAAGJ,IAAI,KAAK,MAAML,OAAOS,KAAK,CAACJ,IAAI;YACzDD,sBAAsBD;YACtBA,kBAAkB;YAClBD,kBAAkB;QACpB,OAAO;YACLA,kBACEK,UAAUG,iBAAiB,CAACT,YAAYM,aACxCA,UAAUI,iBAAiB,CAACV,YAAYM;YAC1CH,sBAAsBD;YACtBA,kBACEI,UAAUI,iBAAiB,CAACV,YAAYM,aACxCA,UAAUG,iBAAiB,CAACT,YAAYM;QAC5C;IACF;IAEA,OAAOP;AACT;AAEA,MAAMY,+BAA+B,CAACC;IACpC,OAAOA,MAAMC,OAAO,CAAC,2BAA2B,CAACC,KAAOA,GAAGC,WAAW;AACxE;AAEA,MAAMC,cAAc,CAACJ,OAAeK;IAClC,OAAOL,MACJC,OAAO,CAAC,mCAAmC,CAACK,GAAGC,KAC9CA,GAAGT,iBAAiB,CAACO,QAAQjB,MAAM,GAEpCa,OAAO,CAAC,8BAA8B,CAACO,IACtCA,EAAEV,iBAAiB,CAACO,QAAQjB,MAAM;AAExC;AAEA,MAAMqB,YAAY,CAACT,OAA0BK;IAC3C,IAAI,CAAE,CAAA,OAAOL,UAAU,YAAYU,MAAMC,OAAO,CAACX,MAAK,GAAI;QACxD,MAAM,qBAA6D,CAA7D,IAAIY,UAAU,iDAAd,qBAAA;mBAAA;wBAAA;0BAAA;QAA4D;IACpE;IAEAP,UAAU;QACRQ,YAAY;QACZd,8BAA8B;QAC9B,GAAGM,OAAO;IACZ;IAEA,IAAIK,MAAMC,OAAO,CAACX,QAAQ;QACxBA,QAAQA,MACLc,GAAG,CAAC,CAACC,IAAMA,EAAEC,IAAI,IACjBC,MAAM,CAAC,CAACF,IAAMA,EAAEtB,MAAM,EACtByB,IAAI,CAAC;IACV,OAAO;QACLlB,QAAQA,MAAMgB,IAAI;IACpB;IAEA,IAAIhB,MAAMP,MAAM,KAAK,GAAG;QACtB,OAAO;IACT;IAEA,IAAIO,MAAMP,MAAM,KAAK,GAAG;QACtB,OAAOY,QAAQQ,UAAU,GACrBb,MAAMF,iBAAiB,CAACO,QAAQjB,MAAM,IACtCY,MAAMH,iBAAiB,CAACQ,QAAQjB,MAAM;IAC5C;IAEA,MAAM+B,eAAenB,UAAUA,MAAMH,iBAAiB,CAACQ,QAAQjB,MAAM;IAErE,IAAI+B,cAAc;QAChBnB,QAAQd,kBAAkBc,OAAOK,QAAQjB,MAAM;IACjD;IAEAY,QAAQA,MAAMC,OAAO,CAAC,aAAa;IAEnC,IAAII,QAAQN,4BAA4B,EAAE;QACxCC,QAAQD,6BAA6BC;IACvC,OAAO;QACLA,QAAQA,MAAMH,iBAAiB;IACjC;IAEA,IAAIQ,QAAQQ,UAAU,EAAE;QACtBb,QAAQA,MAAMoB,MAAM,CAAC,GAAGtB,iBAAiB,CAACO,QAAQjB,MAAM,IAAIY,MAAMJ,KAAK,CAAC;IAC1E;IAEA,OAAOQ,YAAYJ,OAAOK;AAC5B;AAEA,eAAeI,UAAS"}