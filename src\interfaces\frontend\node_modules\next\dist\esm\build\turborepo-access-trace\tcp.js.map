{"version": 3, "sources": ["../../../src/build/turborepo-access-trace/tcp.ts"], "sourcesContent": ["import net from 'net'\nimport type { Addresses, RestoreOriginalFunction } from './types'\n\n/**\n * Proxy the TCP connect method to determine if any network access is made during the build\n *\n * @param addresses An array to track the addresses that are accessed.\n * @returns A function that restores the original connect method.\n */\nexport function tcpProxy(addresses: Addresses): RestoreOriginalFunction {\n  // net.Socket docs https://nodejs.org/api/net.html#class-netsocket\n  const originalConnect = net.Socket.prototype.connect\n\n  // Override the connect method\n  net.Socket.prototype.connect = function (...args: any) {\n    // First, check if the first argument is an object and not null\n    if (typeof args[0] === 'object' && args[0] !== null) {\n      const options = args[0] as net.SocketConnectOpts\n\n      // check if the options has what we need\n      if (\n        'port' in options &&\n        options.port !== undefined &&\n        'host' in options &&\n        options.host !== undefined\n      ) {\n        addresses.push({ addr: options.host, port: options.port.toString() })\n      }\n    }\n\n    return originalConnect.apply(this, args)\n  }\n\n  return () => {\n    // Restore the original connect method\n    net.Socket.prototype.connect = originalConnect\n  }\n}\n"], "names": ["net", "tcpProxy", "addresses", "originalConnect", "Socket", "prototype", "connect", "args", "options", "port", "undefined", "host", "push", "addr", "toString", "apply"], "mappings": "AAAA,OAAOA,SAAS,MAAK;AAGrB;;;;;CAKC,GACD,OAAO,SAASC,SAASC,SAAoB;IAC3C,kEAAkE;IAClE,MAAMC,kBAAkBH,IAAII,MAAM,CAACC,SAAS,CAACC,OAAO;IAEpD,8BAA8B;IAC9BN,IAAII,MAAM,CAACC,SAAS,CAACC,OAAO,GAAG,SAAU,GAAGC,IAAS;QACnD,+DAA+D;QAC/D,IAAI,OAAOA,IAAI,CAAC,EAAE,KAAK,YAAYA,IAAI,CAAC,EAAE,KAAK,MAAM;YACnD,MAAMC,UAAUD,IAAI,CAAC,EAAE;YAEvB,wCAAwC;YACxC,IACE,UAAUC,WACVA,QAAQC,IAAI,KAAKC,aACjB,UAAUF,WACVA,QAAQG,IAAI,KAAKD,WACjB;gBACAR,UAAUU,IAAI,CAAC;oBAAEC,MAAML,QAAQG,IAAI;oBAAEF,MAAMD,QAAQC,IAAI,CAACK,QAAQ;gBAAG;YACrE;QACF;QAEA,OAAOX,gBAAgBY,KAAK,CAAC,IAAI,EAAER;IACrC;IAEA,OAAO;QACL,sCAAsC;QACtCP,IAAII,MAAM,CAACC,SAAS,CAACC,OAAO,GAAGH;IACjC;AACF"}