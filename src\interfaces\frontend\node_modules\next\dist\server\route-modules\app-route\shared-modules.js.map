{"version": 3, "sources": ["../../../../src/server/route-modules/app-route/shared-modules.ts"], "sourcesContent": ["// the name of the export has to be the camelCase version of the file name (without the extension)\n// TODO: remove this. We need it because using notFound from next/navigation imports this file :(\nexport * as appRouterContext from '../../../shared/lib/app-router-context.shared-runtime'\n"], "names": ["appRouterContext"], "mappings": "AAAA,kGAAkG;AAClG,iGAAiG;;;;;+BACrFA;;;;;;uFAAsB"}