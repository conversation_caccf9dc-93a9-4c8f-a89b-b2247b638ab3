{"version": 3, "sources": ["../../../src/shared/lib/loadable-context.shared-runtime.ts"], "sourcesContent": ["'use client'\n\nimport React from 'react'\n\ntype CaptureFn = (moduleName: string) => void\n\nexport const LoadableContext = React.createContext<CaptureFn | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  LoadableContext.displayName = 'LoadableContext'\n}\n"], "names": ["LoadableContext", "React", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAAA;;;;;+BAMaA;;;eAAAA;;;;gEAJK;AAIX,MAAMA,kBAAkBC,cAAK,CAACC,aAAa,CAAmB;AAErE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCL,gBAAgBM,WAAW,GAAG;AAChC"}