{"version": 3, "sources": ["../../src/build/create-compiler-aliases.ts"], "sourcesContent": ["import path from 'path'\nimport * as React from 'react'\nimport {\n  DOT_NEXT_ALIAS,\n  PAGES_DIR_ALIAS,\n  ROOT_DIR_ALIAS,\n  APP_DIR_ALIAS,\n  RSC_ACTION_PROXY_ALIAS,\n  RSC_ACTION_CLIENT_WRAPPER_ALIAS,\n  RSC_ACTION_VALIDATE_ALIAS,\n  RSC_ACTION_ENCRYPTION_ALIAS,\n  RSC_CACHE_WRAPPER_ALIAS,\n  type WebpackLayerName,\n} from '../lib/constants'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport { defaultOverrides } from '../server/require-hook'\nimport { hasExternalOtelApiPackage } from './webpack-config'\nimport { NEXT_PROJECT_ROOT } from './next-dir-paths'\nimport { WEBPACK_LAYERS } from '../lib/constants'\nimport { isWebpackServerOnlyLayer } from './utils'\n\ninterface CompilerAliases {\n  [alias: string]: string | string[]\n}\n\nconst isReact19 = typeof React.use === 'function'\n\nexport function createWebpackAliases({\n  distDir,\n  isClient,\n  isEdgeServer,\n  isNodeServer,\n  dev,\n  config,\n  pagesDir,\n  appDir,\n  dir,\n  reactProductionProfiling,\n  hasRewrites,\n}: {\n  distDir: string\n  isClient: boolean\n  isEdgeServer: boolean\n  isNodeServer: boolean\n  dev: boolean\n  config: NextConfigComplete\n  pagesDir: string | undefined\n  appDir: string | undefined\n  dir: string\n  reactProductionProfiling: boolean\n  hasRewrites: boolean\n}): CompilerAliases {\n  const pageExtensions = config.pageExtensions\n  const clientResolveRewrites = require.resolve(\n    '../shared/lib/router/utils/resolve-rewrites'\n  )\n  const customAppAliases: CompilerAliases = {}\n  const customDocumentAliases: CompilerAliases = {}\n\n  // tell webpack where to look for _app and _document\n  // using aliases to allow falling back to the default\n  // version when removed or not present\n  if (dev) {\n    const nextDistPath = 'next/dist/' + (isEdgeServer ? 'esm/' : '')\n    customAppAliases[`${PAGES_DIR_ALIAS}/_app`] = [\n      ...(pagesDir\n        ? pageExtensions.reduce((prev, ext) => {\n            prev.push(path.join(pagesDir, `_app.${ext}`))\n            return prev\n          }, [] as string[])\n        : []),\n      `${nextDistPath}pages/_app.js`,\n    ]\n    customAppAliases[`${PAGES_DIR_ALIAS}/_error`] = [\n      ...(pagesDir\n        ? pageExtensions.reduce((prev, ext) => {\n            prev.push(path.join(pagesDir, `_error.${ext}`))\n            return prev\n          }, [] as string[])\n        : []),\n      `${nextDistPath}pages/_error.js`,\n    ]\n    customDocumentAliases[`${PAGES_DIR_ALIAS}/_document`] = [\n      ...(pagesDir\n        ? pageExtensions.reduce((prev, ext) => {\n            prev.push(path.join(pagesDir, `_document.${ext}`))\n            return prev\n          }, [] as string[])\n        : []),\n      `${nextDistPath}pages/_document.js`,\n    ]\n  }\n\n  return {\n    '@vercel/og$': 'next/dist/server/og/image-response',\n\n    // Avoid bundling both entrypoints in React 19 when we just need one.\n    // Also avoids bundler warnings in React 18 where react-dom/server.edge doesn't exist.\n    'next/dist/server/ReactDOMServerPages': isReact19\n      ? 'react-dom/server.edge'\n      : 'react-dom/server.browser',\n\n    // Alias next/dist imports to next/dist/esm assets,\n    // let this alias hit before `next` alias.\n    ...(isEdgeServer\n      ? {\n          'next/dist/api': 'next/dist/esm/api',\n          'next/dist/build': 'next/dist/esm/build',\n          'next/dist/client': 'next/dist/esm/client',\n          'next/dist/shared': 'next/dist/esm/shared',\n          'next/dist/pages': 'next/dist/esm/pages',\n          'next/dist/lib': 'next/dist/esm/lib',\n          'next/dist/server': 'next/dist/esm/server',\n\n          ...createNextApiEsmAliases(),\n        }\n      : undefined),\n\n    // For RSC server bundle\n    ...(!hasExternalOtelApiPackage() && {\n      '@opentelemetry/api': 'next/dist/compiled/@opentelemetry/api',\n    }),\n\n    ...(config.images.loaderFile\n      ? {\n          'next/dist/shared/lib/image-loader': config.images.loaderFile,\n          ...(isEdgeServer && {\n            'next/dist/esm/shared/lib/image-loader': config.images.loaderFile,\n          }),\n        }\n      : undefined),\n\n    'styled-jsx/style$': defaultOverrides['styled-jsx/style'],\n    'styled-jsx$': defaultOverrides['styled-jsx'],\n\n    ...customAppAliases,\n    ...customDocumentAliases,\n\n    ...(pagesDir ? { [PAGES_DIR_ALIAS]: pagesDir } : {}),\n    ...(appDir ? { [APP_DIR_ALIAS]: appDir } : {}),\n    [ROOT_DIR_ALIAS]: dir,\n    ...(isClient\n      ? {\n          'private-next-instrumentation-client': [\n            path.join(dir, 'src', 'instrumentation-client'),\n            path.join(dir, 'instrumentation-client'),\n            'private-next-empty-module',\n          ],\n\n          // disable typechecker, webpack5 allows aliases to be set to false to create a no-op module\n          'private-next-empty-module': false as any,\n        }\n      : {}),\n\n    [DOT_NEXT_ALIAS]: distDir,\n    ...(isClient || isEdgeServer ? getOptimizedModuleAliases() : {}),\n    ...(reactProductionProfiling ? getReactProfilingInProduction() : {}),\n\n    // For Node server, we need to re-alias the package imports to prefer to\n    // resolve to the ESM export.\n    ...(isNodeServer\n      ? getBarrelOptimizationAliases(\n          config.experimental.optimizePackageImports || []\n        )\n      : {}),\n\n    [RSC_ACTION_VALIDATE_ALIAS]:\n      'next/dist/build/webpack/loaders/next-flight-loader/action-validate',\n\n    [RSC_ACTION_CLIENT_WRAPPER_ALIAS]:\n      'next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper',\n\n    [RSC_ACTION_PROXY_ALIAS]:\n      'next/dist/build/webpack/loaders/next-flight-loader/server-reference',\n\n    [RSC_ACTION_ENCRYPTION_ALIAS]: 'next/dist/server/app-render/encryption',\n\n    [RSC_CACHE_WRAPPER_ALIAS]:\n      'next/dist/build/webpack/loaders/next-flight-loader/cache-wrapper',\n\n    ...(isClient || isEdgeServer\n      ? {\n          [clientResolveRewrites]: hasRewrites\n            ? clientResolveRewrites\n            : // With webpack 5 an alias can be pointed to false to noop\n              false,\n        }\n      : {}),\n\n    '@swc/helpers/_': path.join(\n      path.dirname(require.resolve('@swc/helpers/package.json')),\n      '_'\n    ),\n\n    setimmediate: 'next/dist/compiled/setimmediate',\n  }\n}\n\nexport function createServerOnlyClientOnlyAliases(\n  isServer: boolean\n): CompilerAliases {\n  return isServer\n    ? {\n        'server-only$': 'next/dist/compiled/server-only/empty',\n        'client-only$': 'next/dist/compiled/client-only/error',\n        'next/dist/compiled/server-only$':\n          'next/dist/compiled/server-only/empty',\n        'next/dist/compiled/client-only$':\n          'next/dist/compiled/client-only/error',\n      }\n    : {\n        'server-only$': 'next/dist/compiled/server-only/index',\n        'client-only$': 'next/dist/compiled/client-only/index',\n        'next/dist/compiled/client-only$':\n          'next/dist/compiled/client-only/index',\n        'next/dist/compiled/server-only':\n          'next/dist/compiled/server-only/index',\n      }\n}\n\nexport function createNextApiEsmAliases() {\n  const mapping = {\n    head: 'next/dist/api/head',\n    image: 'next/dist/api/image',\n    constants: 'next/dist/api/constants',\n    router: 'next/dist/api/router',\n    dynamic: 'next/dist/api/dynamic',\n    script: 'next/dist/api/script',\n    link: 'next/dist/api/link',\n    form: 'next/dist/api/form',\n    navigation: 'next/dist/api/navigation',\n    headers: 'next/dist/api/headers',\n    og: 'next/dist/api/og',\n    server: 'next/dist/api/server',\n    // pages api\n    document: 'next/dist/api/document',\n    app: 'next/dist/api/app',\n  }\n  const aliasMap: Record<string, string> = {}\n  // Handle fully specified imports like `next/image.js`\n  for (const [key, value] of Object.entries(mapping)) {\n    const nextApiFilePath = path.join(NEXT_PROJECT_ROOT, key)\n    aliasMap[nextApiFilePath + '.js'] = value\n  }\n\n  return aliasMap\n}\n\nexport function createAppRouterApiAliases(isServerOnlyLayer: boolean) {\n  const mapping: Record<string, string> = {\n    head: 'next/dist/client/components/noop-head',\n    dynamic: 'next/dist/api/app-dynamic',\n    link: 'next/dist/client/app-dir/link',\n    form: 'next/dist/client/app-dir/form',\n  }\n\n  if (isServerOnlyLayer) {\n    mapping['navigation'] = 'next/dist/api/navigation.react-server'\n  }\n\n  const aliasMap: Record<string, string> = {}\n  for (const [key, value] of Object.entries(mapping)) {\n    const nextApiFilePath = path.join(NEXT_PROJECT_ROOT, key)\n    aliasMap[nextApiFilePath + '.js'] = value\n  }\n  return aliasMap\n}\n\nexport function createRSCAliases(\n  bundledReactChannel: string,\n  {\n    layer,\n    isEdgeServer,\n    reactProductionProfiling,\n  }: {\n    layer: WebpackLayerName\n    isEdgeServer: boolean\n    reactProductionProfiling: boolean\n  }\n): CompilerAliases {\n  const isServerOnlyLayer = isWebpackServerOnlyLayer(layer)\n  // For middleware, instrumentation layers, treat them as rsc layer.\n  // Since we only built the runtime package for rsc, convert everything to rsc\n  // to ensure the runtime modules path existed.\n  if (isServerOnlyLayer) {\n    layer = WEBPACK_LAYERS.reactServerComponents\n  }\n\n  let alias: Record<string, string> = {\n    react$: `next/dist/compiled/react${bundledReactChannel}`,\n    'react-dom$': `next/dist/compiled/react-dom${bundledReactChannel}`,\n    'react/jsx-runtime$': `next/dist/compiled/react${bundledReactChannel}/jsx-runtime`,\n    'react/jsx-dev-runtime$': `next/dist/compiled/react${bundledReactChannel}/jsx-dev-runtime`,\n    'react/compiler-runtime$': `next/dist/compiled/react${bundledReactChannel}/compiler-runtime`,\n    'react-dom/client$': `next/dist/compiled/react-dom${bundledReactChannel}/client`,\n    'react-dom/server$': `next/dist/compiled/react-dom${bundledReactChannel}/server`,\n    'react-dom/server.browser$': `next/dist/compiled/react-dom${bundledReactChannel}/server.browser`,\n    'react-dom/static$': `next/dist/compiled/react-dom${bundledReactChannel}/static`,\n    'react-dom/static.edge$': `next/dist/compiled/react-dom${bundledReactChannel}/static.edge`,\n    'react-dom/static.browser$': `next/dist/compiled/react-dom${bundledReactChannel}/static.browser`,\n    // optimizations to ignore the legacy build of react-dom/server in `server.edge` build\n    'react-dom/server.edge$': `next/dist/build/webpack/alias/react-dom-server-edge${bundledReactChannel}.js`,\n    // react-server-dom-webpack alias\n    'react-server-dom-webpack/client$': `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/client`,\n    'react-server-dom-webpack/client.edge$': `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/client.edge`,\n    'react-server-dom-webpack/server.edge$': `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/server.edge`,\n    'react-server-dom-webpack/server.node$': `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/server.node`,\n    'react-server-dom-webpack/static.edge$': `next/dist/compiled/react-server-dom-webpack${bundledReactChannel}/static.edge`,\n  }\n\n  if (!isEdgeServer) {\n    if (layer === WEBPACK_LAYERS.serverSideRendering) {\n      alias = Object.assign(alias, {\n        'react/jsx-runtime$': `next/dist/server/route-modules/app-page/vendored/${layer}/react-jsx-runtime`,\n        'react/jsx-dev-runtime$': `next/dist/server/route-modules/app-page/vendored/${layer}/react-jsx-dev-runtime`,\n        'react/compiler-runtime$': `next/dist/server/route-modules/app-page/vendored/${layer}/react-compiler-runtime`,\n        react$: `next/dist/server/route-modules/app-page/vendored/${layer}/react`,\n        'react-dom$': `next/dist/server/route-modules/app-page/vendored/${layer}/react-dom`,\n        'react-server-dom-webpack/client.edge$': `next/dist/server/route-modules/app-page/vendored/${layer}/react-server-dom-webpack-client-edge`,\n      })\n    } else if (layer === WEBPACK_LAYERS.reactServerComponents) {\n      alias = Object.assign(alias, {\n        'react/jsx-runtime$': `next/dist/server/route-modules/app-page/vendored/${layer}/react-jsx-runtime`,\n        'react/jsx-dev-runtime$': `next/dist/server/route-modules/app-page/vendored/${layer}/react-jsx-dev-runtime`,\n        'react/compiler-runtime$': `next/dist/server/route-modules/app-page/vendored/${layer}/react-compiler-runtime`,\n        react$: `next/dist/server/route-modules/app-page/vendored/${layer}/react`,\n        'react-dom$': `next/dist/server/route-modules/app-page/vendored/${layer}/react-dom`,\n        'react-server-dom-webpack/server.edge$': `next/dist/server/route-modules/app-page/vendored/${layer}/react-server-dom-webpack-server-edge`,\n        'react-server-dom-webpack/server.node$': `next/dist/server/route-modules/app-page/vendored/${layer}/react-server-dom-webpack-server-node`,\n        'react-server-dom-webpack/static.edge$': `next/dist/server/route-modules/app-page/vendored/${layer}/react-server-dom-webpack-static-edge`,\n      })\n    }\n  }\n\n  if (isEdgeServer) {\n    if (layer === WEBPACK_LAYERS.reactServerComponents) {\n      alias = Object.assign(alias, {\n        react$: `next/dist/compiled/react${bundledReactChannel}/react.react-server`,\n        'next/dist/compiled/react$': `next/dist/compiled/react${bundledReactChannel}/react.react-server`,\n        'next/dist/compiled/react-experimental$': `next/dist/compiled/react-experimental/react.react-server`,\n        'react/jsx-runtime$': `next/dist/compiled/react${bundledReactChannel}/jsx-runtime.react-server`,\n        'react/compiler-runtime$': `next/dist/compiled/react${bundledReactChannel}/compiler-runtime`,\n        'next/dist/compiled/react/jsx-runtime$': `next/dist/compiled/react${bundledReactChannel}/jsx-runtime.react-server`,\n        'next/dist/compiled/react-experimental/jsx-runtime$': `next/dist/compiled/react-experimental/jsx-runtime.react-server`,\n        'react/jsx-dev-runtime$': `next/dist/compiled/react${bundledReactChannel}/jsx-dev-runtime.react-server`,\n        'next/dist/compiled/react/jsx-dev-runtime$': `next/dist/compiled/react${bundledReactChannel}/jsx-dev-runtime.react-server`,\n        'next/dist/compiled/react-experimental/jsx-dev-runtime$': `next/dist/compiled/react-experimental/jsx-dev-runtime.react-server`,\n        'react-dom$': `next/dist/compiled/react-dom${bundledReactChannel}/react-dom.react-server`,\n        'next/dist/compiled/react-dom$': `next/dist/compiled/react-dom${bundledReactChannel}/react-dom.react-server`,\n        'next/dist/compiled/react-dom-experimental$': `next/dist/compiled/react-dom-experimental/react-dom.react-server`,\n      })\n    }\n  }\n\n  if (reactProductionProfiling) {\n    alias['react-dom/client$'] =\n      `next/dist/compiled/react-dom${bundledReactChannel}/profiling`\n  }\n\n  alias[\n    '@vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts'\n  ] = `next/dist/client/dev/noop-turbopack-hmr`\n\n  return alias\n}\n\n// Insert aliases for Next.js stubs of fetch, object-assign, and url\n// Keep in sync with insert_optimized_module_aliases in import_map.rs\nexport function getOptimizedModuleAliases(): CompilerAliases {\n  return {\n    unfetch: require.resolve('next/dist/build/polyfills/fetch/index.js'),\n    'isomorphic-unfetch': require.resolve(\n      'next/dist/build/polyfills/fetch/index.js'\n    ),\n    'whatwg-fetch': require.resolve(\n      'next/dist/build/polyfills/fetch/whatwg-fetch.js'\n    ),\n    'object-assign': require.resolve(\n      'next/dist/build/polyfills/object-assign.js'\n    ),\n    'object.assign/auto': require.resolve(\n      'next/dist/build/polyfills/object.assign/auto.js'\n    ),\n    'object.assign/implementation': require.resolve(\n      'next/dist/build/polyfills/object.assign/implementation.js'\n    ),\n    'object.assign/polyfill': require.resolve(\n      'next/dist/build/polyfills/object.assign/polyfill.js'\n    ),\n    'object.assign/shim': require.resolve(\n      'next/dist/build/polyfills/object.assign/shim.js'\n    ),\n    url: require.resolve('next/dist/compiled/native-url'),\n  }\n}\n\n// Alias these modules to be resolved with \"module\" if possible.\nfunction getBarrelOptimizationAliases(packages: string[]): CompilerAliases {\n  const aliases: { [pkg: string]: string } = {}\n  const mainFields = ['module', 'main']\n\n  for (const pkg of packages) {\n    try {\n      const descriptionFileData = require(`${pkg}/package.json`)\n      const descriptionFilePath = require.resolve(`${pkg}/package.json`)\n\n      for (const field of mainFields) {\n        if (descriptionFileData.hasOwnProperty(field)) {\n          aliases[pkg + '$'] = path.join(\n            path.dirname(descriptionFilePath),\n            descriptionFileData[field]\n          )\n          break\n        }\n      }\n    } catch {}\n  }\n\n  return aliases\n}\nfunction getReactProfilingInProduction(): CompilerAliases {\n  return {\n    'react-dom/client$': 'react-dom/profiling',\n  }\n}\n"], "names": ["createAppRouterApiAliases", "createNextApiEsmAliases", "createRSCAliases", "createServerOnlyClientOnlyAliases", "createWebpackAliases", "getOptimizedModuleAliases", "isReact19", "React", "use", "distDir", "isClient", "isEdgeServer", "isNodeServer", "dev", "config", "pagesDir", "appDir", "dir", "reactProductionProfiling", "hasRewrites", "pageExtensions", "clientResolveRewrites", "require", "resolve", "customAppAliases", "customDocumentAliases", "nextDistPath", "PAGES_DIR_ALIAS", "reduce", "prev", "ext", "push", "path", "join", "undefined", "hasExternalOtelApiPackage", "images", "loaderFile", "defaultOverrides", "APP_DIR_ALIAS", "ROOT_DIR_ALIAS", "DOT_NEXT_ALIAS", "getReactProfilingInProduction", "getBarrelOptimizationAliases", "experimental", "optimizePackageImports", "RSC_ACTION_VALIDATE_ALIAS", "RSC_ACTION_CLIENT_WRAPPER_ALIAS", "RSC_ACTION_PROXY_ALIAS", "RSC_ACTION_ENCRYPTION_ALIAS", "RSC_CACHE_WRAPPER_ALIAS", "dirname", "setimmediate", "isServer", "mapping", "head", "image", "constants", "router", "dynamic", "script", "link", "form", "navigation", "headers", "og", "server", "document", "app", "aliasMap", "key", "value", "Object", "entries", "nextApiFilePath", "NEXT_PROJECT_ROOT", "isServerOnlyLayer", "bundledReactChannel", "layer", "isWebpackServerOnlyLayer", "WEBPACK_LAYERS", "reactServerComponents", "alias", "react$", "serverSideRendering", "assign", "unfetch", "url", "packages", "aliases", "mainFields", "pkg", "descriptionFileData", "descriptionFilePath", "field", "hasOwnProperty"], "mappings": ";;;;;;;;;;;;;;;;;;;IAwPgBA,yBAAyB;eAAzBA;;IA5BAC,uBAAuB;eAAvBA;;IAgDAC,gBAAgB;eAAhBA;;IAtEAC,iCAAiC;eAAjCA;;IA3KAC,oBAAoB;eAApBA;;IAqVAC,yBAAyB;eAAzBA;;;6DAhXC;+DACM;2BAYhB;6BAE0B;+BACS;8BACR;uBAEO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC,MAAMC,YAAY,OAAOC,OAAMC,GAAG,KAAK;AAEhC,SAASJ,qBAAqB,EACnCK,OAAO,EACPC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,GAAG,EACHC,wBAAwB,EACxBC,WAAW,EAaZ;IACC,MAAMC,iBAAiBN,OAAOM,cAAc;IAC5C,MAAMC,wBAAwBC,QAAQC,OAAO,CAC3C;IAEF,MAAMC,mBAAoC,CAAC;IAC3C,MAAMC,wBAAyC,CAAC;IAEhD,oDAAoD;IACpD,qDAAqD;IACrD,sCAAsC;IACtC,IAAIZ,KAAK;QACP,MAAMa,eAAe,eAAgBf,CAAAA,eAAe,SAAS,EAAC;QAC9Da,gBAAgB,CAAC,GAAGG,0BAAe,CAAC,KAAK,CAAC,CAAC,GAAG;eACxCZ,WACAK,eAAeQ,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACC,aAAI,CAACC,IAAI,CAAClB,UAAU,CAAC,KAAK,EAAEe,KAAK;gBAC3C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,GAAGH,aAAa,aAAa,CAAC;SAC/B;QACDF,gBAAgB,CAAC,GAAGG,0BAAe,CAAC,OAAO,CAAC,CAAC,GAAG;eAC1CZ,WACAK,eAAeQ,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACC,aAAI,CAACC,IAAI,CAAClB,UAAU,CAAC,OAAO,EAAEe,KAAK;gBAC7C,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,GAAGH,aAAa,eAAe,CAAC;SACjC;QACDD,qBAAqB,CAAC,GAAGE,0BAAe,CAAC,UAAU,CAAC,CAAC,GAAG;eAClDZ,WACAK,eAAeQ,MAAM,CAAC,CAACC,MAAMC;gBAC3BD,KAAKE,IAAI,CAACC,aAAI,CAACC,IAAI,CAAClB,UAAU,CAAC,UAAU,EAAEe,KAAK;gBAChD,OAAOD;YACT,GAAG,EAAE,IACL,EAAE;YACN,GAAGH,aAAa,kBAAkB,CAAC;SACpC;IACH;IAEA,OAAO;QACL,eAAe;QAEf,qEAAqE;QACrE,sFAAsF;QACtF,wCAAwCpB,YACpC,0BACA;QAEJ,mDAAmD;QACnD,0CAA0C;QAC1C,GAAIK,eACA;YACE,iBAAiB;YACjB,mBAAmB;YACnB,oBAAoB;YACpB,oBAAoB;YACpB,mBAAmB;YACnB,iBAAiB;YACjB,oBAAoB;YAEpB,GAAGV,yBAAyB;QAC9B,IACAiC,SAAS;QAEb,wBAAwB;QACxB,GAAI,CAACC,IAAAA,wCAAyB,OAAM;YAClC,sBAAsB;QACxB,CAAC;QAED,GAAIrB,OAAOsB,MAAM,CAACC,UAAU,GACxB;YACE,qCAAqCvB,OAAOsB,MAAM,CAACC,UAAU;YAC7D,GAAI1B,gBAAgB;gBAClB,yCAAyCG,OAAOsB,MAAM,CAACC,UAAU;YACnE,CAAC;QACH,IACAH,SAAS;QAEb,qBAAqBI,6BAAgB,CAAC,mBAAmB;QACzD,eAAeA,6BAAgB,CAAC,aAAa;QAE7C,GAAGd,gBAAgB;QACnB,GAAGC,qBAAqB;QAExB,GAAIV,WAAW;YAAE,CAACY,0BAAe,CAAC,EAAEZ;QAAS,IAAI,CAAC,CAAC;QACnD,GAAIC,SAAS;YAAE,CAACuB,wBAAa,CAAC,EAAEvB;QAAO,IAAI,CAAC,CAAC;QAC7C,CAACwB,yBAAc,CAAC,EAAEvB;QAClB,GAAIP,WACA;YACE,uCAAuC;gBACrCsB,aAAI,CAACC,IAAI,CAAChB,KAAK,OAAO;gBACtBe,aAAI,CAACC,IAAI,CAAChB,KAAK;gBACf;aACD;YAED,2FAA2F;YAC3F,6BAA6B;QAC/B,IACA,CAAC,CAAC;QAEN,CAACwB,yBAAc,CAAC,EAAEhC;QAClB,GAAIC,YAAYC,eAAeN,8BAA8B,CAAC,CAAC;QAC/D,GAAIa,2BAA2BwB,kCAAkC,CAAC,CAAC;QAEnE,wEAAwE;QACxE,6BAA6B;QAC7B,GAAI9B,eACA+B,6BACE7B,OAAO8B,YAAY,CAACC,sBAAsB,IAAI,EAAE,IAElD,CAAC,CAAC;QAEN,CAACC,oCAAyB,CAAC,EACzB;QAEF,CAACC,0CAA+B,CAAC,EAC/B;QAEF,CAACC,iCAAsB,CAAC,EACtB;QAEF,CAACC,sCAA2B,CAAC,EAAE;QAE/B,CAACC,kCAAuB,CAAC,EACvB;QAEF,GAAIxC,YAAYC,eACZ;YACE,CAACU,sBAAsB,EAAEF,cACrBE,wBAEA;QACN,IACA,CAAC,CAAC;QAEN,kBAAkBW,aAAI,CAACC,IAAI,CACzBD,aAAI,CAACmB,OAAO,CAAC7B,QAAQC,OAAO,CAAC,+BAC7B;QAGF6B,cAAc;IAChB;AACF;AAEO,SAASjD,kCACdkD,QAAiB;IAEjB,OAAOA,WACH;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,mCACE;IACJ,IACA;QACE,gBAAgB;QAChB,gBAAgB;QAChB,mCACE;QACF,kCACE;IACJ;AACN;AAEO,SAASpD;IACd,MAAMqD,UAAU;QACdC,MAAM;QACNC,OAAO;QACPC,WAAW;QACXC,QAAQ;QACRC,SAAS;QACTC,QAAQ;QACRC,MAAM;QACNC,MAAM;QACNC,YAAY;QACZC,SAAS;QACTC,IAAI;QACJC,QAAQ;QACR,YAAY;QACZC,UAAU;QACVC,KAAK;IACP;IACA,MAAMC,WAAmC,CAAC;IAC1C,sDAAsD;IACtD,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACnB,SAAU;QAClD,MAAMoB,kBAAkB1C,aAAI,CAACC,IAAI,CAAC0C,+BAAiB,EAAEL;QACrDD,QAAQ,CAACK,kBAAkB,MAAM,GAAGH;IACtC;IAEA,OAAOF;AACT;AAEO,SAASrE,0BAA0B4E,iBAA0B;IAClE,MAAMtB,UAAkC;QACtCC,MAAM;QACNI,SAAS;QACTE,MAAM;QACNC,MAAM;IACR;IAEA,IAAIc,mBAAmB;QACrBtB,OAAO,CAAC,aAAa,GAAG;IAC1B;IAEA,MAAMe,WAAmC,CAAC;IAC1C,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACnB,SAAU;QAClD,MAAMoB,kBAAkB1C,aAAI,CAACC,IAAI,CAAC0C,+BAAiB,EAAEL;QACrDD,QAAQ,CAACK,kBAAkB,MAAM,GAAGH;IACtC;IACA,OAAOF;AACT;AAEO,SAASnE,iBACd2E,mBAA2B,EAC3B,EACEC,KAAK,EACLnE,YAAY,EACZO,wBAAwB,EAKzB;IAED,MAAM0D,oBAAoBG,IAAAA,+BAAwB,EAACD;IACnD,mEAAmE;IACnE,6EAA6E;IAC7E,8CAA8C;IAC9C,IAAIF,mBAAmB;QACrBE,QAAQE,yBAAc,CAACC,qBAAqB;IAC9C;IAEA,IAAIC,QAAgC;QAClCC,QAAQ,CAAC,wBAAwB,EAAEN,qBAAqB;QACxD,cAAc,CAAC,4BAA4B,EAAEA,qBAAqB;QAClE,sBAAsB,CAAC,wBAAwB,EAAEA,oBAAoB,YAAY,CAAC;QAClF,0BAA0B,CAAC,wBAAwB,EAAEA,oBAAoB,gBAAgB,CAAC;QAC1F,2BAA2B,CAAC,wBAAwB,EAAEA,oBAAoB,iBAAiB,CAAC;QAC5F,qBAAqB,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;QAChF,qBAAqB,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;QAChF,6BAA6B,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;QAChG,qBAAqB,CAAC,4BAA4B,EAAEA,oBAAoB,OAAO,CAAC;QAChF,0BAA0B,CAAC,4BAA4B,EAAEA,oBAAoB,YAAY,CAAC;QAC1F,6BAA6B,CAAC,4BAA4B,EAAEA,oBAAoB,eAAe,CAAC;QAChG,sFAAsF;QACtF,0BAA0B,CAAC,mDAAmD,EAAEA,oBAAoB,GAAG,CAAC;QACxG,iCAAiC;QACjC,oCAAoC,CAAC,2CAA2C,EAAEA,oBAAoB,OAAO,CAAC;QAC9G,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;QACxH,yCAAyC,CAAC,2CAA2C,EAAEA,oBAAoB,YAAY,CAAC;IAC1H;IAEA,IAAI,CAAClE,cAAc;QACjB,IAAImE,UAAUE,yBAAc,CAACI,mBAAmB,EAAE;YAChDF,QAAQV,OAAOa,MAAM,CAACH,OAAO;gBAC3B,sBAAsB,CAAC,iDAAiD,EAAEJ,MAAM,kBAAkB,CAAC;gBACnG,0BAA0B,CAAC,iDAAiD,EAAEA,MAAM,sBAAsB,CAAC;gBAC3G,2BAA2B,CAAC,iDAAiD,EAAEA,MAAM,uBAAuB,CAAC;gBAC7GK,QAAQ,CAAC,iDAAiD,EAAEL,MAAM,MAAM,CAAC;gBACzE,cAAc,CAAC,iDAAiD,EAAEA,MAAM,UAAU,CAAC;gBACnF,yCAAyC,CAAC,iDAAiD,EAAEA,MAAM,qCAAqC,CAAC;YAC3I;QACF,OAAO,IAAIA,UAAUE,yBAAc,CAACC,qBAAqB,EAAE;YACzDC,QAAQV,OAAOa,MAAM,CAACH,OAAO;gBAC3B,sBAAsB,CAAC,iDAAiD,EAAEJ,MAAM,kBAAkB,CAAC;gBACnG,0BAA0B,CAAC,iDAAiD,EAAEA,MAAM,sBAAsB,CAAC;gBAC3G,2BAA2B,CAAC,iDAAiD,EAAEA,MAAM,uBAAuB,CAAC;gBAC7GK,QAAQ,CAAC,iDAAiD,EAAEL,MAAM,MAAM,CAAC;gBACzE,cAAc,CAAC,iDAAiD,EAAEA,MAAM,UAAU,CAAC;gBACnF,yCAAyC,CAAC,iDAAiD,EAAEA,MAAM,qCAAqC,CAAC;gBACzI,yCAAyC,CAAC,iDAAiD,EAAEA,MAAM,qCAAqC,CAAC;gBACzI,yCAAyC,CAAC,iDAAiD,EAAEA,MAAM,qCAAqC,CAAC;YAC3I;QACF;IACF;IAEA,IAAInE,cAAc;QAChB,IAAImE,UAAUE,yBAAc,CAACC,qBAAqB,EAAE;YAClDC,QAAQV,OAAOa,MAAM,CAACH,OAAO;gBAC3BC,QAAQ,CAAC,wBAAwB,EAAEN,oBAAoB,mBAAmB,CAAC;gBAC3E,6BAA6B,CAAC,wBAAwB,EAAEA,oBAAoB,mBAAmB,CAAC;gBAChG,0CAA0C,CAAC,wDAAwD,CAAC;gBACpG,sBAAsB,CAAC,wBAAwB,EAAEA,oBAAoB,yBAAyB,CAAC;gBAC/F,2BAA2B,CAAC,wBAAwB,EAAEA,oBAAoB,iBAAiB,CAAC;gBAC5F,yCAAyC,CAAC,wBAAwB,EAAEA,oBAAoB,yBAAyB,CAAC;gBAClH,sDAAsD,CAAC,8DAA8D,CAAC;gBACtH,0BAA0B,CAAC,wBAAwB,EAAEA,oBAAoB,6BAA6B,CAAC;gBACvG,6CAA6C,CAAC,wBAAwB,EAAEA,oBAAoB,6BAA6B,CAAC;gBAC1H,0DAA0D,CAAC,kEAAkE,CAAC;gBAC9H,cAAc,CAAC,4BAA4B,EAAEA,oBAAoB,uBAAuB,CAAC;gBACzF,iCAAiC,CAAC,4BAA4B,EAAEA,oBAAoB,uBAAuB,CAAC;gBAC5G,8CAA8C,CAAC,gEAAgE,CAAC;YAClH;QACF;IACF;IAEA,IAAI3D,0BAA0B;QAC5BgE,KAAK,CAAC,oBAAoB,GACxB,CAAC,4BAA4B,EAAEL,oBAAoB,UAAU,CAAC;IAClE;IAEAK,KAAK,CACH,4EACD,GAAG,CAAC,uCAAuC,CAAC;IAE7C,OAAOA;AACT;AAIO,SAAS7E;IACd,OAAO;QACLiF,SAAShE,QAAQC,OAAO,CAAC;QACzB,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gBAAgBD,QAAQC,OAAO,CAC7B;QAEF,iBAAiBD,QAAQC,OAAO,CAC9B;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEF,gCAAgCD,QAAQC,OAAO,CAC7C;QAEF,0BAA0BD,QAAQC,OAAO,CACvC;QAEF,sBAAsBD,QAAQC,OAAO,CACnC;QAEFgE,KAAKjE,QAAQC,OAAO,CAAC;IACvB;AACF;AAEA,gEAAgE;AAChE,SAASoB,6BAA6B6C,QAAkB;IACtD,MAAMC,UAAqC,CAAC;IAC5C,MAAMC,aAAa;QAAC;QAAU;KAAO;IAErC,KAAK,MAAMC,OAAOH,SAAU;QAC1B,IAAI;YACF,MAAMI,sBAAsBtE,QAAQ,GAAGqE,IAAI,aAAa,CAAC;YACzD,MAAME,sBAAsBvE,QAAQC,OAAO,CAAC,GAAGoE,IAAI,aAAa,CAAC;YAEjE,KAAK,MAAMG,SAASJ,WAAY;gBAC9B,IAAIE,oBAAoBG,cAAc,CAACD,QAAQ;oBAC7CL,OAAO,CAACE,MAAM,IAAI,GAAG3D,aAAI,CAACC,IAAI,CAC5BD,aAAI,CAACmB,OAAO,CAAC0C,sBACbD,mBAAmB,CAACE,MAAM;oBAE5B;gBACF;YACF;QACF,EAAE,OAAM,CAAC;IACX;IAEA,OAAOL;AACT;AACA,SAAS/C;IACP,OAAO;QACL,qBAAqB;IACvB;AACF"}