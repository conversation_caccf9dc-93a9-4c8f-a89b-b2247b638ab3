{"version": 3, "sources": ["../../../src/server/app-render/clean-async-snapshot-instance.ts"], "sourcesContent": ["import { createSnapshot } from '../app-render/async-local-storage'\n\nexport const runInCleanSnapshot: <R, TArgs extends any[]>(\n  fn: (...args: TArgs) => R,\n  ...args: TArgs\n) => R = createSnapshot()\n"], "names": ["runInCleanSnapshot", "createSnapshot"], "mappings": ";;;;+BAEaA;;;eAAAA;;;mCAFkB;AAExB,MAAMA,qBAGJC,IAAAA,iCAAc"}