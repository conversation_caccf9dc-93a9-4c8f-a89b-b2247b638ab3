{"version": 3, "sources": ["../../src/server/base-server.ts"], "sourcesContent": ["import type { __ApiPreviewProps } from './api-utils'\nimport type { LoadComponentsReturnType } from './load-components'\nimport type { MiddlewareRouteMatch } from '../shared/lib/router/utils/middleware-route-matcher'\nimport type { Params } from './request/params'\nimport {\n  type FallbackRouteParams,\n  getFallbackRouteParams,\n} from './request/fallback-params'\nimport type { NextConfig, NextConfigComplete } from './config-shared'\nimport type {\n  NextParsedUrlQuery,\n  NextUrlWithParsedQuery,\n  RequestMeta,\n} from './request-meta'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { RenderOptsPartial as PagesRenderOptsPartial } from './render'\nimport type {\n  RenderOptsPartial as AppRenderOptsPartial,\n  ServerOnInstrumentationRequestError,\n} from './app-render/types'\nimport {\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type ServerComponentsHmrCache,\n  type ResponseCacheBase,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n  CachedRouteKind,\n  type CachedRedirectValue,\n} from './response-cache'\nimport type { UrlWithParsedQuery } from 'url'\nimport {\n  NormalizeError,\n  DecodeError,\n  normalizeRepeatedSlashes,\n  MissingStaticPage,\n} from '../shared/lib/utils'\nimport type { PreviewData } from '../types'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport type { BaseNextRequest, BaseNextResponse } from './base-http'\nimport type {\n  ManifestRewriteRoute,\n  ManifestRoute,\n  PrerenderManifest,\n} from '../build'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport type {\n  AppPageRouteHandlerContext,\n  AppPageRouteModule,\n} from './route-modules/app-page/module'\nimport type { PagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type { AppRouteRouteHandlerContext } from './route-modules/app-route/module'\nimport type {\n  Server as HTTPServer,\n  IncomingMessage,\n  ServerResponse as HTTPServerResponse,\n} from 'http'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport type { TLSSocket } from 'tls'\nimport type { PathnameNormalizer } from './normalizers/request/pathname-normalizer'\nimport type { InstrumentationModule } from './instrumentation/types'\n\nimport { format as formatUrl, parse as parseUrl } from 'url'\nimport { formatHostname } from './lib/format-hostname'\nimport { getRedirectStatus } from '../lib/redirect-status'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport {\n  APP_PATHS_MANIFEST,\n  NEXT_BUILTIN_DOCUMENT,\n  PAGES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n} from '../shared/lib/constants'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { checkIsOnDemandRevalidate } from './api-utils'\nimport { setConfig } from '../shared/lib/runtime-config.external'\nimport { getCacheControlHeader, type CacheControl } from './lib/cache-control'\nimport { execOnce } from '../shared/lib/utils'\nimport { isBlockedPage } from './utils'\nimport { getBotType, isBot } from '../shared/lib/router/utils/is-bot'\nimport RenderResult from './render-result'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport * as Log from '../build/output/log'\nimport { getPreviouslyRevalidatedTags, getUtils } from './server-utils'\nimport isError, { getProperError } from '../lib/is-error'\nimport {\n  addRequestMeta,\n  getRequestMeta,\n  removeRequestMeta,\n  setRequestMeta,\n} from './request-meta'\nimport { removePathPrefix } from '../shared/lib/router/utils/remove-path-prefix'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport { getHostname } from '../shared/lib/get-hostname'\nimport { parseUrl as parseUrlUtil } from '../shared/lib/router/utils/parse-url'\nimport { getNextPathnameInfo } from '../shared/lib/router/utils/get-next-pathname-info'\nimport {\n  RSC_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_URL,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n} from '../client/components/app-router-headers'\nimport type {\n  MatchOptions,\n  RouteMatcherManager,\n} from './route-matcher-managers/route-matcher-manager'\nimport { LocaleRouteNormalizer } from './normalizers/locale-route-normalizer'\nimport { DefaultRouteMatcherManager } from './route-matcher-managers/default-route-matcher-manager'\nimport { AppPageRouteMatcherProvider } from './route-matcher-providers/app-page-route-matcher-provider'\nimport { AppRouteRouteMatcherProvider } from './route-matcher-providers/app-route-route-matcher-provider'\nimport { PagesAPIRouteMatcherProvider } from './route-matcher-providers/pages-api-route-matcher-provider'\nimport { PagesRouteMatcherProvider } from './route-matcher-providers/pages-route-matcher-provider'\nimport { ServerManifestLoader } from './route-matcher-providers/helpers/manifest-loaders/server-manifest-loader'\nimport { getTracer, isBubbledError, SpanKind } from './lib/trace/tracer'\nimport { BaseServerSpan } from './lib/trace/constants'\nimport { I18NProvider } from './lib/i18n-provider'\nimport { sendResponse } from './send-response'\nimport {\n  fromNodeOutgoingHttpHeaders,\n  normalizeNextQueryParam,\n  toNodeOutgoingHttpHeaders,\n} from './web/utils'\nimport {\n  CACHE_ONE_YEAR,\n  INFINITE_CACHE,\n  MATCHED_PATH_HEADER,\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_RESUME_HEADER,\n} from '../lib/constants'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport {\n  NextRequestAdapter,\n  signalFromNodeResponse,\n} from './web/spec-extension/adapters/next-request'\nimport { matchNextDataPathname } from './lib/match-next-data-pathname'\nimport getRouteFromAssetPath from '../shared/lib/router/utils/get-route-from-asset-path'\nimport { decodePathParams } from './lib/router-utils/decode-path-params'\nimport { RSCPathnameNormalizer } from './normalizers/request/rsc'\nimport { stripFlightHeaders } from './app-render/strip-flight-headers'\nimport {\n  isAppPageRouteModule,\n  isAppRouteRouteModule,\n  isPagesRouteModule,\n} from './route-modules/checks'\nimport { PrefetchRSCPathnameNormalizer } from './normalizers/request/prefetch-rsc'\nimport { NextDataPathnameNormalizer } from './normalizers/request/next-data'\nimport { getIsPossibleServerAction } from './lib/server-action-request-meta'\nimport { isInterceptionRouteAppPath } from '../shared/lib/router/utils/interception-routes'\nimport { toRoute } from './lib/to-route'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { isNodeNextRequest, isNodeNextResponse } from './base-http/helpers'\nimport { patchSetHeaderWithCookieSupport } from './lib/patch-set-header'\nimport { checkIsAppPPREnabled } from './lib/experimental/ppr'\nimport {\n  getBuiltinRequestContext,\n  type WaitUntil,\n} from './after/builtin-request-context'\nimport { ENCODED_TAGS } from './stream-utils/encodedTags'\nimport { NextRequestHint } from './web/adapter'\nimport { getRevalidateReason } from './instrumentation/utils'\nimport { RouteKind } from './route-kind'\nimport type { RouteModule } from './route-modules/route-module'\nimport { FallbackMode, parseFallbackField } from '../lib/fallback'\nimport { toResponseCacheEntry } from './response-cache/utils'\nimport { scheduleOnNextTick } from '../lib/scheduler'\nimport { SegmentPrefixRSCPathnameNormalizer } from './normalizers/request/segment-prefix-rsc'\nimport {\n  shouldServeStreamingMetadata,\n  isHtmlBotRequest,\n} from './lib/streaming-metadata'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { decodeQueryPathParameter } from './lib/decode-query-path-parameter'\nimport { getCacheHandlers } from './use-cache/handlers'\n\nexport type FindComponentsResult = {\n  components: LoadComponentsReturnType\n  query: NextParsedUrlQuery\n}\n\nexport interface MiddlewareRoutingItem {\n  page: string\n  match: MiddlewareRouteMatch\n  matchers?: MiddlewareMatcher[]\n}\n\nexport type RouteHandler<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = (\n  req: ServerRequest,\n  res: ServerResponse,\n  parsedUrl: NextUrlWithParsedQuery\n) => PromiseLike<boolean> | boolean\n\n/**\n * The normalized route manifest is the same as the route manifest, but with\n * the rewrites normalized to the object shape that the router expects.\n */\nexport type NormalizedRouteManifest = {\n  readonly dynamicRoutes: ReadonlyArray<ManifestRoute>\n  readonly rewrites: {\n    readonly beforeFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly afterFiles: ReadonlyArray<ManifestRewriteRoute>\n    readonly fallback: ReadonlyArray<ManifestRewriteRoute>\n  }\n}\n\nexport interface Options {\n  /**\n   * Object containing the configuration next.config.js\n   */\n  conf: NextConfig\n  /**\n   * Set to false when the server was created by Next.js\n   */\n  customServer?: boolean\n  /**\n   * Tells if Next.js is running in dev mode\n   */\n  dev?: boolean\n  /**\n   * Enables the experimental testing mode.\n   */\n  experimentalTestProxy?: boolean\n\n  /**\n   * Whether or not the dev server is running in experimental HTTPS mode\n   */\n  experimentalHttpsServer?: boolean\n  /**\n   * Where the Next project is located\n   */\n  dir?: string\n  /**\n   * Tells if Next.js is at the platform-level\n   */\n  minimalMode?: boolean\n  /**\n   * Hide error messages containing server information\n   */\n  quiet?: boolean\n  /**\n   * The hostname the server is running behind\n   */\n  hostname?: string\n  /**\n   * The port the server is running behind\n   */\n  port?: number\n  /**\n   * The HTTP Server that Next.js is running behind\n   */\n  httpServer?: HTTPServer\n}\n\nexport type RenderOpts = PagesRenderOptsPartial & AppRenderOptsPartial\n\nexport type LoadedRenderOpts = RenderOpts &\n  LoadComponentsReturnType &\n  RequestLifecycleOpts\n\nexport type RequestLifecycleOpts = {\n  waitUntil: ((promise: Promise<any>) => void) | undefined\n  onClose: (callback: () => void) => void\n  onAfterTaskError: ((error: unknown) => void) | undefined\n}\n\ntype BaseRenderOpts = RenderOpts & {\n  poweredByHeader: boolean\n  generateEtags: boolean\n  previewProps: __ApiPreviewProps\n}\n\n/**\n * The public interface for rendering with the server programmatically. This\n * would typically only allow the base request or response to extend it, but\n * because this can be programmatically accessed, we assume that it could also\n * be the base Node.js request and response types.\n */\nexport interface BaseRequestHandler<\n  ServerRequest extends BaseNextRequest | IncomingMessage = BaseNextRequest,\n  ServerResponse extends\n    | BaseNextResponse\n    | HTTPServerResponse = BaseNextResponse,\n> {\n  (\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery | undefined\n  ): Promise<void> | void\n}\n\nexport type RequestContext<\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> = {\n  req: ServerRequest\n  res: ServerResponse\n  pathname: string\n  query: NextParsedUrlQuery\n  renderOpts: RenderOpts\n}\n\nexport class NoFallbackError extends Error {}\n\n// Internal wrapper around build errors at development\n// time, to prevent us from propagating or logging them\nexport class WrappedBuildError extends Error {\n  innerError: Error\n\n  constructor(innerError: Error) {\n    super()\n    this.innerError = innerError\n  }\n}\n\ntype ResponsePayload = {\n  type: 'html' | 'json' | 'rsc'\n  body: RenderResult\n  cacheControl?: CacheControl\n}\n\nexport type NextEnabledDirectories = {\n  readonly pages: boolean\n  readonly app: boolean\n}\n\nexport default abstract class Server<\n  ServerOptions extends Options = Options,\n  ServerRequest extends BaseNextRequest = BaseNextRequest,\n  ServerResponse extends BaseNextResponse = BaseNextResponse,\n> {\n  public readonly hostname?: string\n  public readonly fetchHostname?: string\n  public readonly port?: number\n  protected readonly dir: string\n  protected readonly quiet: boolean\n  protected readonly nextConfig: NextConfigComplete\n  protected readonly distDir: string\n  protected readonly publicDir: string\n  protected readonly hasStaticDir: boolean\n  protected readonly pagesManifest?: PagesManifest\n  protected readonly appPathsManifest?: PagesManifest\n  protected readonly buildId: string\n  protected readonly minimalMode: boolean\n  protected readonly renderOpts: BaseRenderOpts\n  protected readonly serverOptions: Readonly<ServerOptions>\n  protected readonly appPathRoutes?: Record<string, string[]>\n  protected readonly clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  protected interceptionRoutePatterns: RegExp[]\n  protected nextFontManifest?: DeepReadonly<NextFontManifest>\n  protected instrumentation: InstrumentationModule | undefined\n  private readonly responseCache: ResponseCacheBase\n\n  protected abstract getPublicDir(): string\n  protected abstract getHasStaticDir(): boolean\n  protected abstract getPagesManifest(): PagesManifest | undefined\n  protected abstract getAppPathsManifest(): PagesManifest | undefined\n  protected abstract getBuildId(): string\n  protected abstract getinterceptionRoutePatterns(): RegExp[]\n\n  protected readonly enabledDirectories: NextEnabledDirectories\n  protected abstract getEnabledDirectories(dev: boolean): NextEnabledDirectories\n\n  protected readonly experimentalTestProxy?: boolean\n\n  protected abstract findPageComponents(params: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    // The following parameters are used in the development server's\n    // implementation.\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure?: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null>\n  protected abstract getPrerenderManifest(): DeepReadonly<PrerenderManifest>\n  protected abstract getNextFontManifest():\n    | DeepReadonly<NextFontManifest>\n    | undefined\n  protected abstract attachRequestMeta(\n    req: ServerRequest,\n    parsedUrl: NextUrlWithParsedQuery\n  ): void\n  protected abstract hasPage(pathname: string): Promise<boolean>\n\n  protected abstract sendRenderResult(\n    req: ServerRequest,\n    res: ServerResponse,\n    options: {\n      result: RenderResult\n      type: 'html' | 'json' | 'rsc'\n      generateEtags: boolean\n      poweredByHeader: boolean\n      cacheControl: CacheControl | undefined\n    }\n  ): Promise<void>\n\n  protected abstract runApi(\n    req: ServerRequest,\n    res: ServerResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean>\n\n  protected abstract renderHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult>\n\n  protected abstract getIncrementalCache(options: {\n    requestHeaders: Record<string, undefined | string | string[]>\n    requestProtocol: 'http' | 'https'\n  }): Promise<import('./lib/incremental-cache').IncrementalCache>\n\n  protected abstract getResponseCache(options: {\n    dev: boolean\n  }): ResponseCacheBase\n\n  protected getServerComponentsHmrCache():\n    | ServerComponentsHmrCache\n    | undefined {\n    return this.nextConfig.experimental.serverComponentsHmrCache\n      ? (globalThis as any).__serverComponentsHmrCache\n      : undefined\n  }\n\n  protected abstract loadEnvConfig(params: {\n    dev: boolean\n    forceReload?: boolean\n  }): void\n\n  // TODO-APP: (wyattjoh): Make protected again. Used for turbopack in route-resolver.ts right now.\n  public readonly matchers: RouteMatcherManager\n  protected readonly i18nProvider?: I18NProvider\n  protected readonly localeNormalizer?: LocaleRouteNormalizer\n\n  protected readonly normalizers: {\n    readonly rsc: RSCPathnameNormalizer | undefined\n    readonly prefetchRSC: PrefetchRSCPathnameNormalizer | undefined\n    readonly segmentPrefetchRSC: SegmentPrefixRSCPathnameNormalizer | undefined\n    readonly data: NextDataPathnameNormalizer | undefined\n  }\n\n  private readonly isAppPPREnabled: boolean\n  private readonly isAppSegmentPrefetchEnabled: boolean\n\n  /**\n   * This is used to persist cache scopes across\n   * prefetch -> full route requests for dynamic IO\n   * it's only fully used in dev\n   */\n\n  public constructor(options: ServerOptions) {\n    const {\n      dir = '.',\n      quiet = false,\n      conf,\n      dev = false,\n      minimalMode = false,\n      hostname,\n      port,\n      experimentalTestProxy,\n    } = options\n\n    this.experimentalTestProxy = experimentalTestProxy\n    this.serverOptions = options\n\n    this.dir =\n      process.env.NEXT_RUNTIME === 'edge' ? dir : require('path').resolve(dir)\n\n    this.quiet = quiet\n    this.loadEnvConfig({ dev })\n\n    // TODO: should conf be normalized to prevent missing\n    // values from causing issues as this can be user provided\n    this.nextConfig = conf as NextConfigComplete\n    this.hostname = hostname\n    if (this.hostname) {\n      // we format the hostname so that it can be fetched\n      this.fetchHostname = formatHostname(this.hostname)\n    }\n    this.port = port\n    this.distDir =\n      process.env.NEXT_RUNTIME === 'edge'\n        ? this.nextConfig.distDir\n        : require('path').join(this.dir, this.nextConfig.distDir)\n    this.publicDir = this.getPublicDir()\n    this.hasStaticDir = !minimalMode && this.getHasStaticDir()\n\n    this.i18nProvider = this.nextConfig.i18n?.locales\n      ? new I18NProvider(this.nextConfig.i18n)\n      : undefined\n\n    // Configure the locale normalizer, it's used for routes inside `pages/`.\n    this.localeNormalizer = this.i18nProvider\n      ? new LocaleRouteNormalizer(this.i18nProvider)\n      : undefined\n\n    // Only serverRuntimeConfig needs the default\n    // publicRuntimeConfig gets it's default in client/index.js\n    const {\n      serverRuntimeConfig = {},\n      publicRuntimeConfig,\n      assetPrefix,\n      generateEtags,\n    } = this.nextConfig\n\n    this.buildId = this.getBuildId()\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    const minimalModeKey = 'minimalMode'\n    this[minimalModeKey] =\n      minimalMode || !!process.env.NEXT_PRIVATE_MINIMAL_MODE\n\n    this.enabledDirectories = this.getEnabledDirectories(dev)\n\n    this.isAppPPREnabled =\n      this.enabledDirectories.app &&\n      checkIsAppPPREnabled(this.nextConfig.experimental.ppr)\n\n    this.isAppSegmentPrefetchEnabled =\n      this.enabledDirectories.app &&\n      this.nextConfig.experimental.clientSegmentCache === true\n\n    this.normalizers = {\n      // We should normalize the pathname from the RSC prefix only in minimal\n      // mode as otherwise that route is not exposed external to the server as\n      // we instead only rely on the headers.\n      rsc:\n        this.enabledDirectories.app && this.minimalMode\n          ? new RSCPathnameNormalizer()\n          : undefined,\n      prefetchRSC:\n        this.isAppPPREnabled && this.minimalMode\n          ? new PrefetchRSCPathnameNormalizer()\n          : undefined,\n      segmentPrefetchRSC:\n        this.isAppSegmentPrefetchEnabled && this.minimalMode\n          ? new SegmentPrefixRSCPathnameNormalizer()\n          : undefined,\n      data: this.enabledDirectories.pages\n        ? new NextDataPathnameNormalizer(this.buildId)\n        : undefined,\n    }\n\n    this.nextFontManifest = this.getNextFontManifest()\n\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      process.env.NEXT_DEPLOYMENT_ID = this.nextConfig.deploymentId || ''\n    }\n\n    this.renderOpts = {\n      supportsDynamicResponse: true,\n      trailingSlash: this.nextConfig.trailingSlash,\n      deploymentId: this.nextConfig.deploymentId,\n      strictNextHead: this.nextConfig.experimental.strictNextHead ?? true,\n      poweredByHeader: this.nextConfig.poweredByHeader,\n      canonicalBase: this.nextConfig.amp.canonicalBase || '',\n      generateEtags,\n      previewProps: this.getPrerenderManifest().preview,\n      ampOptimizerConfig: this.nextConfig.experimental.amp?.optimizer,\n      basePath: this.nextConfig.basePath,\n      images: this.nextConfig.images,\n      optimizeCss: this.nextConfig.experimental.optimizeCss,\n      nextConfigOutput: this.nextConfig.output,\n      nextScriptWorkers: this.nextConfig.experimental.nextScriptWorkers,\n      disableOptimizedLoading:\n        this.nextConfig.experimental.disableOptimizedLoading,\n      domainLocales: this.nextConfig.i18n?.domains,\n      distDir: this.distDir,\n      serverComponents: this.enabledDirectories.app,\n      cacheLifeProfiles: this.nextConfig.experimental.cacheLife,\n      enableTainting: this.nextConfig.experimental.taint,\n      crossOrigin: this.nextConfig.crossOrigin\n        ? this.nextConfig.crossOrigin\n        : undefined,\n      largePageDataBytes: this.nextConfig.experimental.largePageDataBytes,\n      // Only the `publicRuntimeConfig` key is exposed to the client side\n      // It'll be rendered as part of __NEXT_DATA__ on the client side\n      runtimeConfig:\n        Object.keys(publicRuntimeConfig).length > 0\n          ? publicRuntimeConfig\n          : undefined,\n\n      // @ts-expect-error internal field not publicly exposed\n      isExperimentalCompile: this.nextConfig.experimental.isExperimentalCompile,\n      // `htmlLimitedBots` is passed to server as serialized config in string format\n      htmlLimitedBots: this.nextConfig.htmlLimitedBots,\n      experimental: {\n        expireTime: this.nextConfig.expireTime,\n        staleTimes: this.nextConfig.experimental.staleTimes,\n        clientTraceMetadata: this.nextConfig.experimental.clientTraceMetadata,\n        dynamicIO: this.nextConfig.experimental.dynamicIO ?? false,\n        clientSegmentCache:\n          this.nextConfig.experimental.clientSegmentCache === 'client-only'\n            ? 'client-only'\n            : Boolean(this.nextConfig.experimental.clientSegmentCache),\n        dynamicOnHover: this.nextConfig.experimental.dynamicOnHover ?? false,\n        inlineCss: this.nextConfig.experimental.inlineCss ?? false,\n        authInterrupts: !!this.nextConfig.experimental.authInterrupts,\n      },\n      onInstrumentationRequestError:\n        this.instrumentationOnRequestError.bind(this),\n      reactMaxHeadersLength: this.nextConfig.reactMaxHeadersLength,\n    }\n\n    // Initialize next/config with the environment configuration\n    setConfig({\n      serverRuntimeConfig,\n      publicRuntimeConfig,\n    })\n\n    this.pagesManifest = this.getPagesManifest()\n    this.appPathsManifest = this.getAppPathsManifest()\n    this.appPathRoutes = this.getAppPathRoutes()\n    this.interceptionRoutePatterns = this.getinterceptionRoutePatterns()\n\n    // Configure the routes.\n    this.matchers = this.getRouteMatchers()\n\n    // Start route compilation. We don't wait for the routes to finish loading\n    // because we use the `waitTillReady` promise below in `handleRequest` to\n    // wait. Also we can't `await` in the constructor.\n    void this.matchers.reload()\n\n    this.setAssetPrefix(assetPrefix)\n    this.responseCache = this.getResponseCache({ dev })\n  }\n\n  protected reloadMatchers() {\n    return this.matchers.reload()\n  }\n\n  private handleRSCRequest: RouteHandler<ServerRequest, ServerResponse> = (\n    req,\n    _res,\n    parsedUrl\n  ) => {\n    if (!parsedUrl.pathname) return false\n\n    if (this.normalizers.segmentPrefetchRSC?.match(parsedUrl.pathname)) {\n      const result = this.normalizers.segmentPrefetchRSC.extract(\n        parsedUrl.pathname\n      )\n      if (!result) return false\n\n      const { originalPathname, segmentPath } = result\n      parsedUrl.pathname = originalPathname\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] =\n        segmentPath\n\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n      addRequestMeta(req, 'segmentPrefetchRSCRequest', segmentPath)\n    } else if (this.normalizers.prefetchRSC?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.prefetchRSC.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a router prefetch request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n      addRequestMeta(req, 'isPrefetchRSCRequest', true)\n    } else if (this.normalizers.rsc?.match(parsedUrl.pathname)) {\n      parsedUrl.pathname = this.normalizers.rsc.normalize(\n        parsedUrl.pathname,\n        true\n      )\n\n      // Mark the request as a RSC request.\n      req.headers[RSC_HEADER.toLowerCase()] = '1'\n      addRequestMeta(req, 'isRSCRequest', true)\n    } else if (req.headers['x-now-route-matches']) {\n      // If we didn't match, return with the flight headers stripped. If in\n      // minimal mode we didn't match based on the path, this can't be a RSC\n      // request. This is because Vercel only sends this header during\n      // revalidation requests and we want the cache to instead depend on the\n      // request path for flight information.\n      stripFlightHeaders(req.headers)\n\n      return false\n    } else if (req.headers[RSC_HEADER.toLowerCase()] === '1') {\n      addRequestMeta(req, 'isRSCRequest', true)\n\n      if (req.headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] === '1') {\n        addRequestMeta(req, 'isPrefetchRSCRequest', true)\n\n        const segmentPrefetchRSCRequest =\n          req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()]\n        if (typeof segmentPrefetchRSCRequest === 'string') {\n          addRequestMeta(\n            req,\n            'segmentPrefetchRSCRequest',\n            segmentPrefetchRSCRequest\n          )\n        }\n      }\n    } else {\n      // Otherwise just return without doing anything.\n      return false\n    }\n\n    if (req.url) {\n      const parsed = parseUrl(req.url)\n      parsed.pathname = parsedUrl.pathname\n      req.url = formatUrl(parsed)\n    }\n\n    return false\n  }\n\n  private handleNextDataRequest: RouteHandler<ServerRequest, ServerResponse> =\n    async (req, res, parsedUrl) => {\n      const middleware = await this.getMiddleware()\n      const params = matchNextDataPathname(parsedUrl.pathname)\n\n      // ignore for non-next data URLs\n      if (!params || !params.path) {\n        return false\n      }\n\n      if (params.path[0] !== this.buildId) {\n        // Ignore if its a middleware request when we aren't on edge.\n        if (\n          process.env.NEXT_RUNTIME !== 'edge' &&\n          getRequestMeta(req, 'middlewareInvoke')\n        ) {\n          return false\n        }\n\n        // Make sure to 404 if the buildId isn't correct\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // remove buildId from URL\n      params.path.shift()\n\n      const lastParam = params.path[params.path.length - 1]\n\n      // show 404 if it doesn't end with .json\n      if (typeof lastParam !== 'string' || !lastParam.endsWith('.json')) {\n        await this.render404(req, res, parsedUrl)\n        return true\n      }\n\n      // re-create page's pathname\n      let pathname = `/${params.path.join('/')}`\n      pathname = getRouteFromAssetPath(pathname, '.json')\n\n      // ensure trailing slash is normalized per config\n      if (middleware) {\n        if (this.nextConfig.trailingSlash && !pathname.endsWith('/')) {\n          pathname += '/'\n        }\n        if (\n          !this.nextConfig.trailingSlash &&\n          pathname.length > 1 &&\n          pathname.endsWith('/')\n        ) {\n          pathname = pathname.substring(0, pathname.length - 1)\n        }\n      }\n\n      if (this.i18nProvider) {\n        // Remove the port from the hostname if present.\n        const hostname = req?.headers.host?.split(':', 1)[0].toLowerCase()\n\n        const domainLocale = this.i18nProvider.detectDomainLocale(hostname)\n        const defaultLocale =\n          domainLocale?.defaultLocale ?? this.i18nProvider.config.defaultLocale\n\n        const localePathResult = this.i18nProvider.analyze(pathname)\n\n        // If the locale is detected from the path, we need to remove it\n        // from the pathname.\n        if (localePathResult.detectedLocale) {\n          pathname = localePathResult.pathname\n        }\n\n        // Update the query with the detected locale and default locale.\n        addRequestMeta(req, 'locale', localePathResult.detectedLocale)\n        addRequestMeta(req, 'defaultLocale', defaultLocale)\n\n        // If the locale is not detected from the path, we need to mark that\n        // it was not inferred from default.\n        if (!localePathResult.detectedLocale) {\n          removeRequestMeta(req, 'localeInferredFromDefault')\n        }\n\n        // If no locale was detected and we don't have middleware, we need\n        // to render a 404 page.\n        if (!localePathResult.detectedLocale && !middleware) {\n          addRequestMeta(req, 'locale', defaultLocale)\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n      }\n\n      parsedUrl.pathname = pathname\n      addRequestMeta(req, 'isNextDataReq', true)\n\n      return false\n    }\n\n  protected handleNextImageRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallRenderRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected handleCatchallMiddlewareRequest: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = () => false\n\n  protected getRouteMatchers(): RouteMatcherManager {\n    // Create a new manifest loader that get's the manifests from the server.\n    const manifestLoader = new ServerManifestLoader((name) => {\n      switch (name) {\n        case PAGES_MANIFEST:\n          return this.getPagesManifest() ?? null\n        case APP_PATHS_MANIFEST:\n          return this.getAppPathsManifest() ?? null\n        default:\n          return null\n      }\n    })\n\n    // Configure the matchers and handlers.\n    const matchers: RouteMatcherManager = new DefaultRouteMatcherManager()\n\n    // Match pages under `pages/`.\n    matchers.push(\n      new PagesRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // Match api routes under `pages/api/`.\n    matchers.push(\n      new PagesAPIRouteMatcherProvider(\n        this.distDir,\n        manifestLoader,\n        this.i18nProvider\n      )\n    )\n\n    // If the app directory is enabled, then add the app matchers and handlers.\n    if (this.enabledDirectories.app) {\n      // Match app pages under `app/`.\n      matchers.push(\n        new AppPageRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n      matchers.push(\n        new AppRouteRouteMatcherProvider(this.distDir, manifestLoader)\n      )\n    }\n\n    return matchers\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    const [err, req, ctx] = args\n\n    if (this.instrumentation) {\n      try {\n        await this.instrumentation.onRequestError?.(\n          err,\n          {\n            path: req.url || '',\n            method: req.method || 'GET',\n            // Normalize middleware headers and other server request headers\n            headers:\n              req instanceof NextRequestHint\n                ? Object.fromEntries(req.headers.entries())\n                : req.headers,\n          },\n          ctx\n        )\n      } catch (handlerErr) {\n        // Log the soft error and continue, since errors can thrown from react stream handler\n        console.error('Error in instrumentation.onRequestError:', handlerErr)\n      }\n    }\n  }\n\n  public logError(err: Error): void {\n    if (this.quiet) return\n    Log.error(err)\n  }\n\n  public async handleRequest(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    await this.prepare()\n    const method = req.method.toUpperCase()\n\n    const tracer = getTracer()\n    return tracer.withPropagatedContext(req.headers, () => {\n      return tracer.trace(\n        BaseServerSpan.handleRequest,\n        {\n          spanName: `${method} ${req.url}`,\n          kind: SpanKind.SERVER,\n          attributes: {\n            'http.method': method,\n            'http.target': req.url,\n          },\n        },\n        async (span) =>\n          this.handleRequestImpl(req, res, parsedUrl).finally(() => {\n            if (!span) return\n\n            const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n            span.setAttributes({\n              'http.status_code': res.statusCode,\n              'next.rsc': isRSCRequest,\n            })\n\n            const rootSpanAttributes = tracer.getRootSpanAttributes()\n            // We were unable to get attributes, probably OTEL is not enabled\n            if (!rootSpanAttributes) return\n\n            if (\n              rootSpanAttributes.get('next.span_type') !==\n              BaseServerSpan.handleRequest\n            ) {\n              console.warn(\n                `Unexpected root span type '${rootSpanAttributes.get(\n                  'next.span_type'\n                )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n              )\n              return\n            }\n\n            const route = rootSpanAttributes.get('next.route')\n            if (route) {\n              const name = isRSCRequest\n                ? `RSC ${method} ${route}`\n                : `${method} ${route}`\n\n              span.setAttributes({\n                'next.route': route,\n                'http.route': route,\n                'next.span_name': name,\n              })\n              span.updateName(name)\n            } else {\n              span.updateName(\n                isRSCRequest\n                  ? `RSC ${method} ${req.url}`\n                  : `${method} ${req.url}`\n              )\n            }\n          })\n      )\n    })\n  }\n\n  private async handleRequestImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    try {\n      // Wait for the matchers to be ready.\n      await this.matchers.waitTillReady()\n\n      // ensure cookies set in middleware are merged and\n      // not overridden by API routes/getServerSideProps\n      patchSetHeaderWithCookieSupport(\n        req,\n        isNodeNextResponse(res) ? res.originalResponse : res\n      )\n\n      const urlParts = (req.url || '').split('?', 1)\n      const urlNoQuery = urlParts[0]\n\n      // this normalizes repeated slashes in the path e.g. hello//world ->\n      // hello/world or backslashes to forward slashes, this does not\n      // handle trailing slash as that is handled the same as a next.config.js\n      // redirect\n      if (urlNoQuery?.match(/(\\\\|\\/\\/)/)) {\n        const cleanUrl = normalizeRepeatedSlashes(req.url!)\n        res.redirect(cleanUrl, 308).body(cleanUrl).send()\n        return\n      }\n\n      // Parse url if parsedUrl not provided\n      if (!parsedUrl || typeof parsedUrl !== 'object') {\n        if (!req.url) {\n          throw new Error('Invariant: url can not be undefined')\n        }\n\n        parsedUrl = parseUrl(req.url!, true)\n      }\n\n      if (!parsedUrl.pathname) {\n        throw new Error(\"Invariant: pathname can't be empty\")\n      }\n\n      // Parse the querystring ourselves if the user doesn't handle querystring parsing\n      if (typeof parsedUrl.query === 'string') {\n        parsedUrl.query = Object.fromEntries(\n          new URLSearchParams(parsedUrl.query)\n        )\n      }\n\n      // Update the `x-forwarded-*` headers.\n      const { originalRequest = null } = isNodeNextRequest(req) ? req : {}\n      const xForwardedProto = originalRequest?.headers['x-forwarded-proto']\n      const isHttps = xForwardedProto\n        ? xForwardedProto === 'https'\n        : !!(originalRequest?.socket as TLSSocket)?.encrypted\n\n      req.headers['x-forwarded-host'] ??= req.headers['host'] ?? this.hostname\n      req.headers['x-forwarded-port'] ??= this.port\n        ? this.port.toString()\n        : isHttps\n          ? '443'\n          : '80'\n      req.headers['x-forwarded-proto'] ??= isHttps ? 'https' : 'http'\n      req.headers['x-forwarded-for'] ??= originalRequest?.socket?.remoteAddress\n\n      // This should be done before any normalization of the pathname happens as\n      // it captures the initial URL.\n      this.attachRequestMeta(req, parsedUrl)\n\n      let finished = await this.handleRSCRequest(req, res, parsedUrl)\n      if (finished) return\n\n      const domainLocale = this.i18nProvider?.detectDomainLocale(\n        getHostname(parsedUrl, req.headers)\n      )\n\n      const defaultLocale =\n        domainLocale?.defaultLocale || this.nextConfig.i18n?.defaultLocale\n      addRequestMeta(req, 'defaultLocale', defaultLocale)\n\n      const url = parseUrlUtil(req.url.replace(/^\\/+/, '/'))\n      const pathnameInfo = getNextPathnameInfo(url.pathname, {\n        nextConfig: this.nextConfig,\n        i18nProvider: this.i18nProvider,\n      })\n      url.pathname = pathnameInfo.pathname\n\n      if (pathnameInfo.basePath) {\n        req.url = removePathPrefix(req.url!, this.nextConfig.basePath)\n      }\n\n      const useMatchedPathHeader =\n        this.minimalMode && typeof req.headers[MATCHED_PATH_HEADER] === 'string'\n\n      // TODO: merge handling with invokePath\n      if (useMatchedPathHeader) {\n        try {\n          if (this.enabledDirectories.app) {\n            // ensure /index path is normalized for prerender\n            // in minimal mode\n            if (req.url.match(/^\\/index($|\\?)/)) {\n              req.url = req.url.replace(/^\\/index/, '/')\n            }\n            parsedUrl.pathname =\n              parsedUrl.pathname === '/index' ? '/' : parsedUrl.pathname\n          }\n\n          // x-matched-path is the source of truth, it tells what page\n          // should be rendered because we don't process rewrites in minimalMode\n          let { pathname: matchedPath } = new URL(\n            req.headers[MATCHED_PATH_HEADER] as string,\n            'http://localhost'\n          )\n\n          let { pathname: urlPathname } = new URL(req.url, 'http://localhost')\n\n          // For ISR the URL is normalized to the prerenderPath so if\n          // it's a data request the URL path will be the data URL,\n          // basePath is already stripped by this point\n          if (this.normalizers.data?.match(urlPathname)) {\n            addRequestMeta(req, 'isNextDataReq', true)\n          }\n          // In minimal mode, if PPR is enabled, then we should check to see if\n          // the request should be a resume request.\n          else if (\n            this.isAppPPREnabled &&\n            this.minimalMode &&\n            req.headers[NEXT_RESUME_HEADER] === '1' &&\n            req.method === 'POST'\n          ) {\n            // Decode the postponed state from the request body, it will come as\n            // an array of buffers, so collect them and then concat them to form\n            // the string.\n            const body: Array<Buffer> = []\n            for await (const chunk of req.body) {\n              body.push(chunk)\n            }\n            const postponed = Buffer.concat(body).toString('utf8')\n\n            addRequestMeta(req, 'postponed', postponed)\n          }\n\n          matchedPath = this.normalize(matchedPath)\n          const normalizedUrlPath = this.stripNextDataPath(urlPathname)\n\n          // Perform locale detection and normalization.\n          const localeAnalysisResult = this.i18nProvider?.analyze(matchedPath, {\n            defaultLocale,\n          })\n\n          // The locale result will be defined even if the locale was not\n          // detected for the request because it will be inferred from the\n          // default locale.\n          if (localeAnalysisResult) {\n            addRequestMeta(req, 'locale', localeAnalysisResult.detectedLocale)\n\n            // If the detected locale was inferred from the default locale, we\n            // need to modify the metadata on the request to indicate that.\n            if (localeAnalysisResult.inferredFromDefault) {\n              addRequestMeta(req, 'localeInferredFromDefault', true)\n            } else {\n              removeRequestMeta(req, 'localeInferredFromDefault')\n            }\n          }\n\n          // TODO: check if this is needed any more?\n          matchedPath = denormalizePagePath(matchedPath)\n\n          let srcPathname = matchedPath\n          let pageIsDynamic = isDynamicRoute(srcPathname)\n\n          if (!pageIsDynamic) {\n            const match = await this.matchers.match(srcPathname, {\n              i18n: localeAnalysisResult,\n            })\n\n            // Update the source pathname to the matched page's pathname.\n            if (match) {\n              srcPathname = match.definition.pathname\n              // The page is dynamic if the params are defined.\n              pageIsDynamic = typeof match.params !== 'undefined'\n            }\n          }\n\n          // The rest of this function can't handle i18n properly, so ensure we\n          // restore the pathname with the locale information stripped from it\n          // now that we're done matching if we're using i18n.\n          if (localeAnalysisResult) {\n            matchedPath = localeAnalysisResult.pathname\n          }\n\n          const utils = getUtils({\n            pageIsDynamic,\n            page: srcPathname,\n            i18n: this.nextConfig.i18n,\n            basePath: this.nextConfig.basePath,\n            rewrites: this.getRoutesManifest()?.rewrites || {\n              beforeFiles: [],\n              afterFiles: [],\n              fallback: [],\n            },\n            caseSensitive: !!this.nextConfig.experimental.caseSensitiveRoutes,\n          })\n\n          // Ensure parsedUrl.pathname includes locale before processing\n          // rewrites or they won't match correctly.\n          if (defaultLocale && !pathnameInfo.locale) {\n            parsedUrl.pathname = `/${defaultLocale}${parsedUrl.pathname}`\n          }\n\n          const pathnameBeforeRewrite = parsedUrl.pathname\n          const rewriteParamKeys = Object.keys(\n            utils.handleRewrites(req, parsedUrl)\n          )\n          const didRewrite = pathnameBeforeRewrite !== parsedUrl.pathname\n\n          if (didRewrite && parsedUrl.pathname) {\n            addRequestMeta(req, 'rewroteURL', parsedUrl.pathname)\n          }\n\n          // Create a copy of the query params to avoid mutating the original\n          // object. This prevents any overlapping query params that have the\n          // same normalized key from causing issues.\n          const queryParams = { ...parsedUrl.query }\n\n          for (const [key, value] of Object.entries(parsedUrl.query)) {\n            const normalizedKey = normalizeNextQueryParam(key)\n            if (!normalizedKey) continue\n\n            // Remove the prefixed key from the query params because we want\n            // to consume it for the dynamic route matcher.\n            delete parsedUrl.query[key]\n\n            if (typeof value === 'undefined') continue\n\n            queryParams[normalizedKey] = Array.isArray(value)\n              ? value.map((v) => decodeQueryPathParameter(v))\n              : decodeQueryPathParameter(value)\n          }\n\n          // interpolate dynamic params and normalize URL if needed\n          if (pageIsDynamic) {\n            let params: ParsedUrlQuery | false = {}\n\n            let paramsResult = utils.normalizeDynamicRouteParams(\n              queryParams,\n              false\n            )\n\n            // for prerendered ISR paths we attempt parsing the route\n            // params from the URL directly as route-matches may not\n            // contain the correct values due to the filesystem path\n            // matching before the dynamic route has been matched\n            if (\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(normalizedUrlPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(normalizedUrlPath)\n\n              if (matcherParams) {\n                utils.normalizeDynamicRouteParams(matcherParams, false)\n                Object.assign(paramsResult.params, matcherParams)\n                paramsResult.hasValidParams = true\n              }\n            }\n\n            // if an action request is bypassing a prerender and we\n            // don't have the params in the URL since it was prerendered\n            // and matched during handle: 'filesystem' rather than dynamic route\n            // resolving we need to parse the params from the matched-path.\n            // Note: this is similar to above case but from match-path instead\n            // of from the request URL since a rewrite could cause that to not\n            // match the src pathname\n            if (\n              // we can have a collision with /index and a top-level /[slug]\n              matchedPath !== '/index' &&\n              !paramsResult.hasValidParams &&\n              !isDynamicRoute(matchedPath)\n            ) {\n              let matcherParams = utils.dynamicRouteMatcher?.(matchedPath)\n\n              if (matcherParams) {\n                const curParamsResult = utils.normalizeDynamicRouteParams(\n                  matcherParams,\n                  false\n                )\n\n                if (curParamsResult.hasValidParams) {\n                  Object.assign(params, matcherParams)\n                  paramsResult = curParamsResult\n                }\n              }\n            }\n\n            if (paramsResult.hasValidParams) {\n              params = paramsResult.params\n            }\n\n            const routeMatchesHeader = req.headers['x-now-route-matches']\n            if (\n              typeof routeMatchesHeader === 'string' &&\n              routeMatchesHeader &&\n              isDynamicRoute(matchedPath) &&\n              !paramsResult.hasValidParams\n            ) {\n              const routeMatches =\n                utils.getParamsFromRouteMatches(routeMatchesHeader)\n\n              if (routeMatches) {\n                paramsResult = utils.normalizeDynamicRouteParams(\n                  routeMatches,\n                  true\n                )\n\n                if (paramsResult.hasValidParams) {\n                  params = paramsResult.params\n                }\n              }\n            }\n\n            // Try to parse the params from the query if we couldn't parse them\n            // from the route matches but ignore missing optional params.\n            if (!paramsResult.hasValidParams) {\n              paramsResult = utils.normalizeDynamicRouteParams(\n                queryParams,\n                true\n              )\n\n              if (paramsResult.hasValidParams) {\n                params = paramsResult.params\n              }\n            }\n\n            // handle the actual dynamic route name being requested\n            if (\n              utils.defaultRouteMatches &&\n              normalizedUrlPath === srcPathname &&\n              !paramsResult.hasValidParams &&\n              !utils.normalizeDynamicRouteParams({ ...params }, true)\n                .hasValidParams\n            ) {\n              params = utils.defaultRouteMatches\n\n              // Mark that the default route matches were set on the request\n              // during routing.\n              addRequestMeta(req, 'didSetDefaultRouteMatches', true)\n            }\n\n            if (params) {\n              matchedPath = utils.interpolateDynamicPath(srcPathname, params)\n              req.url = utils.interpolateDynamicPath(req.url!, params)\n\n              // If the request is for a segment prefetch, we need to update the\n              // segment prefetch request path to include the interpolated\n              // params.\n              let segmentPrefetchRSCRequest = getRequestMeta(\n                req,\n                'segmentPrefetchRSCRequest'\n              )\n              if (\n                segmentPrefetchRSCRequest &&\n                isDynamicRoute(segmentPrefetchRSCRequest, false)\n              ) {\n                segmentPrefetchRSCRequest = utils.interpolateDynamicPath(\n                  segmentPrefetchRSCRequest,\n                  params\n                )\n\n                req.headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] =\n                  segmentPrefetchRSCRequest\n                addRequestMeta(\n                  req,\n                  'segmentPrefetchRSCRequest',\n                  segmentPrefetchRSCRequest\n                )\n              }\n            }\n          }\n\n          if (pageIsDynamic || didRewrite) {\n            utils.normalizeVercelUrl(req, [\n              ...rewriteParamKeys,\n              ...Object.keys(utils.defaultRouteRegex?.groups || {}),\n            ])\n          }\n          parsedUrl.pathname = matchedPath\n          url.pathname = parsedUrl.pathname\n          finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n          if (finished) return\n        } catch (err) {\n          if (err instanceof DecodeError || err instanceof NormalizeError) {\n            res.statusCode = 400\n            return this.renderError(null, req, res, '/_error', {})\n          }\n          throw err\n        }\n      }\n\n      addRequestMeta(req, 'isLocaleDomain', Boolean(domainLocale))\n\n      if (pathnameInfo.locale) {\n        req.url = formatUrl(url)\n        addRequestMeta(req, 'didStripLocale', true)\n      }\n\n      // If we aren't in minimal mode or there is no locale in the query\n      // string, add the locale to the query string.\n      if (!this.minimalMode || !getRequestMeta(req, 'locale')) {\n        // If the locale is in the pathname, add it to the query string.\n        if (pathnameInfo.locale) {\n          addRequestMeta(req, 'locale', pathnameInfo.locale)\n        }\n        // If the default locale is available, add it to the query string and\n        // mark it as inferred rather than implicit.\n        else if (defaultLocale) {\n          addRequestMeta(req, 'locale', defaultLocale)\n          addRequestMeta(req, 'localeInferredFromDefault', true)\n        }\n      }\n\n      // set incremental cache to request meta so it can\n      // be passed down for edge functions and the fetch disk\n      // cache can be leveraged locally\n      if (\n        !(this.serverOptions as any).webServerConfig &&\n        !getRequestMeta(req, 'incrementalCache')\n      ) {\n        let protocol: 'http:' | 'https:' = 'https:'\n\n        try {\n          const parsedFullUrl = new URL(\n            getRequestMeta(req, 'initURL') || '/',\n            'http://n'\n          )\n          protocol = parsedFullUrl.protocol as 'https:' | 'http:'\n        } catch {}\n\n        const incrementalCache = await this.getIncrementalCache({\n          requestHeaders: Object.assign({}, req.headers),\n          requestProtocol: protocol.substring(0, protocol.length - 1) as\n            | 'http'\n            | 'https',\n        })\n\n        incrementalCache.resetRequestCache()\n        addRequestMeta(req, 'incrementalCache', incrementalCache)\n        ;(globalThis as any).__incrementalCache = incrementalCache\n      }\n\n      const cacheHandlers = getCacheHandlers()\n\n      if (cacheHandlers) {\n        await Promise.all(\n          [...cacheHandlers].map(async (cacheHandler) => {\n            if ('refreshTags' in cacheHandler) {\n              // Note: cacheHandler.refreshTags() is called lazily before the\n              // first cache entry is retrieved. It allows us to skip the\n              // refresh request if no caches are read at all.\n            } else {\n              const previouslyRevalidatedTags = getPreviouslyRevalidatedTags(\n                req.headers,\n                this.getPrerenderManifest().preview.previewModeId\n              )\n\n              await cacheHandler.receiveExpiredTags(\n                ...previouslyRevalidatedTags\n              )\n            }\n          })\n        )\n      }\n\n      // set server components HMR cache to request meta so it can be passed\n      // down for edge functions\n      if (!getRequestMeta(req, 'serverComponentsHmrCache')) {\n        addRequestMeta(\n          req,\n          'serverComponentsHmrCache',\n          this.getServerComponentsHmrCache()\n        )\n      }\n\n      // when invokePath is specified we can short short circuit resolving\n      // we only honor this header if we are inside of a render worker to\n      // prevent external users coercing the routing path\n      const invokePath = getRequestMeta(req, 'invokePath')\n      const useInvokePath =\n        !useMatchedPathHeader &&\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        invokePath\n\n      if (useInvokePath) {\n        const invokeStatus = getRequestMeta(req, 'invokeStatus')\n        if (invokeStatus) {\n          const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n          if (invokeQuery) {\n            Object.assign(parsedUrl.query, invokeQuery)\n          }\n\n          res.statusCode = invokeStatus\n          let err: Error | null = getRequestMeta(req, 'invokeError') || null\n\n          return this.renderError(err, req, res, '/_error', parsedUrl.query)\n        }\n\n        const parsedMatchedPath = new URL(invokePath || '/', 'http://n')\n        const invokePathnameInfo = getNextPathnameInfo(\n          parsedMatchedPath.pathname,\n          {\n            nextConfig: this.nextConfig,\n            parseData: false,\n          }\n        )\n\n        if (invokePathnameInfo.locale) {\n          addRequestMeta(req, 'locale', invokePathnameInfo.locale)\n        }\n\n        if (parsedUrl.pathname !== parsedMatchedPath.pathname) {\n          parsedUrl.pathname = parsedMatchedPath.pathname\n          addRequestMeta(req, 'rewroteURL', invokePathnameInfo.pathname)\n        }\n        const normalizeResult = normalizeLocalePath(\n          removePathPrefix(parsedUrl.pathname, this.nextConfig.basePath || ''),\n          this.nextConfig.i18n?.locales\n        )\n\n        if (normalizeResult.detectedLocale) {\n          addRequestMeta(req, 'locale', normalizeResult.detectedLocale)\n        }\n        parsedUrl.pathname = normalizeResult.pathname\n\n        for (const key of Object.keys(parsedUrl.query)) {\n          delete parsedUrl.query[key]\n        }\n        const invokeQuery = getRequestMeta(req, 'invokeQuery')\n\n        if (invokeQuery) {\n          Object.assign(parsedUrl.query, invokeQuery)\n        }\n\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        await this.handleCatchallRenderRequest(req, res, parsedUrl)\n        return\n      }\n\n      if (\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        getRequestMeta(req, 'middlewareInvoke')\n      ) {\n        finished = await this.normalizeAndAttachMetadata(req, res, parsedUrl)\n        if (finished) return\n\n        finished = await this.handleCatchallMiddlewareRequest(\n          req,\n          res,\n          parsedUrl\n        )\n        if (finished) return\n\n        const err = new Error()\n        ;(err as any).result = {\n          response: new Response(null, {\n            headers: {\n              'x-middleware-next': '1',\n            },\n          }),\n        }\n        ;(err as any).bubble = true\n        throw err\n      }\n\n      // This wasn't a request via the matched path or the invoke path, so\n      // prepare for a legacy run by removing the base path.\n\n      // ensure we strip the basePath when not using an invoke header\n      if (!useMatchedPathHeader && pathnameInfo.basePath) {\n        parsedUrl.pathname = removePathPrefix(\n          parsedUrl.pathname,\n          pathnameInfo.basePath\n        )\n      }\n\n      res.statusCode = 200\n      return await this.run(req, res, parsedUrl)\n    } catch (err: any) {\n      if (err instanceof NoFallbackError) {\n        throw err\n      }\n\n      if (\n        (err && typeof err === 'object' && err.code === 'ERR_INVALID_URL') ||\n        err instanceof DecodeError ||\n        err instanceof NormalizeError\n      ) {\n        res.statusCode = 400\n        return this.renderError(null, req, res, '/_error', {})\n      }\n\n      if (\n        this.minimalMode ||\n        this.renderOpts.dev ||\n        (isBubbledError(err) && err.bubble)\n      ) {\n        throw err\n      }\n      this.logError(getProperError(err))\n      res.statusCode = 500\n      res.body('Internal Server Error').send()\n    }\n  }\n\n  /**\n   * Normalizes a pathname without attaching any metadata from any matched\n   * normalizer.\n   *\n   * @param pathname the pathname to normalize\n   * @returns the normalized pathname\n   */\n  private normalize = (pathname: string) => {\n    const normalizers: Array<PathnameNormalizer> = []\n\n    if (this.normalizers.data) {\n      normalizers.push(this.normalizers.data)\n    }\n\n    // We have to put the segment prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.segmentPrefetchRSC) {\n      normalizers.push(this.normalizers.segmentPrefetchRSC)\n    }\n\n    // We have to put the prefetch normalizer before the RSC normalizer\n    // because the RSC normalizer will match the prefetch RSC routes too.\n    if (this.normalizers.prefetchRSC) {\n      normalizers.push(this.normalizers.prefetchRSC)\n    }\n\n    if (this.normalizers.rsc) {\n      normalizers.push(this.normalizers.rsc)\n    }\n\n    for (const normalizer of normalizers) {\n      if (!normalizer.match(pathname)) continue\n\n      return normalizer.normalize(pathname, true)\n    }\n\n    return pathname\n  }\n\n  private normalizeAndAttachMetadata: RouteHandler<\n    ServerRequest,\n    ServerResponse\n  > = async (req, res, url) => {\n    let finished = await this.handleNextImageRequest(req, res, url)\n    if (finished) return true\n\n    if (this.enabledDirectories.pages) {\n      finished = await this.handleNextDataRequest(req, res, url)\n      if (finished) return true\n    }\n\n    return false\n  }\n\n  /**\n   * @internal - this method is internal to Next.js and should not be used directly by end-users\n   */\n  public getRequestHandlerWithMetadata(\n    meta: RequestMeta\n  ): BaseRequestHandler<ServerRequest, ServerResponse> {\n    const handler = this.getRequestHandler()\n    return (req, res, parsedUrl) => {\n      setRequestMeta(req, meta)\n      return handler(req, res, parsedUrl)\n    }\n  }\n\n  public getRequestHandler(): BaseRequestHandler<\n    ServerRequest,\n    ServerResponse\n  > {\n    return this.handleRequest.bind(this)\n  }\n\n  protected abstract handleUpgrade(\n    req: ServerRequest,\n    socket: any,\n    head?: any\n  ): Promise<void>\n\n  public setAssetPrefix(prefix?: string): void {\n    this.renderOpts.assetPrefix = prefix ? prefix.replace(/\\/$/, '') : ''\n  }\n\n  protected prepared: boolean = false\n  protected preparedPromise: Promise<void> | null = null\n  /**\n   * Runs async initialization of server.\n   * It is idempotent, won't fire underlying initialization more than once.\n   */\n  public async prepare(): Promise<void> {\n    if (this.prepared) return\n\n    if (this.preparedPromise === null) {\n      // Get instrumentation module\n      this.instrumentation = await this.loadInstrumentationModule()\n      this.preparedPromise = this.prepareImpl().then(() => {\n        this.prepared = true\n        this.preparedPromise = null\n      })\n    }\n    return this.preparedPromise\n  }\n  protected async prepareImpl(): Promise<void> {}\n  protected async loadInstrumentationModule(): Promise<any> {}\n\n  public async close(): Promise<void> {}\n\n  protected getAppPathRoutes(): Record<string, string[]> {\n    const appPathRoutes: Record<string, string[]> = {}\n\n    Object.keys(this.appPathsManifest || {}).forEach((entry) => {\n      const normalizedPath = normalizeAppPath(entry)\n      if (!appPathRoutes[normalizedPath]) {\n        appPathRoutes[normalizedPath] = []\n      }\n      appPathRoutes[normalizedPath].push(entry)\n    })\n    return appPathRoutes\n  }\n\n  protected async run(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.run, async () =>\n      this.runImpl(req, res, parsedUrl)\n    )\n  }\n\n  private async runImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    await this.handleCatchallRenderRequest(req, res, parsedUrl)\n  }\n\n  private async pipe(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.pipe, async () =>\n      this.pipeImpl(fn, partialContext)\n    )\n  }\n\n  private async pipeImpl(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<void> {\n    const ua = partialContext.req.headers['user-agent'] || ''\n    const isBotRequest = isBot(ua)\n\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        supportsDynamicResponse: !isBotRequest,\n        botType: getBotType(ua),\n        serveStreamingMetadata: shouldServeStreamingMetadata(\n          ua,\n          this.nextConfig.htmlLimitedBots\n        ),\n      },\n    }\n\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return\n    }\n    const { req, res } = ctx\n    const originalStatus = res.statusCode\n    const { body, type } = payload\n    let { cacheControl } = payload\n    if (!res.sent) {\n      const { generateEtags, poweredByHeader, dev } = this.renderOpts\n\n      // In dev, we should not cache pages for any reason.\n      if (dev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n        cacheControl = undefined\n      }\n\n      if (cacheControl && cacheControl.expire === undefined) {\n        cacheControl.expire = this.nextConfig.expireTime\n      }\n\n      await this.sendRenderResult(req, res, {\n        result: body,\n        type,\n        generateEtags,\n        poweredByHeader,\n        cacheControl,\n      })\n      res.statusCode = originalStatus\n    }\n  }\n\n  private async getStaticHTML(\n    fn: (\n      ctx: RequestContext<ServerRequest, ServerResponse>\n    ) => Promise<ResponsePayload | null>,\n    partialContext: Omit<\n      RequestContext<ServerRequest, ServerResponse>,\n      'renderOpts'\n    >\n  ): Promise<string | null> {\n    const ctx: RequestContext<ServerRequest, ServerResponse> = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        supportsDynamicResponse: false,\n      },\n    }\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return null\n    }\n    return payload.body.toUnchunkedString()\n  }\n\n  public async render(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.render, async () =>\n      this.renderImpl(req, res, pathname, query, parsedUrl, internalRender)\n    )\n  }\n\n  protected getWaitUntil(): WaitUntil | undefined {\n    const builtinRequestContext = getBuiltinRequestContext()\n    if (builtinRequestContext) {\n      // the platform provided a request context.\n      // use the `waitUntil` from there, whether actually present or not --\n      // if not present, `after` will error.\n\n      // NOTE: if we're in an edge runtime sandbox, this context will be used to forward the outer waitUntil.\n      return builtinRequestContext.waitUntil\n    }\n\n    if (this.minimalMode) {\n      // we're built for a serverless environment, and `waitUntil` is not available,\n      // but using a noop would likely lead to incorrect behavior,\n      // because we have no way of keeping the invocation alive.\n      // return nothing, and `after` will error if used.\n      //\n      // NOTE: for edge functions, `NextWebServer` always runs in minimal mode.\n      //\n      // NOTE: if we're in an edge runtime sandbox, waitUntil will be passed in using \"@next/request-context\",\n      // so we won't get here.\n      return undefined\n    }\n\n    return this.getInternalWaitUntil()\n  }\n\n  protected getInternalWaitUntil(): WaitUntil | undefined {\n    return undefined\n  }\n\n  private async renderImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    parsedUrl?: NextUrlWithParsedQuery,\n    internalRender = false\n  ): Promise<void> {\n    if (!pathname.startsWith('/')) {\n      console.warn(\n        `Cannot render page with path \"${pathname}\", did you mean \"/${pathname}\"?. See more info here: https://nextjs.org/docs/messages/render-no-starting-slash`\n      )\n    }\n\n    if (\n      this.serverOptions.customServer &&\n      pathname === '/index' &&\n      !(await this.hasPage('/index'))\n    ) {\n      // maintain backwards compatibility for custom server\n      // (see custom-server integration tests)\n      pathname = '/'\n    }\n\n    // we allow custom servers to call render for all URLs\n    // so check if we need to serve a static _next file or not.\n    // we don't modify the URL for _next/data request but still\n    // call render so we special case this to prevent an infinite loop\n    if (\n      !internalRender &&\n      !this.minimalMode &&\n      !getRequestMeta(req, 'isNextDataReq') &&\n      (req.url?.match(/^\\/_next\\//) ||\n        (this.hasStaticDir && req.url!.match(/^\\/static\\//)))\n    ) {\n      return this.handleRequest(req, res, parsedUrl)\n    }\n\n    if (isBlockedPage(pathname)) {\n      return this.render404(req, res, parsedUrl)\n    }\n\n    return this.pipe((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  protected async getStaticPaths({\n    pathname,\n  }: {\n    pathname: string\n    requestHeaders: import('./lib/incremental-cache').IncrementalCache['requestHeaders']\n    page: string\n    isAppPath: boolean\n  }): Promise<{\n    staticPaths?: string[]\n    fallbackMode?: FallbackMode\n  }> {\n    // Read whether or not fallback should exist from the manifest.\n    const fallbackField =\n      this.getPrerenderManifest().dynamicRoutes[pathname]?.fallback\n\n    return {\n      // `staticPaths` is intentionally set to `undefined` as it should've\n      // been caught when checking disk data.\n      staticPaths: undefined,\n      fallbackMode: parseFallbackField(fallbackField),\n    }\n  }\n\n  private async renderToResponseWithComponents(\n    requestContext: RequestContext<ServerRequest, ServerResponse>,\n    findComponentsResult: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponseWithComponents,\n      async () =>\n        this.renderToResponseWithComponentsImpl(\n          requestContext,\n          findComponentsResult\n        )\n    )\n  }\n\n  protected pathCouldBeIntercepted(resolvedPathname: string): boolean {\n    return (\n      isInterceptionRouteAppPath(resolvedPathname) ||\n      this.interceptionRoutePatterns.some((regexp) => {\n        return regexp.test(resolvedPathname)\n      })\n    )\n  }\n\n  protected setVaryHeader(\n    req: ServerRequest,\n    res: ServerResponse,\n    isAppPath: boolean,\n    resolvedPathname: string\n  ): void {\n    const baseVaryHeader = `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    let addedNextUrlToVary = false\n\n    if (isAppPath && this.pathCouldBeIntercepted(resolvedPathname)) {\n      // Interception route responses can vary based on the `Next-URL` header.\n      // We use the Vary header to signal this behavior to the client to properly cache the response.\n      res.appendHeader('vary', `${baseVaryHeader}, ${NEXT_URL}`)\n      addedNextUrlToVary = true\n    } else if (isAppPath || isRSCRequest) {\n      // We don't need to include `Next-URL` in the Vary header for non-interception routes since it won't affect the response.\n      // We also set this header for pages to avoid caching issues when navigating between pages and app.\n      res.appendHeader('vary', baseVaryHeader)\n    }\n\n    if (!addedNextUrlToVary) {\n      // Remove `Next-URL` from the request headers we determined it wasn't necessary to include in the Vary header.\n      // This is to avoid any dependency on the `Next-URL` header being present when preparing the response.\n      delete req.headers[NEXT_URL]\n    }\n  }\n\n  private async renderToResponseWithComponentsImpl(\n    {\n      req,\n      res,\n      pathname,\n      renderOpts: opts,\n    }: RequestContext<ServerRequest, ServerResponse>,\n    { components, query }: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    if (pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n      pathname = '/404'\n    }\n    const isErrorPathname = pathname === '/_error'\n    const is404Page =\n      pathname === '/404' || (isErrorPathname && res.statusCode === 404)\n    const is500Page =\n      pathname === '/500' || (isErrorPathname && res.statusCode === 500)\n    const isAppPath = components.isAppPath === true\n\n    const hasServerProps = !!components.getServerSideProps\n    let hasGetStaticPaths = !!components.getStaticPaths\n    const isPossibleServerAction = getIsPossibleServerAction(req)\n    const hasGetInitialProps = !!components.Component?.getInitialProps\n    let isSSG = !!components.getStaticProps\n\n    // Compute the iSSG cache key. We use the rewroteUrl since\n    // pages with fallback: false are allowed to be rewritten to\n    // and we need to look up the path by the rewritten path\n    let urlPathname = parseUrl(req.url || '').pathname || '/'\n\n    let resolvedUrlPathname = getRequestMeta(req, 'rewroteURL') || urlPathname\n\n    this.setVaryHeader(req, res, isAppPath, resolvedUrlPathname)\n\n    let staticPaths: string[] | undefined\n    let fallbackMode: FallbackMode | undefined\n    let hasFallback = false\n\n    const isDynamic = isDynamicRoute(components.page)\n\n    const prerenderManifest = this.getPrerenderManifest()\n\n    if (isAppPath && isDynamic) {\n      const pathsResult = await this.getStaticPaths({\n        pathname,\n        page: components.page,\n        isAppPath,\n        requestHeaders: req.headers,\n      })\n\n      staticPaths = pathsResult.staticPaths\n      fallbackMode = pathsResult.fallbackMode\n      hasFallback = typeof fallbackMode !== 'undefined'\n\n      if (this.nextConfig.output === 'export') {\n        const page = components.page\n        if (!staticPaths) {\n          throw new Error(\n            `Page \"${page}\" is missing exported function \"generateStaticParams()\", which is required with \"output: export\" config.`\n          )\n        }\n\n        const resolvedWithoutSlash = removeTrailingSlash(resolvedUrlPathname)\n        if (!staticPaths.includes(resolvedWithoutSlash)) {\n          throw new Error(\n            `Page \"${page}\" is missing param \"${resolvedWithoutSlash}\" in \"generateStaticParams()\", which is required with \"output: export\" config.`\n          )\n        }\n      }\n\n      if (hasFallback) {\n        hasGetStaticPaths = true\n      }\n    }\n\n    if (\n      hasFallback ||\n      staticPaths?.includes(resolvedUrlPathname) ||\n      // this signals revalidation in deploy environments\n      // TODO: make this more generic\n      req.headers['x-now-route-matches']\n    ) {\n      isSSG = true\n    } else if (!this.renderOpts.dev) {\n      isSSG ||= !!prerenderManifest.routes[toRoute(pathname)]\n    }\n\n    // Toggle whether or not this is a Data request\n    const isNextDataRequest =\n      !!(\n        getRequestMeta(req, 'isNextDataReq') ||\n        (req.headers['x-nextjs-data'] &&\n          (this.serverOptions as any).webServerConfig)\n      ) &&\n      (isSSG || hasServerProps)\n\n    /**\n     * If true, this indicates that the request being made is for an app\n     * prefetch request.\n     */\n    const isPrefetchRSCRequest =\n      getRequestMeta(req, 'isPrefetchRSCRequest') ?? false\n\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? false\n\n    // when we are handling a middleware prefetch and it doesn't\n    // resolve to a static data route we bail early to avoid\n    // unexpected SSR invocations\n    if (\n      !isSSG &&\n      req.headers['x-middleware-prefetch'] &&\n      !(is404Page || pathname === '/_error')\n    ) {\n      res.setHeader(MATCHED_PATH_HEADER, pathname)\n      res.setHeader('x-middleware-skip', '1')\n      res.setHeader(\n        'cache-control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n      res.body('{}').send()\n      return null\n    }\n\n    // normalize req.url for SSG paths as it is not exposed\n    // to getStaticProps and the asPath should not expose /_next/data\n    if (\n      isSSG &&\n      this.minimalMode &&\n      req.headers[MATCHED_PATH_HEADER] &&\n      req.url.startsWith('/_next/data')\n    ) {\n      req.url = this.stripNextDataPath(req.url)\n    }\n\n    const locale = getRequestMeta(req, 'locale')\n    const defaultLocale = isSSG\n      ? this.nextConfig.i18n?.defaultLocale\n      : getRequestMeta(req, 'defaultLocale')\n\n    if (\n      !!req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200)\n    ) {\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${locale ? `/${locale}` : ''}${pathname}`\n      )\n    }\n\n    let routeModule: RouteModule | undefined\n    if (components.routeModule) {\n      routeModule = components.routeModule\n    }\n\n    /**\n     * If the route being rendered is an app page, and the ppr feature has been\n     * enabled, then the given route _could_ support PPR.\n     */\n    const couldSupportPPR: boolean =\n      this.isAppPPREnabled &&\n      typeof routeModule !== 'undefined' &&\n      isAppPageRouteModule(routeModule)\n\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =\n      process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' &&\n      typeof query.__nextppronly !== 'undefined' &&\n      couldSupportPPR\n\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery =\n      hasDebugStaticShellQuery && query.__nextppronly === 'fallback'\n\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled: boolean =\n      couldSupportPPR &&\n      ((\n        prerenderManifest.routes[pathname] ??\n        prerenderManifest.dynamicRoutes[pathname]\n      )?.renderingMode === 'PARTIALLY_STATIC' ||\n        // Ideally we'd want to check the appConfig to see if this page has PPR\n        // enabled or not, but that would require plumbing the appConfig through\n        // to the server during development. We assume that the page supports it\n        // but only during development.\n        (hasDebugStaticShellQuery &&\n          (this.renderOpts.dev === true ||\n            this.experimentalTestProxy === true)))\n\n    const isDebugStaticShell: boolean =\n      hasDebugStaticShellQuery && isRoutePPREnabled\n\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses =\n      isDebugStaticShell && this.renderOpts.dev === true\n\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled\n\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled\n      ? getRequestMeta(req, 'postponed')\n      : undefined\n\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest =\n      isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest\n\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = getRequestMeta(\n      req,\n      'segmentPrefetchRSCRequest'\n    )\n\n    const isHtmlBot = isHtmlBotRequest(req)\n    if (isHtmlBot && isRoutePPREnabled) {\n      isSSG = false\n      this.renderOpts.serveStreamingMetadata = false\n    }\n\n    // we need to ensure the status code if /404 is visited directly\n    if (is404Page && !isNextDataRequest && !isRSCRequest) {\n      res.statusCode = 404\n    }\n\n    // ensure correct status is set when visiting a status page\n    // directly e.g. /500\n    if (STATIC_STATUS_PAGES.includes(pathname)) {\n      res.statusCode = parseInt(pathname.slice(1), 10)\n    }\n\n    if (\n      // Server actions can use non-GET/HEAD methods.\n      !isPossibleServerAction &&\n      // Resume can use non-GET/HEAD methods.\n      !minimalPostponed &&\n      !is404Page &&\n      !is500Page &&\n      pathname !== '/_error' &&\n      req.method !== 'HEAD' &&\n      req.method !== 'GET' &&\n      (typeof components.Component === 'string' || isSSG)\n    ) {\n      res.statusCode = 405\n      res.setHeader('Allow', ['GET', 'HEAD'])\n      res.body('Method Not Allowed').send()\n      return null\n    }\n\n    // handle static page\n    if (typeof components.Component === 'string') {\n      return {\n        type: 'html',\n        // TODO: Static pages should be serialized as RenderResult\n        body: RenderResult.fromStatic(components.Component),\n      }\n    }\n\n    // Ensure that if the `amp` query parameter is falsy that we remove it from\n    // the query object. This ensures it won't be found by the `in` operator.\n    if ('amp' in query && !query.amp) delete query.amp\n\n    if (opts.supportsDynamicResponse === true) {\n      const ua = req.headers['user-agent'] || ''\n      const isBotRequest = isBot(ua)\n      const isSupportedDocument =\n        typeof components.Document?.getInitialProps !== 'function' ||\n        // The built-in `Document` component also supports dynamic HTML for concurrent mode.\n        NEXT_BUILTIN_DOCUMENT in components.Document\n\n      // Disable dynamic HTML in cases that we know it won't be generated,\n      // so that we can continue generating a cache key when possible.\n      // TODO-APP: should the first render for a dynamic app path\n      // be static so we can collect revalidate and populate the\n      // cache if there are no dynamic data requirements\n      opts.supportsDynamicResponse =\n        !isSSG && !isBotRequest && !query.amp && isSupportedDocument\n    }\n\n    // In development, we always want to generate dynamic HTML.\n    if (!isNextDataRequest && isAppPath && opts.dev) {\n      opts.supportsDynamicResponse = true\n    }\n\n    const locales = this.nextConfig.i18n?.locales\n\n    let previewData: PreviewData\n    let isPreviewMode = false\n\n    if (hasServerProps || isSSG || isAppPath) {\n      // For the edge runtime, we don't support preview mode in SSG.\n      if (process.env.NEXT_RUNTIME !== 'edge') {\n        const { tryGetPreviewData } =\n          require('./api-utils/node/try-get-preview-data') as typeof import('./api-utils/node/try-get-preview-data')\n        previewData = tryGetPreviewData(\n          req,\n          res,\n          this.renderOpts.previewProps,\n          !!this.nextConfig.experimental.multiZoneDraftMode\n        )\n        isPreviewMode = previewData !== false\n      }\n    }\n\n    // If this is a request for an app path that should be statically generated\n    // and we aren't in the edge runtime, strip the flight headers so it will\n    // generate the static response.\n    if (\n      isAppPath &&\n      !opts.dev &&\n      !isPreviewMode &&\n      isSSG &&\n      isRSCRequest &&\n      !isDynamicRSCRequest &&\n      (!isEdgeRuntime(opts.runtime) ||\n        (this.serverOptions as any).webServerConfig)\n    ) {\n      stripFlightHeaders(req.headers)\n    }\n\n    let { isOnDemandRevalidate, revalidateOnlyGenerated } =\n      checkIsOnDemandRevalidate(req, this.renderOpts.previewProps)\n\n    if (isSSG && this.minimalMode && req.headers[MATCHED_PATH_HEADER]) {\n      // the url value is already correct when the matched-path header is set\n      resolvedUrlPathname = urlPathname\n    }\n\n    urlPathname = removeTrailingSlash(urlPathname)\n    resolvedUrlPathname = removeTrailingSlash(resolvedUrlPathname)\n    if (this.localeNormalizer) {\n      resolvedUrlPathname = this.localeNormalizer.normalize(resolvedUrlPathname)\n    }\n\n    const handleRedirect = (pageData: any) => {\n      const redirect = {\n        destination: pageData.pageProps.__N_REDIRECT,\n        statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n        basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH,\n      }\n      const statusCode = getRedirectStatus(redirect)\n      const { basePath } = this.nextConfig\n\n      if (\n        basePath &&\n        redirect.basePath !== false &&\n        redirect.destination.startsWith('/')\n      ) {\n        redirect.destination = `${basePath}${redirect.destination}`\n      }\n\n      if (redirect.destination.startsWith('/')) {\n        redirect.destination = normalizeRepeatedSlashes(redirect.destination)\n      }\n\n      res\n        .redirect(redirect.destination, statusCode)\n        .body(redirect.destination)\n        .send()\n    }\n\n    // remove /_next/data prefix from urlPathname so it matches\n    // for direct page visit and /_next/data visit\n    if (isNextDataRequest) {\n      resolvedUrlPathname = this.stripNextDataPath(resolvedUrlPathname)\n      urlPathname = this.stripNextDataPath(urlPathname)\n    }\n\n    let ssgCacheKey: string | null = null\n    if (\n      !isPreviewMode &&\n      isSSG &&\n      !opts.supportsDynamicResponse &&\n      !isPossibleServerAction &&\n      !minimalPostponed &&\n      !isDynamicRSCRequest\n    ) {\n      ssgCacheKey = `${locale ? `/${locale}` : ''}${\n        (pathname === '/' || resolvedUrlPathname === '/') && locale\n          ? ''\n          : resolvedUrlPathname\n      }${query.amp ? '.amp' : ''}`\n    }\n\n    if ((is404Page || is500Page) && isSSG) {\n      ssgCacheKey = `${locale ? `/${locale}` : ''}${pathname}${\n        query.amp ? '.amp' : ''\n      }`\n    }\n\n    if (ssgCacheKey) {\n      ssgCacheKey = decodePathParams(ssgCacheKey)\n\n      // ensure /index and / is normalized to one key\n      ssgCacheKey =\n        ssgCacheKey === '/index' && pathname === '/' ? '/' : ssgCacheKey\n    }\n    let protocol: 'http:' | 'https:' = 'https:'\n\n    try {\n      const parsedFullUrl = new URL(\n        getRequestMeta(req, 'initURL') || '/',\n        'http://n'\n      )\n      protocol = parsedFullUrl.protocol as 'https:' | 'http:'\n    } catch {}\n\n    // use existing incrementalCache instance if available\n    const incrementalCache: import('./lib/incremental-cache').IncrementalCache =\n      (globalThis as any).__incrementalCache ||\n      (await this.getIncrementalCache({\n        requestHeaders: Object.assign({}, req.headers),\n        requestProtocol: protocol.substring(0, protocol.length - 1) as\n          | 'http'\n          | 'https',\n      }))\n\n    // TODO: investigate, this is not safe across multiple concurrent requests\n    incrementalCache.resetRequestCache()\n\n    type RendererContext = {\n      /**\n       * The postponed data for this render. This is only provided when resuming\n       * a render that has been postponed.\n       */\n      postponed: string | undefined\n\n      pagesFallback: boolean | undefined\n\n      /**\n       * The unknown route params for this render.\n       */\n      fallbackRouteParams: FallbackRouteParams | null\n    }\n    type Renderer = (\n      context: RendererContext\n    ) => Promise<ResponseCacheEntry | null>\n\n    const doRender: Renderer = async ({\n      postponed,\n      pagesFallback = false,\n      fallbackRouteParams,\n    }) => {\n      // In development, we always want to generate dynamic HTML.\n      let supportsDynamicResponse: boolean =\n        // If we're in development, we always support dynamic HTML, unless it's\n        // a data request, in which case we only produce static HTML.\n        (!isNextDataRequest && opts.dev === true) ||\n        // If this is not SSG or does not have static paths, then it supports\n        // dynamic HTML.\n        (!isSSG && !hasGetStaticPaths) ||\n        // If this request has provided postponed data, it supports dynamic\n        // HTML.\n        typeof postponed === 'string' ||\n        // If this is a dynamic RSC request, then this render supports dynamic\n        // HTML (it's dynamic).\n        isDynamicRSCRequest\n\n      const origQuery = parseUrl(req.url || '', true).query\n\n      // clear any dynamic route params so they aren't in\n      // the resolvedUrl\n      if (opts.params) {\n        Object.keys(opts.params).forEach((key) => {\n          delete origQuery[key]\n        })\n      }\n      const hadTrailingSlash =\n        urlPathname !== '/' && this.nextConfig.trailingSlash\n\n      const resolvedUrl = formatUrl({\n        pathname: `${resolvedUrlPathname}${hadTrailingSlash ? '/' : ''}`,\n        // make sure to only add query values from original URL\n        query: origQuery,\n      })\n\n      // When html bots request PPR page, perform the full dynamic rendering.\n      const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled\n\n      const renderOpts: LoadedRenderOpts = {\n        ...components,\n        ...opts,\n        ...(isAppPath\n          ? {\n              incrementalCache,\n              // This is a revalidation request if the request is for a static\n              // page and it is not being resumed from a postponed render and\n              // it is not a dynamic RSC request then it is a revalidation\n              // request.\n              isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n              serverActions: this.nextConfig.experimental.serverActions,\n            }\n          : {}),\n        isNextDataRequest,\n        resolvedUrl,\n        locale,\n        locales,\n        defaultLocale,\n        multiZoneDraftMode: this.nextConfig.experimental.multiZoneDraftMode,\n        // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n        // and not the resolved URL to prevent a hydration mismatch on\n        // asPath\n        resolvedAsPath:\n          hasServerProps || hasGetInitialProps\n            ? formatUrl({\n                // we use the original URL pathname less the _next/data prefix if\n                // present\n                pathname: `${urlPathname}${hadTrailingSlash ? '/' : ''}`,\n                query: origQuery,\n              })\n            : resolvedUrl,\n        experimental: {\n          ...opts.experimental,\n          isRoutePPREnabled,\n        },\n        supportsDynamicResponse,\n        shouldWaitOnAllReady,\n        isOnDemandRevalidate,\n        isDraftMode: isPreviewMode,\n        isPossibleServerAction,\n        postponed,\n        waitUntil: this.getWaitUntil(),\n        onClose: res.onClose.bind(res),\n        onAfterTaskError: undefined,\n        // only available in dev\n        setIsrStatus: (this as any).setIsrStatus,\n      }\n\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        supportsDynamicResponse = false\n        renderOpts.nextExport = true\n        renderOpts.supportsDynamicResponse = false\n        renderOpts.isStaticGeneration = true\n        renderOpts.isRevalidate = true\n        renderOpts.isDebugDynamicAccesses = isDebugDynamicAccesses\n      }\n\n      // Legacy render methods will return a render result that needs to be\n      // served by the server.\n      let result: RenderResult\n\n      if (routeModule) {\n        if (isAppRouteRouteModule(routeModule)) {\n          if (\n            // The type check here ensures that `req` is correctly typed, and the\n            // environment variable check provides dead code elimination.\n            process.env.NEXT_RUNTIME === 'edge' ||\n            !isNodeNextRequest(req) ||\n            !isNodeNextResponse(res)\n          ) {\n            throw new Error(\n              'Invariant: App Route Route Modules cannot be used in the edge runtime'\n            )\n          }\n\n          const context: AppRouteRouteHandlerContext = {\n            params: opts.params,\n            prerenderManifest,\n            renderOpts: {\n              experimental: {\n                dynamicIO: renderOpts.experimental.dynamicIO,\n                authInterrupts: renderOpts.experimental.authInterrupts,\n              },\n              supportsDynamicResponse,\n              incrementalCache,\n              cacheLifeProfiles: this.nextConfig.experimental?.cacheLife,\n              isRevalidate: isSSG,\n              waitUntil: this.getWaitUntil(),\n              onClose: res.onClose.bind(res),\n              onAfterTaskError: undefined,\n              onInstrumentationRequestError:\n                this.renderOpts.onInstrumentationRequestError,\n            },\n            sharedContext: {\n              buildId: this.buildId,\n            },\n          }\n\n          try {\n            const request = NextRequestAdapter.fromNodeNextRequest(\n              req,\n              signalFromNodeResponse(res.originalResponse)\n            )\n\n            const response = await routeModule.handle(request, context)\n\n            ;(req as any).fetchMetrics = (\n              context.renderOpts as any\n            ).fetchMetrics\n\n            const cacheTags = context.renderOpts.collectedTags\n\n            // If the request is for a static response, we can cache it so long\n            // as it's not edge.\n            if (isSSG) {\n              const blob = await response.blob()\n\n              // Copy the headers from the response.\n              const headers = toNodeOutgoingHttpHeaders(response.headers)\n\n              if (cacheTags) {\n                headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n              }\n\n              if (!headers['content-type'] && blob.type) {\n                headers['content-type'] = blob.type\n              }\n\n              const revalidate =\n                typeof context.renderOpts.collectedRevalidate === 'undefined' ||\n                context.renderOpts.collectedRevalidate >= INFINITE_CACHE\n                  ? false\n                  : context.renderOpts.collectedRevalidate\n\n              const expire =\n                typeof context.renderOpts.collectedExpire === 'undefined' ||\n                context.renderOpts.collectedExpire >= INFINITE_CACHE\n                  ? undefined\n                  : context.renderOpts.collectedExpire\n\n              // Create the cache entry for the response.\n              const cacheEntry: ResponseCacheEntry = {\n                value: {\n                  kind: CachedRouteKind.APP_ROUTE,\n                  status: response.status,\n                  body: Buffer.from(await blob.arrayBuffer()),\n                  headers,\n                },\n                cacheControl: { revalidate, expire },\n                isFallback: false,\n              }\n\n              return cacheEntry\n            }\n            let pendingWaitUntil = context.renderOpts.pendingWaitUntil\n\n            // Attempt using provided waitUntil if available\n            // if it's not we fallback to sendResponse's handling\n            if (pendingWaitUntil) {\n              if (context.renderOpts.waitUntil) {\n                context.renderOpts.waitUntil(pendingWaitUntil)\n                pendingWaitUntil = undefined\n              }\n            }\n\n            // Send the response now that we have copied it into the cache.\n            await sendResponse(\n              req,\n              res,\n              response,\n              context.renderOpts.pendingWaitUntil\n            )\n            return null\n          } catch (err) {\n            await this.instrumentationOnRequestError(err, req, {\n              routerKind: 'App Router',\n              routePath: pathname,\n              routeType: 'route',\n              revalidateReason: getRevalidateReason(renderOpts),\n            })\n\n            // If this is during static generation, throw the error again.\n            if (isSSG) throw err\n\n            Log.error(err)\n\n            // Otherwise, send a 500 response.\n            await sendResponse(req, res, new Response(null, { status: 500 }))\n\n            return null\n          }\n        } else if (\n          isPagesRouteModule(routeModule) ||\n          isAppPageRouteModule(routeModule)\n        ) {\n          // An OPTIONS request to a page handler is invalid.\n          if (req.method === 'OPTIONS' && !is404Page) {\n            await sendResponse(req, res, new Response(null, { status: 400 }))\n            return null\n          }\n\n          if (isPagesRouteModule(routeModule)) {\n            // Due to the way we pass data by mutating `renderOpts`, we can't extend\n            // the object here but only updating its `clientReferenceManifest` and\n            // `nextFontManifest` properties.\n            // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n            renderOpts.nextFontManifest = this.nextFontManifest\n            renderOpts.clientReferenceManifest =\n              components.clientReferenceManifest\n\n            const request = isNodeNextRequest(req) ? req.originalRequest : req\n            const response = isNodeNextResponse(res)\n              ? res.originalResponse\n              : res\n\n            // Call the built-in render method on the module.\n            try {\n              result = await routeModule.render(\n                request as any,\n                response as any,\n                {\n                  page: pathname,\n                  params: opts.params,\n                  query,\n                  renderOpts,\n                  sharedContext: {\n                    buildId: this.buildId,\n                    deploymentId: this.nextConfig.deploymentId,\n                    customServer: this.serverOptions.customServer || undefined,\n                  },\n                  renderContext: {\n                    isFallback: pagesFallback,\n                    isDraftMode: renderOpts.isDraftMode,\n                    developmentNotFoundSourcePage: getRequestMeta(\n                      req,\n                      'developmentNotFoundSourcePage'\n                    ),\n                  },\n                }\n              )\n            } catch (err) {\n              await this.instrumentationOnRequestError(err, req, {\n                routerKind: 'Pages Router',\n                routePath: pathname,\n                routeType: 'render',\n                revalidateReason: getRevalidateReason({\n                  isRevalidate: isSSG,\n                  isOnDemandRevalidate: renderOpts.isOnDemandRevalidate,\n                }),\n              })\n              throw err\n            }\n          } else {\n            const module = components.routeModule as AppPageRouteModule\n\n            // Due to the way we pass data by mutating `renderOpts`, we can't extend the\n            // object here but only updating its `nextFontManifest` field.\n            // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n            renderOpts.nextFontManifest = this.nextFontManifest\n\n            const context: AppPageRouteHandlerContext = {\n              page: is404Page ? '/404' : pathname,\n              params: opts.params,\n              query,\n              fallbackRouteParams,\n              renderOpts,\n              serverComponentsHmrCache: this.getServerComponentsHmrCache(),\n              sharedContext: {\n                buildId: this.buildId,\n              },\n            }\n\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (\n              this.nextConfig.experimental.dynamicIO &&\n              this.renderOpts.dev &&\n              !isPrefetchRSCRequest &&\n              !isPossibleServerAction\n            ) {\n              const warmup = await module.warmup(req, res, context)\n\n              // If the warmup is successful, we should use the resume data\n              // cache from the warmup.\n              if (warmup.metadata.devRenderResumeDataCache) {\n                renderOpts.devRenderResumeDataCache =\n                  warmup.metadata.devRenderResumeDataCache\n              }\n            }\n\n            // Call the built-in render method on the module.\n            result = await module.render(req, res, context)\n          }\n        } else {\n          throw new Error('Invariant: Unknown route module type')\n        }\n      } else {\n        // If we didn't match a page, we should fallback to using the legacy\n        // render method.\n        result = await this.renderHTML(req, res, pathname, query, renderOpts)\n      }\n\n      const { metadata } = result\n\n      const {\n        cacheControl,\n        headers = {},\n        // Add any fetch tags that were on the page to the response headers.\n        fetchTags: cacheTags,\n      } = metadata\n\n      if (cacheTags) {\n        headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n      }\n\n      // Pull any fetch metrics from the render onto the request.\n      ;(req as any).fetchMetrics = metadata.fetchMetrics\n\n      // we don't throw static to dynamic errors in dev as isSSG\n      // is a best guess in dev since we don't have the prerender pass\n      // to know whether the path is actually static or not\n      if (\n        isAppPath &&\n        isSSG &&\n        cacheControl?.revalidate === 0 &&\n        !this.renderOpts.dev &&\n        !isRoutePPREnabled\n      ) {\n        const staticBailoutInfo = metadata.staticBailoutInfo\n\n        const err = new Error(\n          `Page changed from static to dynamic at runtime ${urlPathname}${\n            staticBailoutInfo?.description\n              ? `, reason: ${staticBailoutInfo.description}`\n              : ``\n          }` +\n            `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`\n        )\n\n        if (staticBailoutInfo?.stack) {\n          const stack = staticBailoutInfo.stack\n          err.stack = err.message + stack.substring(stack.indexOf('\\n'))\n        }\n\n        throw err\n      }\n\n      // Based on the metadata, we can determine what kind of cache result we\n      // should return.\n\n      // Handle `isNotFound`.\n      if ('isNotFound' in metadata && metadata.isNotFound) {\n        return {\n          value: null,\n          cacheControl,\n          isFallback: false,\n        } satisfies ResponseCacheEntry\n      }\n\n      // Handle `isRedirect`.\n      if (metadata.isRedirect) {\n        return {\n          value: {\n            kind: CachedRouteKind.REDIRECT,\n            props: metadata.pageData ?? metadata.flightData,\n          } satisfies CachedRedirectValue,\n          cacheControl,\n          isFallback: false,\n        } satisfies ResponseCacheEntry\n      }\n\n      // Handle `isNull`.\n      if (result.isNull) {\n        return null\n      }\n\n      // We now have a valid HTML result that we can return to the user.\n      if (isAppPath) {\n        return {\n          value: {\n            kind: CachedRouteKind.APP_PAGE,\n            html: result,\n            headers,\n            rscData: metadata.flightData,\n            postponed: metadata.postponed,\n            status: res.statusCode,\n            segmentData: metadata.segmentData,\n          } satisfies CachedAppPageValue,\n          cacheControl,\n          isFallback: !!fallbackRouteParams,\n        } satisfies ResponseCacheEntry\n      }\n\n      return {\n        value: {\n          kind: CachedRouteKind.PAGES,\n          html: result,\n          pageData: metadata.pageData ?? metadata.flightData,\n          headers,\n          status: isAppPath ? res.statusCode : undefined,\n        } satisfies CachedPageValue,\n        cacheControl,\n        isFallback: pagesFallback,\n      }\n    }\n\n    let responseGenerator: ResponseGenerator = async ({\n      hasResolved,\n      previousCacheEntry,\n      isRevalidating,\n    }): Promise<ResponseCacheEntry | null> => {\n      const isProduction = !this.renderOpts.dev\n      const didRespond = hasResolved || res.sent\n\n      // If we haven't found the static paths for the route, then do it now.\n      if (!staticPaths && isDynamic) {\n        if (hasGetStaticPaths) {\n          const pathsResult = await this.getStaticPaths({\n            pathname,\n            requestHeaders: req.headers,\n            isAppPath,\n            page: components.page,\n          })\n\n          staticPaths = pathsResult.staticPaths\n          fallbackMode = pathsResult.fallbackMode\n        } else {\n          staticPaths = undefined\n          fallbackMode = FallbackMode.NOT_FOUND\n        }\n      }\n\n      // When serving a bot request, we want to serve a blocking render and not\n      // the prerendered page. This ensures that the correct content is served\n      // to the bot in the head.\n      if (\n        fallbackMode === FallbackMode.PRERENDER &&\n        isBot(req.headers['user-agent'] || '')\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // skip on-demand revalidate if cache is not present and\n      // revalidate-if-generated is set\n      if (\n        isOnDemandRevalidate &&\n        revalidateOnlyGenerated &&\n        !previousCacheEntry &&\n        !this.minimalMode\n      ) {\n        await this.render404(req, res)\n        return null\n      }\n\n      if (previousCacheEntry?.isStale === -1) {\n        isOnDemandRevalidate = true\n      }\n\n      // TODO: adapt for PPR\n      // only allow on-demand revalidate for fallback: true/blocking\n      // or for prerendered fallback: false paths\n      if (\n        isOnDemandRevalidate &&\n        (fallbackMode !== FallbackMode.NOT_FOUND || previousCacheEntry)\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // We use `ssgCacheKey` here as it is normalized to match the encoding\n      // from getStaticPaths along with including the locale.\n      //\n      // We use the `resolvedUrlPathname` for the development case when this\n      // is an app path since it doesn't include locale information.\n      //\n      // We decode the `resolvedUrlPathname` to correctly match the app path\n      // with prerendered paths.\n      let staticPathKey = ssgCacheKey\n      if (!staticPathKey && opts.dev && isAppPath) {\n        staticPathKey = decodePathParams(resolvedUrlPathname)\n      }\n      if (staticPathKey && query.amp) {\n        staticPathKey = staticPathKey.replace(/\\.amp$/, '')\n      }\n\n      const isPageIncludedInStaticPaths =\n        staticPathKey && staticPaths?.includes(staticPathKey)\n\n      // When experimental compile is used, no pages have been prerendered,\n      // so they should all be blocking.\n\n      // @ts-expect-error internal field\n      if (this.nextConfig.experimental.isExperimentalCompile) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      // When we did not respond from cache, we need to choose to block on\n      // rendering or return a skeleton.\n      //\n      // - Data requests always block.\n      // - Blocking mode fallback always blocks.\n      // - Preview mode toggles all pages to be resolved in a blocking manner.\n      // - Non-dynamic pages should block (though this is an impossible\n      //   case in production).\n      // - Dynamic pages should return their skeleton if not defined in\n      //   getStaticPaths, then finish the data request on the client-side.\n      //\n      if (\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        !this.minimalMode &&\n        fallbackMode !== FallbackMode.BLOCKING_STATIC_RENDER &&\n        staticPathKey &&\n        !didRespond &&\n        !isPreviewMode &&\n        isDynamic &&\n        (isProduction || !staticPaths || !isPageIncludedInStaticPaths)\n      ) {\n        if (\n          // In development, fall through to render to handle missing\n          // getStaticPaths.\n          (isProduction || (staticPaths && staticPaths?.length > 0)) &&\n          // When fallback isn't present, abort this render so we 404\n          fallbackMode === FallbackMode.NOT_FOUND\n        ) {\n          throw new NoFallbackError()\n        }\n\n        let fallbackResponse: ResponseCacheEntry | null | undefined\n\n        // If this is a pages router page.\n        if (isPagesRouteModule(components.routeModule) && !isNextDataRequest) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await this.responseCache.get(\n            isProduction ? (locale ? `/${locale}${pathname}` : pathname) : null,\n            // This is the response generator for the fallback shell.\n            async ({\n              previousCacheEntry: previousFallbackCacheEntry = null,\n            }) => {\n              // For the pages router, fallbacks cannot be revalidated or\n              // generated in production. In the case of a missing fallback,\n              // we return null, but if it's being revalidated, we just return\n              // the previous fallback cache entry. This preserves the previous\n              // behavior.\n              if (isProduction) {\n                return toResponseCacheEntry(previousFallbackCacheEntry)\n              }\n\n              // We pass `undefined` and `null` as it doesn't apply to the pages\n              // router.\n              return doRender({\n                postponed: undefined,\n                // For the pages router, fallbacks can only be generated on\n                // demand in development, so if we're not in production, and we\n                // aren't a app path.\n                pagesFallback: true,\n                fallbackRouteParams: null,\n              })\n            },\n            {\n              routeKind: RouteKind.PAGES,\n              incrementalCache,\n              isRoutePPREnabled,\n              isFallback: true,\n            }\n          )\n        }\n        // If this is a app router page, PPR is enabled, and PFPR is also\n        // enabled, then we should use the fallback renderer.\n        else if (\n          isRoutePPREnabled &&\n          isAppPageRouteModule(components.routeModule) &&\n          !isRSCRequest\n        ) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await this.responseCache.get(\n            isProduction ? pathname : null,\n            // This is the response generator for the fallback shell.\n            async () =>\n              doRender({\n                // We pass `undefined` as rendering a fallback isn't resumed\n                // here.\n                postponed: undefined,\n                pagesFallback: undefined,\n                fallbackRouteParams:\n                  // If we're in production of we're debugging the fallback\n                  // shell then we should postpone when dynamic params are\n                  // accessed.\n                  isProduction || isDebugFallbackShell\n                    ? getFallbackRouteParams(pathname)\n                    : null,\n              }),\n            {\n              routeKind: RouteKind.APP_PAGE,\n              incrementalCache,\n              isRoutePPREnabled,\n              isFallback: true,\n            }\n          )\n        }\n\n        // If the fallback response was set to null, then we should return null.\n        if (fallbackResponse === null) return null\n\n        // Otherwise, if we did get a fallback response, we should return it.\n        if (fallbackResponse) {\n          // Remove the cache control from the response to prevent it from being\n          // used in the surrounding cache.\n          delete fallbackResponse.cacheControl\n\n          return fallbackResponse\n        }\n      }\n\n      // Only requests that aren't revalidating can be resumed. If we have the\n      // minimal postponed data, then we should resume the render with it.\n      const postponed =\n        !isOnDemandRevalidate && !isRevalidating && minimalPostponed\n          ? minimalPostponed\n          : undefined\n\n      // When we're in minimal mode, if we're trying to debug the static shell,\n      // we should just return nothing instead of resuming the dynamic render.\n      if (\n        (isDebugStaticShell || isDebugDynamicAccesses) &&\n        typeof postponed !== 'undefined'\n      ) {\n        return {\n          cacheControl: { revalidate: 1, expire: undefined },\n          isFallback: false,\n          value: {\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(''),\n            pageData: {},\n            headers: undefined,\n            status: undefined,\n          } satisfies CachedPageValue,\n        }\n      }\n\n      // If this is a dynamic route with PPR enabled and the default route\n      // matches were set, then we should pass the fallback route params to\n      // the renderer as this is a fallback revalidation request.\n      const fallbackRouteParams =\n        isDynamic &&\n        isRoutePPREnabled &&\n        (getRequestMeta(req, 'didSetDefaultRouteMatches') ||\n          isDebugFallbackShell)\n          ? getFallbackRouteParams(pathname)\n          : null\n\n      // Perform the render.\n      return doRender({\n        postponed,\n        pagesFallback: undefined,\n        fallbackRouteParams,\n      })\n    }\n\n    const cacheEntry = await this.responseCache.get(\n      ssgCacheKey,\n      responseGenerator,\n      {\n        routeKind:\n          // If the route module is not defined, we can assume it's a page being\n          // rendered and thus check isAppPath.\n          routeModule?.definition.kind ??\n          (isAppPath ? RouteKind.APP_PAGE : RouteKind.PAGES),\n        incrementalCache,\n        isOnDemandRevalidate,\n        isPrefetch: req.headers.purpose === 'prefetch',\n        isRoutePPREnabled,\n      }\n    )\n\n    if (isPreviewMode) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    if (!cacheEntry) {\n      if (ssgCacheKey && !(isOnDemandRevalidate && revalidateOnlyGenerated)) {\n        // A cache entry might not be generated if a response is written\n        // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n        // have a cache key. If we do have a cache key but we don't end up\n        // with a cache entry, then either Next.js or the application has a\n        // bug that needs fixing.\n        throw new Error('invariant: cache entry required but not generated')\n      }\n      return null\n    }\n\n    // If we're not in minimal mode and the cache entry that was returned was a\n    // app page fallback, then we need to kick off the dynamic shell generation.\n    if (\n      ssgCacheKey &&\n      !this.minimalMode &&\n      isRoutePPREnabled &&\n      cacheEntry.value?.kind === CachedRouteKind.APP_PAGE &&\n      cacheEntry.isFallback &&\n      !isOnDemandRevalidate &&\n      // When we're debugging the fallback shell, we don't want to regenerate\n      // the route shell.\n      !isDebugFallbackShell &&\n      process.env.DISABLE_ROUTE_SHELL_GENERATION !== 'true'\n    ) {\n      scheduleOnNextTick(async () => {\n        try {\n          await this.responseCache.get(\n            ssgCacheKey,\n            () =>\n              doRender({\n                // We're an on-demand request, so we don't need to pass in the\n                // fallbackRouteParams.\n                fallbackRouteParams: null,\n                pagesFallback: undefined,\n                postponed: undefined,\n              }),\n            {\n              routeKind: RouteKind.APP_PAGE,\n              incrementalCache,\n              isOnDemandRevalidate: true,\n              isPrefetch: false,\n              isRoutePPREnabled: true,\n            }\n          )\n        } catch (err) {\n          console.error('Error occurred while rendering dynamic shell', err)\n        }\n      })\n    }\n\n    const didPostpone =\n      cacheEntry.value?.kind === CachedRouteKind.APP_PAGE &&\n      typeof cacheEntry.value.postponed === 'string'\n\n    if (\n      isSSG &&\n      // We don't want to send a cache header for requests that contain dynamic\n      // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n      // request, then we should set the cache header.\n      !isDynamicRSCRequest &&\n      (!didPostpone || isPrefetchRSCRequest)\n    ) {\n      if (!this.minimalMode) {\n        // set x-nextjs-cache header to match the header\n        // we set for the image-optimizer\n        res.setHeader(\n          'x-nextjs-cache',\n          isOnDemandRevalidate\n            ? 'REVALIDATED'\n            : cacheEntry.isMiss\n              ? 'MISS'\n              : cacheEntry.isStale\n                ? 'STALE'\n                : 'HIT'\n        )\n      }\n      // Set a header used by the client router to signal the response is static\n      // and should respect the `static` cache staleTime value.\n      res.setHeader(NEXT_IS_PRERENDER_HEADER, '1')\n    }\n\n    const { value: cachedData } = cacheEntry\n\n    // If the cache value is an image, we should error early.\n    if (cachedData?.kind === CachedRouteKind.IMAGE) {\n      throw new InvariantError('SSG should not return an image cache value')\n    }\n\n    // Coerce the cache control parameter from the render.\n    let cacheControl: CacheControl | undefined\n\n    // If this is a resume request in minimal mode it is streamed with dynamic\n    // content and should not be cached.\n    if (minimalPostponed) {\n      cacheControl = { revalidate: 0, expire: undefined }\n    }\n\n    // If this is in minimal mode and this is a flight request that isn't a\n    // prefetch request while PPR is enabled, it cannot be cached as it contains\n    // dynamic content.\n    else if (\n      this.minimalMode &&\n      isRSCRequest &&\n      !isPrefetchRSCRequest &&\n      isRoutePPREnabled\n    ) {\n      cacheControl = { revalidate: 0, expire: undefined }\n    } else if (!this.renderOpts.dev || (hasServerProps && !isNextDataRequest)) {\n      // If this is a preview mode request, we shouldn't cache it\n      if (isPreviewMode) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      }\n\n      // If this isn't SSG, then we should set change the header only if it is\n      // not set already.\n      else if (!isSSG) {\n        if (!res.getHeader('Cache-Control')) {\n          cacheControl = { revalidate: 0, expire: undefined }\n        }\n      }\n\n      // If we are rendering the 404 page we derive the cache-control\n      // revalidate period from the value that trigged the not found\n      // to be rendered. So if `getStaticProps` returns\n      // { notFound: true, revalidate 60 } the revalidate period should\n      // be 60 but if a static asset 404s directly it should have a revalidate\n      // period of 0 so that it doesn't get cached unexpectedly by a CDN\n      else if (is404Page) {\n        const notFoundRevalidate = getRequestMeta(req, 'notFoundRevalidate')\n\n        cacheControl = {\n          revalidate:\n            typeof notFoundRevalidate === 'undefined' ? 0 : notFoundRevalidate,\n          expire: undefined,\n        }\n      } else if (is500Page) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      } else if (cacheEntry.cacheControl) {\n        // If the cache entry has a cache control with a revalidate value that's\n        // a number, use it.\n        if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n          if (cacheEntry.cacheControl.revalidate < 1) {\n            throw new Error(\n              `Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`\n            )\n          }\n\n          cacheControl = {\n            revalidate: cacheEntry.cacheControl.revalidate,\n            expire:\n              cacheEntry.cacheControl?.expire ?? this.nextConfig.expireTime,\n          }\n        }\n        // Otherwise if the revalidate value is false, then we should use the\n        // cache time of one year.\n        else {\n          cacheControl = { revalidate: CACHE_ONE_YEAR, expire: undefined }\n        }\n      }\n    }\n\n    cacheEntry.cacheControl = cacheControl\n\n    if (\n      typeof segmentPrefetchHeader === 'string' &&\n      cachedData?.kind === CachedRouteKind.APP_PAGE &&\n      cachedData.segmentData\n    ) {\n      // This is a prefetch request issued by the client Segment Cache. These\n      // should never reach the application layer (lambda). We should either\n      // respond from the cache (HIT) or respond with 204 No Content (MISS).\n\n      // Set a header to indicate that PPR is enabled for this route. This\n      // lets the client distinguish between a regular cache miss and a cache\n      // miss due to PPR being disabled. In other contexts this header is used\n      // to indicate that the response contains dynamic data, but here we're\n      // only using it to indicate that the feature is enabled — the segment\n      // response itself contains whether the data is dynamic.\n      res.setHeader(NEXT_DID_POSTPONE_HEADER, '2')\n\n      // Add the cache tags header to the response if it exists and we're in\n      // minimal mode while rendering a static page.\n      const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n      if (this.minimalMode && isSSG && tags && typeof tags === 'string') {\n        res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n      }\n\n      const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader)\n      if (matchedSegment !== undefined) {\n        // Cache hit\n        return {\n          type: 'rsc',\n          body: RenderResult.fromStatic(matchedSegment),\n          // TODO: Eventually this should use cache control of the individual\n          // segment, not the whole page.\n          cacheControl: cacheEntry.cacheControl,\n        }\n      }\n\n      // Cache miss. Either a cache entry for this route has not been generated\n      // (which technically should not be possible when PPR is enabled, because\n      // at a minimum there should always be a fallback entry) or there's no\n      // match for the requested segment. Respond with a 204 No Content. We\n      // don't bother to respond with 404, because these requests are only\n      // issued as part of a prefetch.\n      res.statusCode = 204\n      return {\n        type: 'rsc',\n        body: RenderResult.fromStatic(''),\n        cacheControl: cacheEntry?.cacheControl,\n      }\n    }\n\n    // If there's a callback for `onCacheEntry`, call it with the cache entry\n    // and the revalidate options.\n    const onCacheEntry = getRequestMeta(req, 'onCacheEntry')\n    if (onCacheEntry) {\n      const finished = await onCacheEntry(\n        {\n          ...cacheEntry,\n          // TODO: remove this when upstream doesn't\n          // always expect this value to be \"PAGE\"\n          value: {\n            ...cacheEntry.value,\n            kind:\n              cacheEntry.value?.kind === CachedRouteKind.APP_PAGE\n                ? 'PAGE'\n                : cacheEntry.value?.kind,\n          },\n        },\n        {\n          url: getRequestMeta(req, 'initURL'),\n        }\n      )\n      if (finished) {\n        // TODO: maybe we have to end the request?\n        return null\n      }\n    }\n\n    if (!cachedData) {\n      // add revalidate metadata before rendering 404 page\n      // so that we can use this as source of truth for the\n      // cache-control header instead of what the 404 page returns\n      // for the revalidate value\n      addRequestMeta(\n        req,\n        'notFoundRevalidate',\n        cacheEntry.cacheControl?.revalidate\n      )\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (cacheEntry.cacheControl && !res.getHeader('Cache-Control')) {\n        res.setHeader(\n          'Cache-Control',\n          getCacheControlHeader(cacheEntry.cacheControl)\n        )\n      }\n      if (isNextDataRequest) {\n        res.statusCode = 404\n        res.body('{\"notFound\":true}').send()\n        return null\n      }\n\n      if (this.renderOpts.dev) {\n        addRequestMeta(req, 'developmentNotFoundSourcePage', pathname)\n      }\n      await this.render404(req, res, { pathname, query }, false)\n      return null\n    } else if (cachedData.kind === CachedRouteKind.REDIRECT) {\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (cacheEntry.cacheControl && !res.getHeader('Cache-Control')) {\n        res.setHeader(\n          'Cache-Control',\n          getCacheControlHeader(cacheEntry.cacheControl)\n        )\n      }\n\n      if (isNextDataRequest) {\n        return {\n          type: 'json',\n          body: RenderResult.fromStatic(\n            // @TODO: Handle flight data.\n            JSON.stringify(cachedData.props)\n          ),\n          cacheControl: cacheEntry.cacheControl,\n        }\n      } else {\n        await handleRedirect(cachedData.props)\n        return null\n      }\n    } else if (cachedData.kind === CachedRouteKind.APP_ROUTE) {\n      const headers = fromNodeOutgoingHttpHeaders(cachedData.headers)\n\n      if (!(this.minimalMode && isSSG)) {\n        headers.delete(NEXT_CACHE_TAGS_HEADER)\n      }\n\n      // If cache control is already set on the response we don't\n      // override it to allow users to customize it via next.config\n      if (\n        cacheEntry.cacheControl &&\n        !res.getHeader('Cache-Control') &&\n        !headers.get('Cache-Control')\n      ) {\n        headers.set(\n          'Cache-Control',\n          getCacheControlHeader(cacheEntry.cacheControl)\n        )\n      }\n\n      await sendResponse(\n        req,\n        res,\n        new Response(cachedData.body, {\n          headers,\n          status: cachedData.status || 200,\n        })\n      )\n      return null\n    } else if (cachedData.kind === CachedRouteKind.APP_PAGE) {\n      // If the request has a postponed state and it's a resume request we\n      // should error.\n      if (didPostpone && minimalPostponed) {\n        throw new Error(\n          'Invariant: postponed state should not be present on a resume request'\n        )\n      }\n\n      if (cachedData.headers) {\n        const headers = { ...cachedData.headers }\n\n        if (!this.minimalMode || !isSSG) {\n          delete headers[NEXT_CACHE_TAGS_HEADER]\n        }\n\n        for (let [key, value] of Object.entries(headers)) {\n          if (typeof value === 'undefined') continue\n\n          if (Array.isArray(value)) {\n            for (const v of value) {\n              res.appendHeader(key, v)\n            }\n          } else if (typeof value === 'number') {\n            value = value.toString()\n            res.appendHeader(key, value)\n          } else {\n            res.appendHeader(key, value)\n          }\n        }\n      }\n\n      // Add the cache tags header to the response if it exists and we're in\n      // minimal mode while rendering a static page.\n      const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n      if (this.minimalMode && isSSG && tags && typeof tags === 'string') {\n        res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n      }\n\n      // If the request is a data request, then we shouldn't set the status code\n      // from the response because it should always be 200. This should be gated\n      // behind the experimental PPR flag.\n      if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n        res.statusCode = cachedData.status\n      }\n\n      // Mark that the request did postpone.\n      if (didPostpone) {\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n      }\n\n      // we don't go through this block when preview mode is true\n      // as preview mode is a dynamic request (bypasses cache) and doesn't\n      // generate both HTML and payloads in the same request so continue to just\n      // return the generated payload\n      if (isRSCRequest && !isPreviewMode) {\n        // If this is a dynamic RSC request, then stream the response.\n        if (typeof cachedData.rscData === 'undefined') {\n          if (cachedData.postponed) {\n            throw new Error('Invariant: Expected postponed to be undefined')\n          }\n\n          return {\n            type: 'rsc',\n            body: cachedData.html,\n            // Dynamic RSC responses cannot be cached, even if they're\n            // configured with `force-static` because we have no way of\n            // distinguishing between `force-static` and pages that have no\n            // postponed state.\n            // TODO: distinguish `force-static` from pages with no postponed state (static)\n            cacheControl: isDynamicRSCRequest\n              ? { revalidate: 0, expire: undefined }\n              : cacheEntry.cacheControl,\n          }\n        }\n\n        // As this isn't a prefetch request, we should serve the static flight\n        // data.\n        return {\n          type: 'rsc',\n          body: RenderResult.fromStatic(cachedData.rscData),\n          cacheControl: cacheEntry.cacheControl,\n        }\n      }\n\n      // This is a request for HTML data.\n      let body = cachedData.html\n\n      // If there's no postponed state, we should just serve the HTML. This\n      // should also be the case for a resume request because it's completed\n      // as a server render (rather than a static render).\n      if (!didPostpone || this.minimalMode) {\n        return {\n          type: 'html',\n          body,\n          cacheControl: cacheEntry.cacheControl,\n        }\n      }\n\n      // If we're debugging the static shell or the dynamic API accesses, we\n      // should just serve the HTML without resuming the render. The returned\n      // HTML will be the static shell so all the Dynamic API's will be used\n      // during static generation.\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        // Since we're not resuming the render, we need to at least add the\n        // closing body and html tags to create valid HTML.\n        body.chain(\n          new ReadableStream({\n            start(controller) {\n              controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n              controller.close()\n            },\n          })\n        )\n\n        return {\n          type: 'html',\n          body,\n          cacheControl: { revalidate: 0, expire: undefined },\n        }\n      }\n\n      // This request has postponed, so let's create a new transformer that the\n      // dynamic data can pipe to that will attach the dynamic data to the end\n      // of the response.\n      const transformer = new TransformStream<Uint8Array, Uint8Array>()\n      body.chain(transformer.readable)\n\n      // Perform the render again, but this time, provide the postponed state.\n      // We don't await because we want the result to start streaming now, and\n      // we've already chained the transformer's readable to the render result.\n      doRender({\n        postponed: cachedData.postponed,\n        pagesFallback: undefined,\n        // This is a resume render, not a fallback render, so we don't need to\n        // set this.\n        fallbackRouteParams: null,\n      })\n        .then(async (result) => {\n          if (!result) {\n            throw new Error('Invariant: expected a result to be returned')\n          }\n\n          if (result.value?.kind !== CachedRouteKind.APP_PAGE) {\n            throw new Error(\n              `Invariant: expected a page response, got ${result.value?.kind}`\n            )\n          }\n\n          // Pipe the resume result to the transformer.\n          await result.value.html.pipeTo(transformer.writable)\n        })\n        .catch((err) => {\n          // An error occurred during piping or preparing the render, abort\n          // the transformers writer so we can terminate the stream.\n          transformer.writable.abort(err).catch((e) => {\n            console.error(\"couldn't abort transformer\", e)\n          })\n        })\n\n      return {\n        type: 'html',\n        body,\n        // We don't want to cache the response if it has postponed data because\n        // the response being sent to the client it's dynamic parts are streamed\n        // to the client on the same request.\n        cacheControl: { revalidate: 0, expire: undefined },\n      }\n    } else if (isNextDataRequest) {\n      return {\n        type: 'json',\n        body: RenderResult.fromStatic(JSON.stringify(cachedData.pageData)),\n        cacheControl: cacheEntry.cacheControl,\n      }\n    } else {\n      return {\n        type: 'html',\n        body: cachedData.html,\n        cacheControl: cacheEntry.cacheControl,\n      }\n    }\n  }\n\n  private stripNextDataPath(path: string, stripLocale = true) {\n    if (path.includes(this.buildId)) {\n      const splitPath = path.substring(\n        path.indexOf(this.buildId) + this.buildId.length\n      )\n\n      path = denormalizePagePath(splitPath.replace(/\\.json$/, ''))\n    }\n\n    if (this.localeNormalizer && stripLocale) {\n      return this.localeNormalizer.normalize(path)\n    }\n    return path\n  }\n\n  // map the route to the actual bundle name\n  protected getOriginalAppPaths(route: string) {\n    if (this.enabledDirectories.app) {\n      const originalAppPath = this.appPathRoutes?.[route]\n\n      if (!originalAppPath) {\n        return null\n      }\n\n      return originalAppPath\n    }\n    return null\n  }\n\n  protected async renderPageComponent(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    bubbleNoFallback: boolean\n  ) {\n    const { query, pathname } = ctx\n\n    const appPaths = this.getOriginalAppPaths(pathname)\n    const isAppPath = Array.isArray(appPaths)\n\n    let page = pathname\n    if (isAppPath) {\n      // the last item in the array is the root page, if there are parallel routes\n      page = appPaths[appPaths.length - 1]\n    }\n\n    const result = await this.findPageComponents({\n      locale: getRequestMeta(ctx.req, 'locale'),\n      page,\n      query,\n      params: ctx.renderOpts.params || {},\n      isAppPath,\n      sriEnabled: !!this.nextConfig.experimental.sri?.algorithm,\n      appPaths,\n      // Ensuring for loading page component routes is done via the matcher.\n      shouldEnsure: false,\n    })\n    if (result) {\n      getTracer().setRootSpanAttribute('next.route', pathname)\n      try {\n        return await this.renderToResponseWithComponents(ctx, result)\n      } catch (err) {\n        const isNoFallbackError = err instanceof NoFallbackError\n\n        if (!isNoFallbackError || (isNoFallbackError && bubbleNoFallback)) {\n          throw err\n        }\n      }\n    }\n    return false\n  }\n\n  private async renderToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(\n      BaseServerSpan.renderToResponse,\n      {\n        spanName: `rendering page`,\n        attributes: {\n          'next.route': ctx.pathname,\n        },\n      },\n      async () => {\n        return this.renderToResponseImpl(ctx)\n      }\n    )\n  }\n\n  protected abstract getMiddleware(): Promise<MiddlewareRoutingItem | undefined>\n  protected abstract getFallbackErrorComponents(\n    url?: string\n  ): Promise<LoadComponentsReturnType | null>\n  protected abstract getRoutesManifest(): NormalizedRouteManifest | undefined\n\n  private async renderToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>\n  ): Promise<ResponsePayload | null> {\n    const { req, res, query, pathname } = ctx\n    let page = pathname\n    const bubbleNoFallback =\n      getRequestMeta(ctx.req, 'bubbleNoFallback') ?? false\n    delete query[NEXT_RSC_UNION_QUERY]\n\n    const options: MatchOptions = {\n      i18n: this.i18nProvider?.fromRequest(req, pathname),\n    }\n\n    try {\n      for await (const match of this.matchers.matchAll(pathname, options)) {\n        // when a specific invoke-output is meant to be matched\n        // ensure a prior dynamic route/page doesn't take priority\n        const invokeOutput = getRequestMeta(ctx.req, 'invokeOutput')\n        if (\n          !this.minimalMode &&\n          typeof invokeOutput === 'string' &&\n          isDynamicRoute(invokeOutput || '') &&\n          invokeOutput !== match.definition.pathname\n        ) {\n          continue\n        }\n\n        const result = await this.renderPageComponent(\n          {\n            ...ctx,\n            pathname: match.definition.pathname,\n            renderOpts: {\n              ...ctx.renderOpts,\n              params: match.params,\n            },\n          },\n          bubbleNoFallback\n        )\n        if (result !== false) return result\n      }\n\n      // currently edge functions aren't receiving the x-matched-path\n      // header so we need to fallback to matching the current page\n      // when we weren't able to match via dynamic route to handle\n      // the rewrite case\n      // @ts-expect-error extended in child class web-server\n      if (this.serverOptions.webServerConfig) {\n        // @ts-expect-error extended in child class web-server\n        ctx.pathname = this.serverOptions.webServerConfig.page\n        const result = await this.renderPageComponent(ctx, bubbleNoFallback)\n        if (result !== false) return result\n      }\n    } catch (error) {\n      const err = getProperError(error)\n\n      if (error instanceof MissingStaticPage) {\n        console.error(\n          'Invariant: failed to load static page',\n          JSON.stringify(\n            {\n              page,\n              url: ctx.req.url,\n              matchedPath: ctx.req.headers[MATCHED_PATH_HEADER],\n              initUrl: getRequestMeta(ctx.req, 'initURL'),\n              didRewrite: !!getRequestMeta(ctx.req, 'rewroteURL'),\n              rewroteUrl: getRequestMeta(ctx.req, 'rewroteURL'),\n            },\n            null,\n            2\n          )\n        )\n        throw err\n      }\n\n      if (err instanceof NoFallbackError && bubbleNoFallback) {\n        throw err\n      }\n      if (err instanceof DecodeError || err instanceof NormalizeError) {\n        res.statusCode = 400\n        return await this.renderErrorToResponse(ctx, err)\n      }\n\n      res.statusCode = 500\n\n      // if pages/500 is present we still need to trigger\n      // /_error `getInitialProps` to allow reporting error\n      if (await this.hasPage('/500')) {\n        addRequestMeta(ctx.req, 'customErrorRender', true)\n        await this.renderErrorToResponse(ctx, err)\n        removeRequestMeta(ctx.req, 'customErrorRender')\n      }\n\n      const isWrappedError = err instanceof WrappedBuildError\n\n      if (!isWrappedError) {\n        if (\n          (this.minimalMode && process.env.NEXT_RUNTIME !== 'edge') ||\n          this.renderOpts.dev\n        ) {\n          if (isError(err)) err.page = page\n          throw err\n        }\n        this.logError(getProperError(err))\n      }\n      const response = await this.renderErrorToResponse(\n        ctx,\n        isWrappedError ? (err as WrappedBuildError).innerError : err\n      )\n      return response\n    }\n\n    const middleware = await this.getMiddleware()\n    if (\n      middleware &&\n      !!ctx.req.headers['x-nextjs-data'] &&\n      (!res.statusCode || res.statusCode === 200 || res.statusCode === 404)\n    ) {\n      const locale = getRequestMeta(req, 'locale')\n\n      res.setHeader(\n        'x-nextjs-matched-path',\n        `${locale ? `/${locale}` : ''}${pathname}`\n      )\n      res.statusCode = 200\n      res.setHeader('content-type', 'application/json')\n      res.body('{}')\n      res.send()\n      return null\n    }\n\n    res.statusCode = 404\n    return this.renderErrorToResponse(ctx, null)\n  }\n\n  public async renderToHTML(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return getTracer().trace(BaseServerSpan.renderToHTML, async () => {\n      return this.renderToHTMLImpl(req, res, pathname, query)\n    })\n  }\n\n  private async renderToHTMLImpl(\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async renderError(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    return getTracer().trace(BaseServerSpan.renderError, async () => {\n      return this.renderErrorImpl(err, req, res, pathname, query, setHeaders)\n    })\n  }\n\n  private async renderErrorImpl(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: NextParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    if (setHeaders) {\n      res.setHeader(\n        'Cache-Control',\n        'private, no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    return this.pipe(\n      async (ctx) => {\n        const response = await this.renderErrorToResponse(ctx, err)\n        if (this.minimalMode && res.statusCode === 500) {\n          throw err\n        }\n        return response\n      },\n      { req, res, pathname, query }\n    )\n  }\n\n  private customErrorNo404Warn = execOnce(() => {\n    Log.warn(\n      `You have added a custom /_error page without a custom /404 page. This prevents the 404 page from being auto statically optimized.\\nSee here for info: https://nextjs.org/docs/messages/custom-error-no-custom-404`\n    )\n  })\n\n  private async renderErrorToResponse(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    return getTracer().trace(BaseServerSpan.renderErrorToResponse, async () => {\n      return this.renderErrorToResponseImpl(ctx, err)\n    })\n  }\n\n  protected async renderErrorToResponseImpl(\n    ctx: RequestContext<ServerRequest, ServerResponse>,\n    err: Error | null\n  ): Promise<ResponsePayload | null> {\n    // Short-circuit favicon.ico in development to avoid compiling 404 page when the app has no favicon.ico.\n    // Since favicon.ico is automatically requested by the browser.\n    if (this.renderOpts.dev && ctx.pathname === '/favicon.ico') {\n      return {\n        type: 'html',\n        body: RenderResult.fromStatic(''),\n      }\n    }\n    const { res, query } = ctx\n\n    try {\n      let result: null | FindComponentsResult = null\n\n      const is404 = res.statusCode === 404\n      let using404Page = false\n\n      if (is404) {\n        if (this.enabledDirectories.app) {\n          // Use the not-found entry in app directory\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n            query,\n            params: {},\n            isAppPath: true,\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n\n        if (!result && (await this.hasPage('/404'))) {\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: '/404',\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 404 route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n          using404Page = result !== null\n        }\n      }\n      let statusPage = `/${res.statusCode}`\n\n      if (\n        !getRequestMeta(ctx.req, 'customErrorRender') &&\n        !result &&\n        STATIC_STATUS_PAGES.includes(statusPage)\n      ) {\n        // skip ensuring /500 in dev mode as it isn't used and the\n        // dev overlay is used instead\n        if (statusPage !== '/500' || !this.renderOpts.dev) {\n          result = await this.findPageComponents({\n            locale: getRequestMeta(ctx.req, 'locale'),\n            page: statusPage,\n            query,\n            params: {},\n            isAppPath: false,\n            // Ensuring can't be done here because you never \"match\" a 500\n            // route.\n            shouldEnsure: true,\n            url: ctx.req.url,\n          })\n        }\n      }\n\n      if (!result) {\n        result = await this.findPageComponents({\n          locale: getRequestMeta(ctx.req, 'locale'),\n          page: '/_error',\n          query,\n          params: {},\n          isAppPath: false,\n          // Ensuring can't be done here because you never \"match\" an error\n          // route.\n          shouldEnsure: true,\n          url: ctx.req.url,\n        })\n        statusPage = '/_error'\n      }\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        !using404Page &&\n        (await this.hasPage('/_error')) &&\n        !(await this.hasPage('/404'))\n      ) {\n        this.customErrorNo404Warn()\n      }\n\n      if (!result) {\n        // this can occur when a project directory has been moved/deleted\n        // which is handled in the parent process in development\n        if (this.renderOpts.dev) {\n          return {\n            type: 'html',\n            // wait for dev-server to restart before refreshing\n            body: RenderResult.fromStatic(\n              `\n              <pre>missing required error components, refreshing...</pre>\n              <script>\n                async function check() {\n                  const res = await fetch(location.href).catch(() => ({}))\n\n                  if (res.status === 200) {\n                    location.reload()\n                  } else {\n                    setTimeout(check, 1000)\n                  }\n                }\n                check()\n              </script>`\n            ),\n          }\n        }\n\n        throw new WrappedBuildError(\n          new Error('missing required error components')\n        )\n      }\n\n      // If the page has a route module, use it for the new match. If it doesn't\n      // have a route module, remove the match.\n      if (result.components.routeModule) {\n        addRequestMeta(ctx.req, 'match', {\n          definition: result.components.routeModule.definition,\n          params: undefined,\n        })\n      } else {\n        removeRequestMeta(ctx.req, 'match')\n      }\n\n      try {\n        return await this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: statusPage,\n            renderOpts: {\n              ...ctx.renderOpts,\n              err,\n            },\n          },\n          result\n        )\n      } catch (maybeFallbackError) {\n        if (maybeFallbackError instanceof NoFallbackError) {\n          throw new Error('invariant: failed to render error page')\n        }\n        throw maybeFallbackError\n      }\n    } catch (error) {\n      const renderToHtmlError = getProperError(error)\n      const isWrappedError = renderToHtmlError instanceof WrappedBuildError\n      if (!isWrappedError) {\n        this.logError(renderToHtmlError)\n      }\n      res.statusCode = 500\n      const fallbackComponents = await this.getFallbackErrorComponents(\n        ctx.req.url\n      )\n\n      if (fallbackComponents) {\n        // There was an error, so use it's definition from the route module\n        // to add the match to the request.\n        addRequestMeta(ctx.req, 'match', {\n          definition: fallbackComponents.routeModule!.definition,\n          params: undefined,\n        })\n\n        return this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: '/_error',\n            renderOpts: {\n              ...ctx.renderOpts,\n              // We render `renderToHtmlError` here because `err` is\n              // already captured in the stacktrace.\n              err: isWrappedError\n                ? renderToHtmlError.innerError\n                : renderToHtmlError,\n            },\n          },\n          {\n            query,\n            components: fallbackComponents,\n          }\n        )\n      }\n      return {\n        type: 'html',\n        body: RenderResult.fromStatic('Internal Server Error'),\n      }\n    }\n  }\n\n  public async renderErrorToHTML(\n    err: Error | null,\n    req: ServerRequest,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderErrorToResponse(ctx, err), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async render404(\n    req: ServerRequest,\n    res: ServerResponse,\n    parsedUrl?: Pick<NextUrlWithParsedQuery, 'pathname' | 'query'>,\n    setHeaders = true\n  ): Promise<void> {\n    const { pathname, query } = parsedUrl ? parsedUrl : parseUrl(req.url!, true)\n\n    // Ensure the locales are provided on the request meta.\n    if (this.nextConfig.i18n) {\n      if (!getRequestMeta(req, 'locale')) {\n        addRequestMeta(req, 'locale', this.nextConfig.i18n.defaultLocale)\n      }\n      addRequestMeta(req, 'defaultLocale', this.nextConfig.i18n.defaultLocale)\n    }\n\n    res.statusCode = 404\n    return this.renderError(null, req, res, pathname!, query, setHeaders)\n  }\n}\n"], "names": ["getFallbackRouteParams", "CachedRouteKind", "NormalizeError", "DecodeError", "normalizeRepeatedSlashes", "MissingStaticPage", "format", "formatUrl", "parse", "parseUrl", "formatHostname", "getRedirectStatus", "isEdgeRuntime", "APP_PATHS_MANIFEST", "NEXT_BUILTIN_DOCUMENT", "PAGES_MANIFEST", "STATIC_STATUS_PAGES", "UNDERSCORE_NOT_FOUND_ROUTE", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "isDynamicRoute", "checkIsOnDemandRevalidate", "setConfig", "getCacheControlHeader", "execOnce", "isBlockedPage", "getBotType", "isBot", "RenderResult", "removeTrailingSlash", "denormalizePagePath", "Log", "getPreviouslyRevalidatedTags", "getUtils", "isError", "getProperError", "addRequestMeta", "getRequestMeta", "removeRequestMeta", "setRequestMeta", "removePathPrefix", "normalizeAppPath", "getHostname", "parseUrlUtil", "getNextPathnameInfo", "RSC_HEADER", "NEXT_RSC_UNION_QUERY", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_URL", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_IS_PRERENDER_HEADER", "LocaleRouteNormalizer", "DefaultRouteMatcherManager", "AppPageRouteMatcherProvider", "AppRouteRouteMatcherProvider", "PagesAPIRouteMatcherProvider", "PagesRouteMatcherProvider", "ServerManifestLoader", "getTracer", "isBubbledError", "SpanKind", "BaseServerSpan", "I18NProvider", "sendResponse", "fromNodeOutgoingHttpHeaders", "normalizeNextQueryParam", "toNodeOutgoingHttpHeaders", "CACHE_ONE_YEAR", "INFINITE_CACHE", "MATCHED_PATH_HEADER", "NEXT_CACHE_TAGS_HEADER", "NEXT_RESUME_HEADER", "normalizeLocalePath", "NextRequestAdapter", "signalFromNodeResponse", "matchNextDataPathname", "getRouteFromAssetPath", "decodePathParams", "RSCPathnameNormalizer", "stripFlightHeaders", "isAppPageRouteModule", "isAppRouteRouteModule", "isPagesRouteModule", "PrefetchRSCPathnameNormalizer", "NextDataPathnameNormalizer", "getIsPossibleServerAction", "isInterceptionRouteAppPath", "toRoute", "isNodeNextRequest", "isNodeNextResponse", "patchSetHeaderWithCookieSupport", "checkIsAppPPREnabled", "getBuiltinRequestContext", "ENCODED_TAGS", "NextRequestHint", "getRevalidateReason", "RouteKind", "FallbackMode", "parseFallbackField", "toResponseCacheEntry", "scheduleOnNextTick", "SegmentPrefixRSCPathnameNormalizer", "shouldServeStreamingMetadata", "isHtmlBotRequest", "InvariantError", "decodeQueryPathParameter", "getCacheHandlers", "NoFallbackError", "Error", "WrappedBuildError", "constructor", "innerError", "Server", "getServerComponentsHmrCache", "nextConfig", "experimental", "serverComponentsHmrCache", "globalThis", "__serverComponentsHmrCache", "undefined", "options", "handleRSCRequest", "req", "_res", "parsedUrl", "pathname", "normalizers", "segmentPrefetchRSC", "match", "result", "extract", "originalPathname", "segmentPath", "headers", "toLowerCase", "prefetchRSC", "normalize", "rsc", "segmentPrefetchRSCRequest", "url", "parsed", "handleNextDataRequest", "res", "middleware", "getMiddleware", "params", "path", "buildId", "process", "env", "NEXT_RUNTIME", "render404", "shift", "lastPara<PERSON>", "length", "endsWith", "join", "trailingSlash", "substring", "i18nProvider", "hostname", "host", "split", "domainLocale", "detectDomainLocale", "defaultLocale", "config", "localePathResult", "analyze", "detectedLocale", "handleNextImageRequest", "handleCatchallRenderRequest", "handleCatchallMiddlewareRequest", "data", "push", "normalizer", "normalizeAndAttachMetadata", "finished", "enabledDirectories", "pages", "prepared", "preparedPromise", "customErrorNo404Warn", "warn", "dir", "quiet", "conf", "dev", "minimalMode", "port", "experimentalTestProxy", "serverOptions", "require", "resolve", "loadEnvConfig", "fetchHostname", "distDir", "publicDir", "getPublicDir", "hasStaticDir", "getHasStaticDir", "i18n", "locales", "localeNormalizer", "serverRuntimeConfig", "publicRuntimeConfig", "assetPrefix", "generateEtags", "getBuildId", "minimalModeKey", "NEXT_PRIVATE_MINIMAL_MODE", "getEnabledDirectories", "isAppPPREnabled", "app", "ppr", "isAppSegmentPrefetchEnabled", "clientSegmentCache", "nextFontManifest", "getNextFontManifest", "NEXT_DEPLOYMENT_ID", "deploymentId", "renderOpts", "supportsDynamicResponse", "strictNextHead", "poweredByHeader", "canonicalBase", "amp", "previewProps", "getPrerenderManifest", "preview", "ampOptimizerConfig", "optimizer", "basePath", "images", "optimizeCss", "nextConfigOutput", "output", "nextScriptWorkers", "disableOptimizedLoading", "domainLocales", "domains", "serverComponents", "cacheLifeProfiles", "cacheLife", "enableTainting", "taint", "crossOrigin", "largePageDataBytes", "runtimeConfig", "Object", "keys", "isExperimentalCompile", "htmlLimitedBots", "expireTime", "staleTimes", "clientTraceMetadata", "dynamicIO", "Boolean", "dynamicOnHover", "inlineCss", "authInterrupts", "onInstrumentationRequestError", "instrumentationOnRequestError", "bind", "reactMaxHeadersLength", "pagesManifest", "getPagesManifest", "appPathsManifest", "getAppPathsManifest", "appPathRoutes", "getAppPathRoutes", "interceptionRoutePatterns", "getinterceptionRoutePatterns", "matchers", "getRouteMatchers", "reload", "setAssetPrefix", "responseCache", "getResponseCache", "reloadMatchers", "manifest<PERSON><PERSON>der", "name", "args", "err", "ctx", "instrumentation", "onRequestError", "method", "fromEntries", "entries", "handlerErr", "console", "error", "logError", "handleRequest", "prepare", "toUpperCase", "tracer", "withPropagatedContext", "trace", "spanName", "kind", "SERVER", "attributes", "span", "handleRequestImpl", "finally", "isRSCRequest", "setAttributes", "statusCode", "rootSpanAttributes", "getRootSpanAttributes", "get", "route", "updateName", "originalRequest", "waitTillReady", "originalResponse", "urlParts", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanUrl", "redirect", "body", "send", "query", "URLSearchParams", "xForwardedProto", "isHttps", "socket", "encrypted", "toString", "remoteAddress", "attachRequestMeta", "replace", "pathnameInfo", "useMatchedPathHeader", "<PERSON><PERSON><PERSON>", "URL", "urlPathname", "chunk", "postponed", "<PERSON><PERSON><PERSON>", "concat", "normalizedUrlPath", "stripNextDataPath", "localeAnalysisResult", "inferredFromDefault", "srcPathname", "pageIsDynamic", "definition", "utils", "page", "rewrites", "getRoutesManifest", "beforeFiles", "afterFiles", "fallback", "caseSensitive", "caseSensitiveRoutes", "locale", "pathnameBeforeRewrite", "rewriteParamKeys", "handleRewrites", "didRewrite", "queryParams", "key", "value", "normalizedKey", "Array", "isArray", "map", "v", "paramsResult", "normalizeDynamicRouteParams", "hasValidParams", "matcherParams", "dynamicRouteMatcher", "assign", "curParamsResult", "routeMatchesHeader", "routeMatches", "getParamsFromRouteMatches", "defaultRouteMatches", "interpolateDynamicPath", "normalizeVercelUrl", "defaultRouteRegex", "groups", "renderError", "webServerConfig", "protocol", "parsedFullUrl", "incrementalCache", "getIncrementalCache", "requestHeaders", "requestProtocol", "resetRequestCache", "__incrementalCache", "cacheHandlers", "Promise", "all", "cache<PERSON><PERSON><PERSON>", "previouslyRevalidatedTags", "previewModeId", "receiveExpiredTags", "invoke<PERSON><PERSON>", "useInvokePath", "invoke<PERSON>tatus", "invoke<PERSON><PERSON>y", "parsedMatchedPath", "invokePathnameInfo", "parseData", "normalizeResult", "response", "Response", "bubble", "run", "code", "getRequestHandlerWithMetadata", "meta", "handler", "getRequestHandler", "prefix", "loadInstrumentationModule", "prepareImpl", "then", "close", "for<PERSON>ach", "entry", "normalizedPath", "runImpl", "pipe", "fn", "partialContext", "pipeImpl", "ua", "isBotRequest", "botType", "serveStreamingMetadata", "payload", "originalStatus", "type", "cacheControl", "sent", "<PERSON><PERSON><PERSON><PERSON>", "expire", "sendRenderResult", "getStaticHTML", "toUnchunkedString", "render", "internalRender", "renderImpl", "getWaitUntil", "builtinRequestContext", "waitUntil", "getInternalWaitUntil", "startsWith", "customServer", "hasPage", "renderToResponse", "getStaticPaths", "fallback<PERSON><PERSON>", "dynamicRoutes", "staticPaths", "fallbackMode", "renderToResponseWithComponents", "requestContext", "findComponentsResult", "renderToResponseWithComponentsImpl", "pathCouldBeIntercepted", "resolvedPathname", "some", "regexp", "test", "set<PERSON>aryH<PERSON>er", "isAppPath", "baseVaryHeader", "addedNextUrlToVary", "append<PERSON><PERSON>er", "opts", "components", "prerenderManifest", "cacheEntry", "isErrorPathname", "is404Page", "is500Page", "hasServerProps", "getServerSideProps", "hasGetStaticPaths", "isPossibleServerAction", "hasGetInitialProps", "Component", "getInitialProps", "isSSG", "getStaticProps", "resolvedUrlPathname", "<PERSON><PERSON><PERSON><PERSON>", "isDynamic", "pathsResult", "resolvedWithoutSlash", "includes", "routes", "isNextDataRequest", "isPrefetchRSCRequest", "routeModule", "couldSupportPPR", "hasDebugStaticShellQuery", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "__nextppronly", "hasDebugFallbackShellQuery", "isRoutePPREnabled", "renderingMode", "isDebugStaticShell", "isDebugDynamicAccesses", "isDebugFallbackShell", "minimalPostponed", "isDynamicRSCRequest", "segmentPrefetchHeader", "isHtmlBot", "parseInt", "slice", "fromStatic", "isSupportedDocument", "Document", "previewData", "isPreviewMode", "tryGetPreviewData", "multiZoneDraftMode", "runtime", "isOnDemandRevalidate", "revalidateOnlyGenerated", "handleRedirect", "pageData", "destination", "pageProps", "__N_REDIRECT", "__N_REDIRECT_STATUS", "__N_REDIRECT_BASE_PATH", "ssgCacheKey", "doR<PERSON>", "pagesFallback", "fallbackRouteParams", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hadTrailingSlash", "resolvedUrl", "shouldWaitOnAllReady", "isRevalidate", "serverActions", "resolvedAsPath", "isDraftMode", "onClose", "onAfterTaskError", "setIsrStatus", "nextExport", "isStaticGeneration", "context", "sharedContext", "request", "fromNodeNextRequest", "handle", "fetchMetrics", "cacheTags", "collectedTags", "blob", "revalidate", "collectedRevalidate", "collectedExpire", "APP_ROUTE", "status", "from", "arrayBuffer", "<PERSON><PERSON><PERSON><PERSON>", "pendingWaitUntil", "routerKind", "routePath", "routeType", "revalidateReason", "clientReferenceManifest", "renderContext", "developmentNotFoundSourcePage", "module", "warmup", "metadata", "devRenderResumeDataCache", "renderHTML", "fetchTags", "staticBailoutInfo", "description", "stack", "message", "indexOf", "isNotFound", "isRedirect", "REDIRECT", "props", "flightData", "isNull", "APP_PAGE", "html", "rscData", "segmentData", "PAGES", "responseGenerator", "hasResolved", "previousCacheEntry", "isRevalidating", "isProduction", "didRespond", "NOT_FOUND", "PRERENDER", "BLOCKING_STATIC_RENDER", "isStale", "static<PERSON><PERSON><PERSON><PERSON>", "isPageIncludedInStaticPaths", "fallbackResponse", "previousFallbackCacheEntry", "routeKind", "isPrefetch", "purpose", "DISABLE_ROUTE_SHELL_GENERATION", "didPostpone", "isMiss", "cachedData", "IMAGE", "<PERSON><PERSON><PERSON><PERSON>", "notFoundRevalidate", "tags", "matchedSegment", "onCacheEntry", "JSON", "stringify", "delete", "set", "chain", "ReadableStream", "start", "controller", "enqueue", "CLOSED", "BODY_AND_HTML", "transformer", "TransformStream", "readable", "pipeTo", "writable", "catch", "abort", "e", "stripLocale", "splitPath", "getOriginalAppPaths", "originalAppPath", "renderPageComponent", "bubbleNoFallback", "appPaths", "findPageComponents", "sriEnabled", "sri", "algorithm", "shouldEnsure", "setRootSpanAttribute", "isNoFallbackError", "renderToResponseImpl", "fromRequest", "matchAll", "invokeOutput", "initUrl", "rewroteUrl", "renderErrorToResponse", "isWrappedError", "renderToHTML", "renderToHTMLImpl", "setHeaders", "renderErrorImpl", "renderErrorToResponseImpl", "is404", "using404Page", "statusPage", "NODE_ENV", "maybeFallbackError", "renderToHtmlError", "fallbackComponents", "getFallbackErrorComponents", "renderErrorToHTML"], "mappings": "AAIA,SAEEA,sBAAsB,QACjB,4BAA2B;AAalC,SAOEC,eAAe,QAEV,mBAAkB;AAEzB,SACEC,cAAc,EACdC,WAAW,EACXC,wBAAwB,EACxBC,iBAAiB,QACZ,sBAAqB;AA2B5B,SAASC,UAAUC,SAAS,EAAEC,SAASC,QAAQ,QAAQ,MAAK;AAC5D,SAASC,cAAc,QAAQ,wBAAuB;AACtD,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SACEC,kBAAkB,EAClBC,qBAAqB,EACrBC,cAAc,EACdC,mBAAmB,EACnBC,0BAA0B,EAC1BC,gCAAgC,QAC3B,0BAAyB;AAChC,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,yBAAyB,QAAQ,cAAa;AACvD,SAASC,SAAS,QAAQ,wCAAuC;AACjE,SAASC,qBAAqB,QAA2B,sBAAqB;AAC9E,SAASC,QAAQ,QAAQ,sBAAqB;AAC9C,SAASC,aAAa,QAAQ,UAAS;AACvC,SAASC,UAAU,EAAEC,KAAK,QAAQ,oCAAmC;AACrE,OAAOC,kBAAkB,kBAAiB;AAC1C,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,4BAA4B,EAAEC,QAAQ,QAAQ,iBAAgB;AACvE,OAAOC,WAAWC,cAAc,QAAQ,kBAAiB;AACzD,SACEC,cAAc,EACdC,cAAc,EACdC,iBAAiB,EACjBC,cAAc,QACT,iBAAgB;AACvB,SAASC,gBAAgB,QAAQ,gDAA+C;AAChF,SAASC,gBAAgB,QAAQ,uCAAsC;AACvE,SAASC,WAAW,QAAQ,6BAA4B;AACxD,SAAShC,YAAYiC,YAAY,QAAQ,uCAAsC;AAC/E,SAASC,mBAAmB,QAAQ,oDAAmD;AACvF,SACEC,UAAU,EACVC,oBAAoB,EACpBC,2BAA2B,EAC3BC,mCAAmC,EACnCC,wBAAwB,EACxBC,QAAQ,EACRC,6BAA6B,EAC7BC,wBAAwB,QACnB,0CAAyC;AAKhD,SAASC,qBAAqB,QAAQ,wCAAuC;AAC7E,SAASC,0BAA0B,QAAQ,yDAAwD;AACnG,SAASC,2BAA2B,QAAQ,4DAA2D;AACvG,SAASC,4BAA4B,QAAQ,6DAA4D;AACzG,SAASC,4BAA4B,QAAQ,6DAA4D;AACzG,SAASC,yBAAyB,QAAQ,yDAAwD;AAClG,SAASC,oBAAoB,QAAQ,4EAA2E;AAChH,SAASC,SAAS,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,qBAAoB;AACxE,SAASC,cAAc,QAAQ,wBAAuB;AACtD,SAASC,YAAY,QAAQ,sBAAqB;AAClD,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SACEC,2BAA2B,EAC3BC,uBAAuB,EACvBC,yBAAyB,QACpB,cAAa;AACpB,SACEC,cAAc,EACdC,cAAc,EACdC,mBAAmB,EACnBC,sBAAsB,EACtBC,kBAAkB,QACb,mBAAkB;AACzB,SAASC,mBAAmB,QAAQ,2CAA0C;AAC9E,SACEC,kBAAkB,EAClBC,sBAAsB,QACjB,6CAA4C;AACnD,SAASC,qBAAqB,QAAQ,iCAAgC;AACtE,OAAOC,2BAA2B,uDAAsD;AACxF,SAASC,gBAAgB,QAAQ,wCAAuC;AACxE,SAASC,qBAAqB,QAAQ,4BAA2B;AACjE,SAASC,kBAAkB,QAAQ,oCAAmC;AACtE,SACEC,oBAAoB,EACpBC,qBAAqB,EACrBC,kBAAkB,QACb,yBAAwB;AAC/B,SAASC,6BAA6B,QAAQ,qCAAoC;AAClF,SAASC,0BAA0B,QAAQ,kCAAiC;AAC5E,SAASC,yBAAyB,QAAQ,mCAAkC;AAC5E,SAASC,0BAA0B,QAAQ,iDAAgD;AAC3F,SAASC,OAAO,QAAQ,iBAAgB;AAExC,SAASC,iBAAiB,EAAEC,kBAAkB,QAAQ,sBAAqB;AAC3E,SAASC,+BAA+B,QAAQ,yBAAwB;AACxE,SAASC,oBAAoB,QAAQ,yBAAwB;AAC7D,SACEC,wBAAwB,QAEnB,kCAAiC;AACxC,SAASC,YAAY,QAAQ,6BAA4B;AACzD,SAASC,eAAe,QAAQ,gBAAe;AAC/C,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,SAAS,QAAQ,eAAc;AAExC,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,kBAAiB;AAClE,SAASC,oBAAoB,QAAQ,yBAAwB;AAC7D,SAASC,kBAAkB,QAAQ,mBAAkB;AACrD,SAASC,kCAAkC,QAAQ,2CAA0C;AAC7F,SACEC,4BAA4B,EAC5BC,gBAAgB,QACX,2BAA0B;AACjC,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,wBAAwB,QAAQ,oCAAmC;AAC5E,SAASC,gBAAgB,QAAQ,uBAAsB;AAmIvD,OAAO,MAAMC,wBAAwBC;AAAO;AAE5C,sDAAsD;AACtD,uDAAuD;AACvD,OAAO,MAAMC,0BAA0BD;IAGrCE,YAAYC,UAAiB,CAAE;QAC7B,KAAK;QACL,IAAI,CAACA,UAAU,GAAGA;IACpB;AACF;AAaA,eAAe,MAAeC;IAkGlBC,8BAEI;QACZ,OAAO,IAAI,CAACC,UAAU,CAACC,YAAY,CAACC,wBAAwB,GACxD,AAACC,WAAmBC,0BAA0B,GAC9CC;IACN;IAsBA;;;;GAIC,GAED,YAAmBC,OAAsB,CAAE;YAqCrB,uBAuEE,mCAQL;aAiEXC,mBAAgE,CACtEC,KACAC,MACAC;gBAII,sCAkBO,+BAWA;YA/BX,IAAI,CAACA,UAAUC,QAAQ,EAAE,OAAO;YAEhC,KAAI,uCAAA,IAAI,CAACC,WAAW,CAACC,kBAAkB,qBAAnC,qCAAqCC,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAClE,MAAMI,SAAS,IAAI,CAACH,WAAW,CAACC,kBAAkB,CAACG,OAAO,CACxDN,UAAUC,QAAQ;gBAEpB,IAAI,CAACI,QAAQ,OAAO;gBAEpB,MAAM,EAAEE,gBAAgB,EAAEC,WAAW,EAAE,GAAGH;gBAC1CL,UAAUC,QAAQ,GAAGM;gBAErB,iDAAiD;gBACjDT,IAAIW,OAAO,CAAC1F,WAAW2F,WAAW,GAAG,GAAG;gBACxCZ,IAAIW,OAAO,CAACxF,4BAA4ByF,WAAW,GAAG,GAAG;gBACzDZ,IAAIW,OAAO,CAACvF,oCAAoCwF,WAAW,GAAG,GAC5DF;gBAEFlG,eAAewF,KAAK,gBAAgB;gBACpCxF,eAAewF,KAAK,wBAAwB;gBAC5CxF,eAAewF,KAAK,6BAA6BU;YACnD,OAAO,KAAI,gCAAA,IAAI,CAACN,WAAW,CAACS,WAAW,qBAA5B,8BAA8BP,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAClED,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACS,WAAW,CAACC,SAAS,CACzDZ,UAAUC,QAAQ,EAClB;gBAGF,iDAAiD;gBACjDH,IAAIW,OAAO,CAAC1F,WAAW2F,WAAW,GAAG,GAAG;gBACxCZ,IAAIW,OAAO,CAACxF,4BAA4ByF,WAAW,GAAG,GAAG;gBACzDpG,eAAewF,KAAK,gBAAgB;gBACpCxF,eAAewF,KAAK,wBAAwB;YAC9C,OAAO,KAAI,wBAAA,IAAI,CAACI,WAAW,CAACW,GAAG,qBAApB,sBAAsBT,KAAK,CAACJ,UAAUC,QAAQ,GAAG;gBAC1DD,UAAUC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACW,GAAG,CAACD,SAAS,CACjDZ,UAAUC,QAAQ,EAClB;gBAGF,qCAAqC;gBACrCH,IAAIW,OAAO,CAAC1F,WAAW2F,WAAW,GAAG,GAAG;gBACxCpG,eAAewF,KAAK,gBAAgB;YACtC,OAAO,IAAIA,IAAIW,OAAO,CAAC,sBAAsB,EAAE;gBAC7C,qEAAqE;gBACrE,sEAAsE;gBACtE,gEAAgE;gBAChE,uEAAuE;gBACvE,uCAAuC;gBACvCtD,mBAAmB2C,IAAIW,OAAO;gBAE9B,OAAO;YACT,OAAO,IAAIX,IAAIW,OAAO,CAAC1F,WAAW2F,WAAW,GAAG,KAAK,KAAK;gBACxDpG,eAAewF,KAAK,gBAAgB;gBAEpC,IAAIA,IAAIW,OAAO,CAACxF,4BAA4ByF,WAAW,GAAG,KAAK,KAAK;oBAClEpG,eAAewF,KAAK,wBAAwB;oBAE5C,MAAMgB,4BACJhB,IAAIW,OAAO,CAACvF,oCAAoCwF,WAAW,GAAG;oBAChE,IAAI,OAAOI,8BAA8B,UAAU;wBACjDxG,eACEwF,KACA,6BACAgB;oBAEJ;gBACF;YACF,OAAO;gBACL,gDAAgD;gBAChD,OAAO;YACT;YAEA,IAAIhB,IAAIiB,GAAG,EAAE;gBACX,MAAMC,SAASpI,SAASkH,IAAIiB,GAAG;gBAC/BC,OAAOf,QAAQ,GAAGD,UAAUC,QAAQ;gBACpCH,IAAIiB,GAAG,GAAGrI,UAAUsI;YACtB;YAEA,OAAO;QACT;aAEQC,wBACN,OAAOnB,KAAKoB,KAAKlB;YACf,MAAMmB,aAAa,MAAM,IAAI,CAACC,aAAa;YAC3C,MAAMC,SAAStE,sBAAsBiD,UAAUC,QAAQ;YAEvD,gCAAgC;YAChC,IAAI,CAACoB,UAAU,CAACA,OAAOC,IAAI,EAAE;gBAC3B,OAAO;YACT;YAEA,IAAID,OAAOC,IAAI,CAAC,EAAE,KAAK,IAAI,CAACC,OAAO,EAAE;gBACnC,6DAA6D;gBAC7D,IACEC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BnH,eAAeuF,KAAK,qBACpB;oBACA,OAAO;gBACT;gBAEA,gDAAgD;gBAChD,MAAM,IAAI,CAAC6B,SAAS,CAAC7B,KAAKoB,KAAKlB;gBAC/B,OAAO;YACT;YAEA,0BAA0B;YAC1BqB,OAAOC,IAAI,CAACM,KAAK;YAEjB,MAAMC,YAAYR,OAAOC,IAAI,CAACD,OAAOC,IAAI,CAACQ,MAAM,GAAG,EAAE;YAErD,wCAAwC;YACxC,IAAI,OAAOD,cAAc,YAAY,CAACA,UAAUE,QAAQ,CAAC,UAAU;gBACjE,MAAM,IAAI,CAACJ,SAAS,CAAC7B,KAAKoB,KAAKlB;gBAC/B,OAAO;YACT;YAEA,4BAA4B;YAC5B,IAAIC,WAAW,CAAC,CAAC,EAAEoB,OAAOC,IAAI,CAACU,IAAI,CAAC,MAAM;YAC1C/B,WAAWjD,sBAAsBiD,UAAU;YAE3C,iDAAiD;YACjD,IAAIkB,YAAY;gBACd,IAAI,IAAI,CAAC7B,UAAU,CAAC2C,aAAa,IAAI,CAAChC,SAAS8B,QAAQ,CAAC,MAAM;oBAC5D9B,YAAY;gBACd;gBACA,IACE,CAAC,IAAI,CAACX,UAAU,CAAC2C,aAAa,IAC9BhC,SAAS6B,MAAM,GAAG,KAClB7B,SAAS8B,QAAQ,CAAC,MAClB;oBACA9B,WAAWA,SAASiC,SAAS,CAAC,GAAGjC,SAAS6B,MAAM,GAAG;gBACrD;YACF;YAEA,IAAI,IAAI,CAACK,YAAY,EAAE;oBAEJrC;gBADjB,gDAAgD;gBAChD,MAAMsC,WAAWtC,wBAAAA,oBAAAA,IAAKW,OAAO,CAAC4B,IAAI,qBAAjBvC,kBAAmBwC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC5B,WAAW;gBAEhE,MAAM6B,eAAe,IAAI,CAACJ,YAAY,CAACK,kBAAkB,CAACJ;gBAC1D,MAAMK,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,KAAI,IAAI,CAACN,YAAY,CAACO,MAAM,CAACD,aAAa;gBAEvE,MAAME,mBAAmB,IAAI,CAACR,YAAY,CAACS,OAAO,CAAC3C;gBAEnD,gEAAgE;gBAChE,qBAAqB;gBACrB,IAAI0C,iBAAiBE,cAAc,EAAE;oBACnC5C,WAAW0C,iBAAiB1C,QAAQ;gBACtC;gBAEA,gEAAgE;gBAChE3F,eAAewF,KAAK,UAAU6C,iBAAiBE,cAAc;gBAC7DvI,eAAewF,KAAK,iBAAiB2C;gBAErC,oEAAoE;gBACpE,oCAAoC;gBACpC,IAAI,CAACE,iBAAiBE,cAAc,EAAE;oBACpCrI,kBAAkBsF,KAAK;gBACzB;gBAEA,kEAAkE;gBAClE,wBAAwB;gBACxB,IAAI,CAAC6C,iBAAiBE,cAAc,IAAI,CAAC1B,YAAY;oBACnD7G,eAAewF,KAAK,UAAU2C;oBAC9B,MAAM,IAAI,CAACd,SAAS,CAAC7B,KAAKoB,KAAKlB;oBAC/B,OAAO;gBACT;YACF;YAEAA,UAAUC,QAAQ,GAAGA;YACrB3F,eAAewF,KAAK,iBAAiB;YAErC,OAAO;QACT;aAEQgD,yBAGN,IAAM;aAEAC,8BAGN,IAAM;aAEAC,kCAGN,IAAM;QA+vBV;;;;;;GAMC,QACOpC,YAAY,CAACX;YACnB,MAAMC,cAAyC,EAAE;YAEjD,IAAI,IAAI,CAACA,WAAW,CAAC+C,IAAI,EAAE;gBACzB/C,YAAYgD,IAAI,CAAC,IAAI,CAAChD,WAAW,CAAC+C,IAAI;YACxC;YAEA,2EAA2E;YAC3E,qEAAqE;YACrE,IAAI,IAAI,CAAC/C,WAAW,CAACC,kBAAkB,EAAE;gBACvCD,YAAYgD,IAAI,CAAC,IAAI,CAAChD,WAAW,CAACC,kBAAkB;YACtD;YAEA,mEAAmE;YACnE,qEAAqE;YACrE,IAAI,IAAI,CAACD,WAAW,CAACS,WAAW,EAAE;gBAChCT,YAAYgD,IAAI,CAAC,IAAI,CAAChD,WAAW,CAACS,WAAW;YAC/C;YAEA,IAAI,IAAI,CAACT,WAAW,CAACW,GAAG,EAAE;gBACxBX,YAAYgD,IAAI,CAAC,IAAI,CAAChD,WAAW,CAACW,GAAG;YACvC;YAEA,KAAK,MAAMsC,cAAcjD,YAAa;gBACpC,IAAI,CAACiD,WAAW/C,KAAK,CAACH,WAAW;gBAEjC,OAAOkD,WAAWvC,SAAS,CAACX,UAAU;YACxC;YAEA,OAAOA;QACT;aAEQmD,6BAGJ,OAAOtD,KAAKoB,KAAKH;YACnB,IAAIsC,WAAW,MAAM,IAAI,CAACP,sBAAsB,CAAChD,KAAKoB,KAAKH;YAC3D,IAAIsC,UAAU,OAAO;YAErB,IAAI,IAAI,CAACC,kBAAkB,CAACC,KAAK,EAAE;gBACjCF,WAAW,MAAM,IAAI,CAACpC,qBAAqB,CAACnB,KAAKoB,KAAKH;gBACtD,IAAIsC,UAAU,OAAO;YACvB;YAEA,OAAO;QACT;aAgCUG,WAAoB;aACpBC,kBAAwC;aAkvE1CC,uBAAuBhK,SAAS;YACtCO,IAAI0J,IAAI,CACN,CAAC,iNAAiN,CAAC;QAEvN;QA77GE,MAAM,EACJC,MAAM,GAAG,EACTC,QAAQ,KAAK,EACbC,IAAI,EACJC,MAAM,KAAK,EACXC,cAAc,KAAK,EACnB5B,QAAQ,EACR6B,IAAI,EACJC,qBAAqB,EACtB,GAAGtE;QAEJ,IAAI,CAACsE,qBAAqB,GAAGA;QAC7B,IAAI,CAACC,aAAa,GAAGvE;QAErB,IAAI,CAACgE,GAAG,GACNpC,QAAQC,GAAG,CAACC,YAAY,KAAK,SAASkC,MAAMQ,QAAQ,QAAQC,OAAO,CAACT;QAEtE,IAAI,CAACC,KAAK,GAAGA;QACb,IAAI,CAACS,aAAa,CAAC;YAAEP;QAAI;QAEzB,qDAAqD;QACrD,0DAA0D;QAC1D,IAAI,CAACzE,UAAU,GAAGwE;QAClB,IAAI,CAAC1B,QAAQ,GAAGA;QAChB,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjB,mDAAmD;YACnD,IAAI,CAACmC,aAAa,GAAG1L,eAAe,IAAI,CAACuJ,QAAQ;QACnD;QACA,IAAI,CAAC6B,IAAI,GAAGA;QACZ,IAAI,CAACO,OAAO,GACVhD,QAAQC,GAAG,CAACC,YAAY,KAAK,SACzB,IAAI,CAACpC,UAAU,CAACkF,OAAO,GACvBJ,QAAQ,QAAQpC,IAAI,CAAC,IAAI,CAAC4B,GAAG,EAAE,IAAI,CAACtE,UAAU,CAACkF,OAAO;QAC5D,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,YAAY;QAClC,IAAI,CAACC,YAAY,GAAG,CAACX,eAAe,IAAI,CAACY,eAAe;QAExD,IAAI,CAACzC,YAAY,GAAG,EAAA,wBAAA,IAAI,CAAC7C,UAAU,CAACuF,IAAI,qBAApB,sBAAsBC,OAAO,IAC7C,IAAI5I,aAAa,IAAI,CAACoD,UAAU,CAACuF,IAAI,IACrClF;QAEJ,yEAAyE;QACzE,IAAI,CAACoF,gBAAgB,GAAG,IAAI,CAAC5C,YAAY,GACrC,IAAI5G,sBAAsB,IAAI,CAAC4G,YAAY,IAC3CxC;QAEJ,6CAA6C;QAC7C,2DAA2D;QAC3D,MAAM,EACJqF,sBAAsB,CAAC,CAAC,EACxBC,mBAAmB,EACnBC,WAAW,EACXC,aAAa,EACd,GAAG,IAAI,CAAC7F,UAAU;QAEnB,IAAI,CAACiC,OAAO,GAAG,IAAI,CAAC6D,UAAU;QAC9B,4EAA4E;QAC5E,qEAAqE;QACrE,MAAMC,iBAAiB;QACvB,IAAI,CAACA,eAAe,GAClBrB,eAAe,CAAC,CAACxC,QAAQC,GAAG,CAAC6D,yBAAyB;QAExD,IAAI,CAAChC,kBAAkB,GAAG,IAAI,CAACiC,qBAAqB,CAACxB;QAErD,IAAI,CAACyB,eAAe,GAClB,IAAI,CAAClC,kBAAkB,CAACmC,GAAG,IAC3B1H,qBAAqB,IAAI,CAACuB,UAAU,CAACC,YAAY,CAACmG,GAAG;QAEvD,IAAI,CAACC,2BAA2B,GAC9B,IAAI,CAACrC,kBAAkB,CAACmC,GAAG,IAC3B,IAAI,CAACnG,UAAU,CAACC,YAAY,CAACqG,kBAAkB,KAAK;QAEtD,IAAI,CAAC1F,WAAW,GAAG;YACjB,uEAAuE;YACvE,wEAAwE;YACxE,uCAAuC;YACvCW,KACE,IAAI,CAACyC,kBAAkB,CAACmC,GAAG,IAAI,IAAI,CAACzB,WAAW,GAC3C,IAAI9G,0BACJyC;YACNgB,aACE,IAAI,CAAC6E,eAAe,IAAI,IAAI,CAACxB,WAAW,GACpC,IAAIzG,kCACJoC;YACNQ,oBACE,IAAI,CAACwF,2BAA2B,IAAI,IAAI,CAAC3B,WAAW,GAChD,IAAIvF,uCACJkB;YACNsD,MAAM,IAAI,CAACK,kBAAkB,CAACC,KAAK,GAC/B,IAAI/F,2BAA2B,IAAI,CAAC+D,OAAO,IAC3C5B;QACN;QAEA,IAAI,CAACkG,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAEhD,IAAItE,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YACvCF,QAAQC,GAAG,CAACsE,kBAAkB,GAAG,IAAI,CAACzG,UAAU,CAAC0G,YAAY,IAAI;QACnE;QAEA,IAAI,CAACC,UAAU,GAAG;YAChBC,yBAAyB;YACzBjE,eAAe,IAAI,CAAC3C,UAAU,CAAC2C,aAAa;YAC5C+D,cAAc,IAAI,CAAC1G,UAAU,CAAC0G,YAAY;YAC1CG,gBAAgB,IAAI,CAAC7G,UAAU,CAACC,YAAY,CAAC4G,cAAc,IAAI;YAC/DC,iBAAiB,IAAI,CAAC9G,UAAU,CAAC8G,eAAe;YAChDC,eAAe,IAAI,CAAC/G,UAAU,CAACgH,GAAG,CAACD,aAAa,IAAI;YACpDlB;YACAoB,cAAc,IAAI,CAACC,oBAAoB,GAAGC,OAAO;YACjDC,kBAAkB,GAAE,oCAAA,IAAI,CAACpH,UAAU,CAACC,YAAY,CAAC+G,GAAG,qBAAhC,kCAAkCK,SAAS;YAC/DC,UAAU,IAAI,CAACtH,UAAU,CAACsH,QAAQ;YAClCC,QAAQ,IAAI,CAACvH,UAAU,CAACuH,MAAM;YAC9BC,aAAa,IAAI,CAACxH,UAAU,CAACC,YAAY,CAACuH,WAAW;YACrDC,kBAAkB,IAAI,CAACzH,UAAU,CAAC0H,MAAM;YACxCC,mBAAmB,IAAI,CAAC3H,UAAU,CAACC,YAAY,CAAC0H,iBAAiB;YACjEC,yBACE,IAAI,CAAC5H,UAAU,CAACC,YAAY,CAAC2H,uBAAuB;YACtDC,aAAa,GAAE,yBAAA,IAAI,CAAC7H,UAAU,CAACuF,IAAI,qBAApB,uBAAsBuC,OAAO;YAC5C5C,SAAS,IAAI,CAACA,OAAO;YACrB6C,kBAAkB,IAAI,CAAC/D,kBAAkB,CAACmC,GAAG;YAC7C6B,mBAAmB,IAAI,CAAChI,UAAU,CAACC,YAAY,CAACgI,SAAS;YACzDC,gBAAgB,IAAI,CAAClI,UAAU,CAACC,YAAY,CAACkI,KAAK;YAClDC,aAAa,IAAI,CAACpI,UAAU,CAACoI,WAAW,GACpC,IAAI,CAACpI,UAAU,CAACoI,WAAW,GAC3B/H;YACJgI,oBAAoB,IAAI,CAACrI,UAAU,CAACC,YAAY,CAACoI,kBAAkB;YACnE,mEAAmE;YACnE,gEAAgE;YAChEC,eACEC,OAAOC,IAAI,CAAC7C,qBAAqBnD,MAAM,GAAG,IACtCmD,sBACAtF;YAEN,uDAAuD;YACvDoI,uBAAuB,IAAI,CAACzI,UAAU,CAACC,YAAY,CAACwI,qBAAqB;YACzE,8EAA8E;YAC9EC,iBAAiB,IAAI,CAAC1I,UAAU,CAAC0I,eAAe;YAChDzI,cAAc;gBACZ0I,YAAY,IAAI,CAAC3I,UAAU,CAAC2I,UAAU;gBACtCC,YAAY,IAAI,CAAC5I,UAAU,CAACC,YAAY,CAAC2I,UAAU;gBACnDC,qBAAqB,IAAI,CAAC7I,UAAU,CAACC,YAAY,CAAC4I,mBAAmB;gBACrEC,WAAW,IAAI,CAAC9I,UAAU,CAACC,YAAY,CAAC6I,SAAS,IAAI;gBACrDxC,oBACE,IAAI,CAACtG,UAAU,CAACC,YAAY,CAACqG,kBAAkB,KAAK,gBAChD,gBACAyC,QAAQ,IAAI,CAAC/I,UAAU,CAACC,YAAY,CAACqG,kBAAkB;gBAC7D0C,gBAAgB,IAAI,CAAChJ,UAAU,CAACC,YAAY,CAAC+I,cAAc,IAAI;gBAC/DC,WAAW,IAAI,CAACjJ,UAAU,CAACC,YAAY,CAACgJ,SAAS,IAAI;gBACrDC,gBAAgB,CAAC,CAAC,IAAI,CAAClJ,UAAU,CAACC,YAAY,CAACiJ,cAAc;YAC/D;YACAC,+BACE,IAAI,CAACC,6BAA6B,CAACC,IAAI,CAAC,IAAI;YAC9CC,uBAAuB,IAAI,CAACtJ,UAAU,CAACsJ,qBAAqB;QAC9D;QAEA,4DAA4D;QAC5DpP,UAAU;YACRwL;YACAC;QACF;QAEA,IAAI,CAAC4D,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB;QAChD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,gBAAgB;QAC1C,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAACC,4BAA4B;QAElE,wBAAwB;QACxB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,gBAAgB;QAErC,0EAA0E;QAC1E,yEAAyE;QACzE,kDAAkD;QAClD,KAAK,IAAI,CAACD,QAAQ,CAACE,MAAM;QAEzB,IAAI,CAACC,cAAc,CAACtE;QACpB,IAAI,CAACuE,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC;YAAE3F;QAAI;IACnD;IAEU4F,iBAAiB;QACzB,OAAO,IAAI,CAACN,QAAQ,CAACE,MAAM;IAC7B;IAmMUD,mBAAwC;QAChD,yEAAyE;QACzE,MAAMM,iBAAiB,IAAI/N,qBAAqB,CAACgO;YAC/C,OAAQA;gBACN,KAAK3Q;oBACH,OAAO,IAAI,CAAC4P,gBAAgB,MAAM;gBACpC,KAAK9P;oBACH,OAAO,IAAI,CAACgQ,mBAAmB,MAAM;gBACvC;oBACE,OAAO;YACX;QACF;QAEA,uCAAuC;QACvC,MAAMK,WAAgC,IAAI7N;QAE1C,8BAA8B;QAC9B6N,SAASnG,IAAI,CACX,IAAItH,0BACF,IAAI,CAAC4I,OAAO,EACZoF,gBACA,IAAI,CAACzH,YAAY;QAIrB,uCAAuC;QACvCkH,SAASnG,IAAI,CACX,IAAIvH,6BACF,IAAI,CAAC6I,OAAO,EACZoF,gBACA,IAAI,CAACzH,YAAY;QAIrB,2EAA2E;QAC3E,IAAI,IAAI,CAACmB,kBAAkB,CAACmC,GAAG,EAAE;YAC/B,gCAAgC;YAChC4D,SAASnG,IAAI,CACX,IAAIzH,4BAA4B,IAAI,CAAC+I,OAAO,EAAEoF;YAEhDP,SAASnG,IAAI,CACX,IAAIxH,6BAA6B,IAAI,CAAC8I,OAAO,EAAEoF;QAEnD;QAEA,OAAOP;IACT;IAEA,MAAgBX,8BACd,GAAGoB,IAAqD,EACxD;QACA,MAAM,CAACC,KAAKjK,KAAKkK,IAAI,GAAGF;QAExB,IAAI,IAAI,CAACG,eAAe,EAAE;YACxB,IAAI;gBACF,OAAM,IAAI,CAACA,eAAe,CAACC,cAAc,oBAAnC,IAAI,CAACD,eAAe,CAACC,cAAc,MAAnC,IAAI,CAACD,eAAe,EACxBF,KACA;oBACEzI,MAAMxB,IAAIiB,GAAG,IAAI;oBACjBoJ,QAAQrK,IAAIqK,MAAM,IAAI;oBACtB,gEAAgE;oBAChE1J,SACEX,eAAe5B,kBACX2J,OAAOuC,WAAW,CAACtK,IAAIW,OAAO,CAAC4J,OAAO,MACtCvK,IAAIW,OAAO;gBACnB,GACAuJ;YAEJ,EAAE,OAAOM,YAAY;gBACnB,qFAAqF;gBACrFC,QAAQC,KAAK,CAAC,4CAA4CF;YAC5D;QACF;IACF;IAEOG,SAASV,GAAU,EAAQ;QAChC,IAAI,IAAI,CAAClG,KAAK,EAAE;QAChB5J,IAAIuQ,KAAK,CAACT;IACZ;IAEA,MAAaW,cACX5K,GAAkB,EAClBoB,GAAmB,EACnBlB,SAAkC,EACnB;QACf,MAAM,IAAI,CAAC2K,OAAO;QAClB,MAAMR,SAASrK,IAAIqK,MAAM,CAACS,WAAW;QAErC,MAAMC,SAAS/O;QACf,OAAO+O,OAAOC,qBAAqB,CAAChL,IAAIW,OAAO,EAAE;YAC/C,OAAOoK,OAAOE,KAAK,CACjB9O,eAAeyO,aAAa,EAC5B;gBACEM,UAAU,GAAGb,OAAO,CAAC,EAAErK,IAAIiB,GAAG,EAAE;gBAChCkK,MAAMjP,SAASkP,MAAM;gBACrBC,YAAY;oBACV,eAAehB;oBACf,eAAerK,IAAIiB,GAAG;gBACxB;YACF,GACA,OAAOqK,OACL,IAAI,CAACC,iBAAiB,CAACvL,KAAKoB,KAAKlB,WAAWsL,OAAO,CAAC;oBAClD,IAAI,CAACF,MAAM;oBAEX,MAAMG,eAAehR,eAAeuF,KAAK,mBAAmB;oBAC5DsL,KAAKI,aAAa,CAAC;wBACjB,oBAAoBtK,IAAIuK,UAAU;wBAClC,YAAYF;oBACd;oBAEA,MAAMG,qBAAqBb,OAAOc,qBAAqB;oBACvD,iEAAiE;oBACjE,IAAI,CAACD,oBAAoB;oBAEzB,IACEA,mBAAmBE,GAAG,CAAC,sBACvB3P,eAAeyO,aAAa,EAC5B;wBACAH,QAAQ5G,IAAI,CACV,CAAC,2BAA2B,EAAE+H,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;wBAE1E;oBACF;oBAEA,MAAMC,QAAQH,mBAAmBE,GAAG,CAAC;oBACrC,IAAIC,OAAO;wBACT,MAAMhC,OAAO0B,eACT,CAAC,IAAI,EAAEpB,OAAO,CAAC,EAAE0B,OAAO,GACxB,GAAG1B,OAAO,CAAC,EAAE0B,OAAO;wBAExBT,KAAKI,aAAa,CAAC;4BACjB,cAAcK;4BACd,cAAcA;4BACd,kBAAkBhC;wBACpB;wBACAuB,KAAKU,UAAU,CAACjC;oBAClB,OAAO;wBACLuB,KAAKU,UAAU,CACbP,eACI,CAAC,IAAI,EAAEpB,OAAO,CAAC,EAAErK,IAAIiB,GAAG,EAAE,GAC1B,GAAGoJ,OAAO,CAAC,EAAErK,IAAIiB,GAAG,EAAE;oBAE9B;gBACF;QAEN;IACF;IAEA,MAAcsK,kBACZvL,GAAkB,EAClBoB,GAAmB,EACnBlB,SAAkC,EACnB;QACf,IAAI;gBAiDK+L,yBAS4BA,0BASd,oBAKY;YAvEjC,qCAAqC;YACrC,MAAM,IAAI,CAAC1C,QAAQ,CAAC2C,aAAa;YAEjC,kDAAkD;YAClD,kDAAkD;YAClDlO,gCACEgC,KACAjC,mBAAmBqD,OAAOA,IAAI+K,gBAAgB,GAAG/K;YAGnD,MAAMgL,WAAW,AAACpM,CAAAA,IAAIiB,GAAG,IAAI,EAAC,EAAGuB,KAAK,CAAC,KAAK;YAC5C,MAAM6J,aAAaD,QAAQ,CAAC,EAAE;YAE9B,oEAAoE;YACpE,+DAA+D;YAC/D,wEAAwE;YACxE,WAAW;YACX,IAAIC,8BAAAA,WAAY/L,KAAK,CAAC,cAAc;gBAClC,MAAMgM,WAAW7T,yBAAyBuH,IAAIiB,GAAG;gBACjDG,IAAImL,QAAQ,CAACD,UAAU,KAAKE,IAAI,CAACF,UAAUG,IAAI;gBAC/C;YACF;YAEA,sCAAsC;YACtC,IAAI,CAACvM,aAAa,OAAOA,cAAc,UAAU;gBAC/C,IAAI,CAACF,IAAIiB,GAAG,EAAE;oBACZ,MAAM,qBAAgD,CAAhD,IAAI/B,MAAM,wCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA+C;gBACvD;gBAEAgB,YAAYpH,SAASkH,IAAIiB,GAAG,EAAG;YACjC;YAEA,IAAI,CAACf,UAAUC,QAAQ,EAAE;gBACvB,MAAM,qBAA+C,CAA/C,IAAIjB,MAAM,uCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;YACtD;YAEA,iFAAiF;YACjF,IAAI,OAAOgB,UAAUwM,KAAK,KAAK,UAAU;gBACvCxM,UAAUwM,KAAK,GAAG3E,OAAOuC,WAAW,CAClC,IAAIqC,gBAAgBzM,UAAUwM,KAAK;YAEvC;YAEA,sCAAsC;YACtC,MAAM,EAAET,kBAAkB,IAAI,EAAE,GAAGnO,kBAAkBkC,OAAOA,MAAM,CAAC;YACnE,MAAM4M,kBAAkBX,mCAAAA,gBAAiBtL,OAAO,CAAC,oBAAoB;YACrE,MAAMkM,UAAUD,kBACZA,oBAAoB,UACpB,CAAC,EAAEX,oCAAAA,0BAAAA,gBAAiBa,MAAM,qBAAxB,AAACb,wBAAuCc,SAAS;YAEvD/M,IAAIW,OAAO,CAAC,mBAAmB,KAAKX,IAAIW,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC2B,QAAQ;YACxEtC,IAAIW,OAAO,CAAC,mBAAmB,KAAK,IAAI,CAACwD,IAAI,GACzC,IAAI,CAACA,IAAI,CAAC6I,QAAQ,KAClBH,UACE,QACA;YACN7M,IAAIW,OAAO,CAAC,oBAAoB,KAAKkM,UAAU,UAAU;YACzD7M,IAAIW,OAAO,CAAC,kBAAkB,KAAKsL,oCAAAA,2BAAAA,gBAAiBa,MAAM,qBAAvBb,yBAAyBgB,aAAa;YAEzE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAI,CAACC,iBAAiB,CAAClN,KAAKE;YAE5B,IAAIqD,WAAW,MAAM,IAAI,CAACxD,gBAAgB,CAACC,KAAKoB,KAAKlB;YACrD,IAAIqD,UAAU;YAEd,MAAMd,gBAAe,qBAAA,IAAI,CAACJ,YAAY,qBAAjB,mBAAmBK,kBAAkB,CACxD5H,YAAYoF,WAAWF,IAAIW,OAAO;YAGpC,MAAMgC,gBACJF,CAAAA,gCAAAA,aAAcE,aAAa,OAAI,wBAAA,IAAI,CAACnD,UAAU,CAACuF,IAAI,qBAApB,sBAAsBpC,aAAa;YACpEnI,eAAewF,KAAK,iBAAiB2C;YAErC,MAAM1B,MAAMlG,aAAaiF,IAAIiB,GAAG,CAACkM,OAAO,CAAC,QAAQ;YACjD,MAAMC,eAAepS,oBAAoBiG,IAAId,QAAQ,EAAE;gBACrDX,YAAY,IAAI,CAACA,UAAU;gBAC3B6C,cAAc,IAAI,CAACA,YAAY;YACjC;YACApB,IAAId,QAAQ,GAAGiN,aAAajN,QAAQ;YAEpC,IAAIiN,aAAatG,QAAQ,EAAE;gBACzB9G,IAAIiB,GAAG,GAAGrG,iBAAiBoF,IAAIiB,GAAG,EAAG,IAAI,CAACzB,UAAU,CAACsH,QAAQ;YAC/D;YAEA,MAAMuG,uBACJ,IAAI,CAACnJ,WAAW,IAAI,OAAOlE,IAAIW,OAAO,CAAChE,oBAAoB,KAAK;YAElE,uCAAuC;YACvC,IAAI0Q,sBAAsB;gBACxB,IAAI;wBAuBE,wBA2ByB,qBAkDjB;oBAnGZ,IAAI,IAAI,CAAC7J,kBAAkB,CAACmC,GAAG,EAAE;wBAC/B,iDAAiD;wBACjD,kBAAkB;wBAClB,IAAI3F,IAAIiB,GAAG,CAACX,KAAK,CAAC,mBAAmB;4BACnCN,IAAIiB,GAAG,GAAGjB,IAAIiB,GAAG,CAACkM,OAAO,CAAC,YAAY;wBACxC;wBACAjN,UAAUC,QAAQ,GAChBD,UAAUC,QAAQ,KAAK,WAAW,MAAMD,UAAUC,QAAQ;oBAC9D;oBAEA,4DAA4D;oBAC5D,sEAAsE;oBACtE,IAAI,EAAEA,UAAUmN,WAAW,EAAE,GAAG,IAAIC,IAClCvN,IAAIW,OAAO,CAAChE,oBAAoB,EAChC;oBAGF,IAAI,EAAEwD,UAAUqN,WAAW,EAAE,GAAG,IAAID,IAAIvN,IAAIiB,GAAG,EAAE;oBAEjD,2DAA2D;oBAC3D,yDAAyD;oBACzD,6CAA6C;oBAC7C,KAAI,yBAAA,IAAI,CAACb,WAAW,CAAC+C,IAAI,qBAArB,uBAAuB7C,KAAK,CAACkN,cAAc;wBAC7ChT,eAAewF,KAAK,iBAAiB;oBACvC,OAGK,IACH,IAAI,CAAC0F,eAAe,IACpB,IAAI,CAACxB,WAAW,IAChBlE,IAAIW,OAAO,CAAC9D,mBAAmB,KAAK,OACpCmD,IAAIqK,MAAM,KAAK,QACf;wBACA,oEAAoE;wBACpE,oEAAoE;wBACpE,cAAc;wBACd,MAAMmC,OAAsB,EAAE;wBAC9B,WAAW,MAAMiB,SAASzN,IAAIwM,IAAI,CAAE;4BAClCA,KAAKpJ,IAAI,CAACqK;wBACZ;wBACA,MAAMC,YAAYC,OAAOC,MAAM,CAACpB,MAAMQ,QAAQ,CAAC;wBAE/CxS,eAAewF,KAAK,aAAa0N;oBACnC;oBAEAJ,cAAc,IAAI,CAACxM,SAAS,CAACwM;oBAC7B,MAAMO,oBAAoB,IAAI,CAACC,iBAAiB,CAACN;oBAEjD,8CAA8C;oBAC9C,MAAMO,wBAAuB,sBAAA,IAAI,CAAC1L,YAAY,qBAAjB,oBAAmBS,OAAO,CAACwK,aAAa;wBACnE3K;oBACF;oBAEA,+DAA+D;oBAC/D,gEAAgE;oBAChE,kBAAkB;oBAClB,IAAIoL,sBAAsB;wBACxBvT,eAAewF,KAAK,UAAU+N,qBAAqBhL,cAAc;wBAEjE,kEAAkE;wBAClE,+DAA+D;wBAC/D,IAAIgL,qBAAqBC,mBAAmB,EAAE;4BAC5CxT,eAAewF,KAAK,6BAA6B;wBACnD,OAAO;4BACLtF,kBAAkBsF,KAAK;wBACzB;oBACF;oBAEA,0CAA0C;oBAC1CsN,cAAcpT,oBAAoBoT;oBAElC,IAAIW,cAAcX;oBAClB,IAAIY,gBAAgB1U,eAAeyU;oBAEnC,IAAI,CAACC,eAAe;wBAClB,MAAM5N,QAAQ,MAAM,IAAI,CAACiJ,QAAQ,CAACjJ,KAAK,CAAC2N,aAAa;4BACnDlJ,MAAMgJ;wBACR;wBAEA,6DAA6D;wBAC7D,IAAIzN,OAAO;4BACT2N,cAAc3N,MAAM6N,UAAU,CAAChO,QAAQ;4BACvC,iDAAiD;4BACjD+N,gBAAgB,OAAO5N,MAAMiB,MAAM,KAAK;wBAC1C;oBACF;oBAEA,qEAAqE;oBACrE,oEAAoE;oBACpE,oDAAoD;oBACpD,IAAIwM,sBAAsB;wBACxBT,cAAcS,qBAAqB5N,QAAQ;oBAC7C;oBAEA,MAAMiO,QAAQ/T,SAAS;wBACrB6T;wBACAG,MAAMJ;wBACNlJ,MAAM,IAAI,CAACvF,UAAU,CAACuF,IAAI;wBAC1B+B,UAAU,IAAI,CAACtH,UAAU,CAACsH,QAAQ;wBAClCwH,UAAU,EAAA,0BAAA,IAAI,CAACC,iBAAiB,uBAAtB,wBAA0BD,QAAQ,KAAI;4BAC9CE,aAAa,EAAE;4BACfC,YAAY,EAAE;4BACdC,UAAU,EAAE;wBACd;wBACAC,eAAe,CAAC,CAAC,IAAI,CAACnP,UAAU,CAACC,YAAY,CAACmP,mBAAmB;oBACnE;oBAEA,8DAA8D;oBAC9D,0CAA0C;oBAC1C,IAAIjM,iBAAiB,CAACyK,aAAayB,MAAM,EAAE;wBACzC3O,UAAUC,QAAQ,GAAG,CAAC,CAAC,EAAEwC,gBAAgBzC,UAAUC,QAAQ,EAAE;oBAC/D;oBAEA,MAAM2O,wBAAwB5O,UAAUC,QAAQ;oBAChD,MAAM4O,mBAAmBhH,OAAOC,IAAI,CAClCoG,MAAMY,cAAc,CAAChP,KAAKE;oBAE5B,MAAM+O,aAAaH,0BAA0B5O,UAAUC,QAAQ;oBAE/D,IAAI8O,cAAc/O,UAAUC,QAAQ,EAAE;wBACpC3F,eAAewF,KAAK,cAAcE,UAAUC,QAAQ;oBACtD;oBAEA,mEAAmE;oBACnE,mEAAmE;oBACnE,2CAA2C;oBAC3C,MAAM+O,cAAc;wBAAE,GAAGhP,UAAUwM,KAAK;oBAAC;oBAEzC,KAAK,MAAM,CAACyC,KAAKC,MAAM,IAAIrH,OAAOwC,OAAO,CAACrK,UAAUwM,KAAK,EAAG;wBAC1D,MAAM2C,gBAAgB9S,wBAAwB4S;wBAC9C,IAAI,CAACE,eAAe;wBAEpB,gEAAgE;wBAChE,+CAA+C;wBAC/C,OAAOnP,UAAUwM,KAAK,CAACyC,IAAI;wBAE3B,IAAI,OAAOC,UAAU,aAAa;wBAElCF,WAAW,CAACG,cAAc,GAAGC,MAAMC,OAAO,CAACH,SACvCA,MAAMI,GAAG,CAAC,CAACC,IAAM1Q,yBAAyB0Q,MAC1C1Q,yBAAyBqQ;oBAC/B;oBAEA,yDAAyD;oBACzD,IAAIlB,eAAe;wBACjB,IAAI3M,SAAiC,CAAC;wBAEtC,IAAImO,eAAetB,MAAMuB,2BAA2B,CAClDT,aACA;wBAGF,yDAAyD;wBACzD,wDAAwD;wBACxD,wDAAwD;wBACxD,qDAAqD;wBACrD,IACE,CAACQ,aAAaE,cAAc,IAC5B,CAACpW,eAAeqU,oBAChB;4BACA,IAAIgC,gBAAgBzB,MAAM0B,mBAAmB,oBAAzB1B,MAAM0B,mBAAmB,MAAzB1B,OAA4BP;4BAEhD,IAAIgC,eAAe;gCACjBzB,MAAMuB,2BAA2B,CAACE,eAAe;gCACjD9H,OAAOgI,MAAM,CAACL,aAAanO,MAAM,EAAEsO;gCACnCH,aAAaE,cAAc,GAAG;4BAChC;wBACF;wBAEA,uDAAuD;wBACvD,4DAA4D;wBAC5D,oEAAoE;wBACpE,+DAA+D;wBAC/D,kEAAkE;wBAClE,kEAAkE;wBAClE,yBAAyB;wBACzB,IACE,8DAA8D;wBAC9DtC,gBAAgB,YAChB,CAACoC,aAAaE,cAAc,IAC5B,CAACpW,eAAe8T,cAChB;4BACA,IAAIuC,gBAAgBzB,MAAM0B,mBAAmB,oBAAzB1B,MAAM0B,mBAAmB,MAAzB1B,OAA4Bd;4BAEhD,IAAIuC,eAAe;gCACjB,MAAMG,kBAAkB5B,MAAMuB,2BAA2B,CACvDE,eACA;gCAGF,IAAIG,gBAAgBJ,cAAc,EAAE;oCAClC7H,OAAOgI,MAAM,CAACxO,QAAQsO;oCACtBH,eAAeM;gCACjB;4BACF;wBACF;wBAEA,IAAIN,aAAaE,cAAc,EAAE;4BAC/BrO,SAASmO,aAAanO,MAAM;wBAC9B;wBAEA,MAAM0O,qBAAqBjQ,IAAIW,OAAO,CAAC,sBAAsB;wBAC7D,IACE,OAAOsP,uBAAuB,YAC9BA,sBACAzW,eAAe8T,gBACf,CAACoC,aAAaE,cAAc,EAC5B;4BACA,MAAMM,eACJ9B,MAAM+B,yBAAyB,CAACF;4BAElC,IAAIC,cAAc;gCAChBR,eAAetB,MAAMuB,2BAA2B,CAC9CO,cACA;gCAGF,IAAIR,aAAaE,cAAc,EAAE;oCAC/BrO,SAASmO,aAAanO,MAAM;gCAC9B;4BACF;wBACF;wBAEA,mEAAmE;wBACnE,6DAA6D;wBAC7D,IAAI,CAACmO,aAAaE,cAAc,EAAE;4BAChCF,eAAetB,MAAMuB,2BAA2B,CAC9CT,aACA;4BAGF,IAAIQ,aAAaE,cAAc,EAAE;gCAC/BrO,SAASmO,aAAanO,MAAM;4BAC9B;wBACF;wBAEA,uDAAuD;wBACvD,IACE6M,MAAMgC,mBAAmB,IACzBvC,sBAAsBI,eACtB,CAACyB,aAAaE,cAAc,IAC5B,CAACxB,MAAMuB,2BAA2B,CAAC;4BAAE,GAAGpO,MAAM;wBAAC,GAAG,MAC/CqO,cAAc,EACjB;4BACArO,SAAS6M,MAAMgC,mBAAmB;4BAElC,8DAA8D;4BAC9D,kBAAkB;4BAClB5V,eAAewF,KAAK,6BAA6B;wBACnD;wBAEA,IAAIuB,QAAQ;4BACV+L,cAAcc,MAAMiC,sBAAsB,CAACpC,aAAa1M;4BACxDvB,IAAIiB,GAAG,GAAGmN,MAAMiC,sBAAsB,CAACrQ,IAAIiB,GAAG,EAAGM;4BAEjD,kEAAkE;4BAClE,4DAA4D;4BAC5D,UAAU;4BACV,IAAIP,4BAA4BvG,eAC9BuF,KACA;4BAEF,IACEgB,6BACAxH,eAAewH,2BAA2B,QAC1C;gCACAA,4BAA4BoN,MAAMiC,sBAAsB,CACtDrP,2BACAO;gCAGFvB,IAAIW,OAAO,CAACvF,oCAAoCwF,WAAW,GAAG,GAC5DI;gCACFxG,eACEwF,KACA,6BACAgB;4BAEJ;wBACF;oBACF;oBAEA,IAAIkN,iBAAiBe,YAAY;4BAGdb;wBAFjBA,MAAMkC,kBAAkB,CAACtQ,KAAK;+BACzB+O;+BACAhH,OAAOC,IAAI,CAACoG,EAAAA,2BAAAA,MAAMmC,iBAAiB,qBAAvBnC,yBAAyBoC,MAAM,KAAI,CAAC;yBACpD;oBACH;oBACAtQ,UAAUC,QAAQ,GAAGmN;oBACrBrM,IAAId,QAAQ,GAAGD,UAAUC,QAAQ;oBACjCoD,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACtD,KAAKoB,KAAKlB;oBAC3D,IAAIqD,UAAU;gBAChB,EAAE,OAAO0G,KAAK;oBACZ,IAAIA,eAAezR,eAAeyR,eAAe1R,gBAAgB;wBAC/D6I,IAAIuK,UAAU,GAAG;wBACjB,OAAO,IAAI,CAAC8E,WAAW,CAAC,MAAMzQ,KAAKoB,KAAK,WAAW,CAAC;oBACtD;oBACA,MAAM6I;gBACR;YACF;YAEAzP,eAAewF,KAAK,kBAAkBuI,QAAQ9F;YAE9C,IAAI2K,aAAayB,MAAM,EAAE;gBACvB7O,IAAIiB,GAAG,GAAGrI,UAAUqI;gBACpBzG,eAAewF,KAAK,kBAAkB;YACxC;YAEA,kEAAkE;YAClE,8CAA8C;YAC9C,IAAI,CAAC,IAAI,CAACkE,WAAW,IAAI,CAACzJ,eAAeuF,KAAK,WAAW;gBACvD,gEAAgE;gBAChE,IAAIoN,aAAayB,MAAM,EAAE;oBACvBrU,eAAewF,KAAK,UAAUoN,aAAayB,MAAM;gBACnD,OAGK,IAAIlM,eAAe;oBACtBnI,eAAewF,KAAK,UAAU2C;oBAC9BnI,eAAewF,KAAK,6BAA6B;gBACnD;YACF;YAEA,kDAAkD;YAClD,uDAAuD;YACvD,iCAAiC;YACjC,IACE,CAAC,AAAC,IAAI,CAACqE,aAAa,CAASqM,eAAe,IAC5C,CAACjW,eAAeuF,KAAK,qBACrB;gBACA,IAAI2Q,WAA+B;gBAEnC,IAAI;oBACF,MAAMC,gBAAgB,IAAIrD,IACxB9S,eAAeuF,KAAK,cAAc,KAClC;oBAEF2Q,WAAWC,cAAcD,QAAQ;gBACnC,EAAE,OAAM,CAAC;gBAET,MAAME,mBAAmB,MAAM,IAAI,CAACC,mBAAmB,CAAC;oBACtDC,gBAAgBhJ,OAAOgI,MAAM,CAAC,CAAC,GAAG/P,IAAIW,OAAO;oBAC7CqQ,iBAAiBL,SAASvO,SAAS,CAAC,GAAGuO,SAAS3O,MAAM,GAAG;gBAG3D;gBAEA6O,iBAAiBI,iBAAiB;gBAClCzW,eAAewF,KAAK,oBAAoB6Q;gBACtClR,WAAmBuR,kBAAkB,GAAGL;YAC5C;YAEA,MAAMM,gBAAgBnS;YAEtB,IAAImS,eAAe;gBACjB,MAAMC,QAAQC,GAAG,CACf;uBAAIF;iBAAc,CAAC3B,GAAG,CAAC,OAAO8B;oBAC5B,IAAI,iBAAiBA,cAAc;oBACjC,+DAA+D;oBAC/D,2DAA2D;oBAC3D,gDAAgD;oBAClD,OAAO;wBACL,MAAMC,4BAA4BnX,6BAChC4F,IAAIW,OAAO,EACX,IAAI,CAAC+F,oBAAoB,GAAGC,OAAO,CAAC6K,aAAa;wBAGnD,MAAMF,aAAaG,kBAAkB,IAChCF;oBAEP;gBACF;YAEJ;YAEA,sEAAsE;YACtE,0BAA0B;YAC1B,IAAI,CAAC9W,eAAeuF,KAAK,6BAA6B;gBACpDxF,eACEwF,KACA,4BACA,IAAI,CAACT,2BAA2B;YAEpC;YAEA,oEAAoE;YACpE,mEAAmE;YACnE,mDAAmD;YACnD,MAAMmS,aAAajX,eAAeuF,KAAK;YACvC,MAAM2R,gBACJ,CAACtE,wBACD3L,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B8P;YAEF,IAAIC,eAAe;oBAkCf;gBAjCF,MAAMC,eAAenX,eAAeuF,KAAK;gBACzC,IAAI4R,cAAc;oBAChB,MAAMC,cAAcpX,eAAeuF,KAAK;oBAExC,IAAI6R,aAAa;wBACf9J,OAAOgI,MAAM,CAAC7P,UAAUwM,KAAK,EAAEmF;oBACjC;oBAEAzQ,IAAIuK,UAAU,GAAGiG;oBACjB,IAAI3H,MAAoBxP,eAAeuF,KAAK,kBAAkB;oBAE9D,OAAO,IAAI,CAACyQ,WAAW,CAACxG,KAAKjK,KAAKoB,KAAK,WAAWlB,UAAUwM,KAAK;gBACnE;gBAEA,MAAMoF,oBAAoB,IAAIvE,IAAImE,cAAc,KAAK;gBACrD,MAAMK,qBAAqB/W,oBACzB8W,kBAAkB3R,QAAQ,EAC1B;oBACEX,YAAY,IAAI,CAACA,UAAU;oBAC3BwS,WAAW;gBACb;gBAGF,IAAID,mBAAmBlD,MAAM,EAAE;oBAC7BrU,eAAewF,KAAK,UAAU+R,mBAAmBlD,MAAM;gBACzD;gBAEA,IAAI3O,UAAUC,QAAQ,KAAK2R,kBAAkB3R,QAAQ,EAAE;oBACrDD,UAAUC,QAAQ,GAAG2R,kBAAkB3R,QAAQ;oBAC/C3F,eAAewF,KAAK,cAAc+R,mBAAmB5R,QAAQ;gBAC/D;gBACA,MAAM8R,kBAAkBnV,oBACtBlC,iBAAiBsF,UAAUC,QAAQ,EAAE,IAAI,CAACX,UAAU,CAACsH,QAAQ,IAAI,MACjE,yBAAA,IAAI,CAACtH,UAAU,CAACuF,IAAI,qBAApB,uBAAsBC,OAAO;gBAG/B,IAAIiN,gBAAgBlP,cAAc,EAAE;oBAClCvI,eAAewF,KAAK,UAAUiS,gBAAgBlP,cAAc;gBAC9D;gBACA7C,UAAUC,QAAQ,GAAG8R,gBAAgB9R,QAAQ;gBAE7C,KAAK,MAAMgP,OAAOpH,OAAOC,IAAI,CAAC9H,UAAUwM,KAAK,EAAG;oBAC9C,OAAOxM,UAAUwM,KAAK,CAACyC,IAAI;gBAC7B;gBACA,MAAM0C,cAAcpX,eAAeuF,KAAK;gBAExC,IAAI6R,aAAa;oBACf9J,OAAOgI,MAAM,CAAC7P,UAAUwM,KAAK,EAAEmF;gBACjC;gBAEAtO,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACtD,KAAKoB,KAAKlB;gBAC3D,IAAIqD,UAAU;gBAEd,MAAM,IAAI,CAACN,2BAA2B,CAACjD,KAAKoB,KAAKlB;gBACjD;YACF;YAEA,IACEwB,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BnH,eAAeuF,KAAK,qBACpB;gBACAuD,WAAW,MAAM,IAAI,CAACD,0BAA0B,CAACtD,KAAKoB,KAAKlB;gBAC3D,IAAIqD,UAAU;gBAEdA,WAAW,MAAM,IAAI,CAACL,+BAA+B,CACnDlD,KACAoB,KACAlB;gBAEF,IAAIqD,UAAU;gBAEd,MAAM0G,MAAM,IAAI/K;gBACd+K,IAAY1J,MAAM,GAAG;oBACrB2R,UAAU,IAAIC,SAAS,MAAM;wBAC3BxR,SAAS;4BACP,qBAAqB;wBACvB;oBACF;gBACF;gBACEsJ,IAAYmI,MAAM,GAAG;gBACvB,MAAMnI;YACR;YAEA,oEAAoE;YACpE,sDAAsD;YAEtD,+DAA+D;YAC/D,IAAI,CAACoD,wBAAwBD,aAAatG,QAAQ,EAAE;gBAClD5G,UAAUC,QAAQ,GAAGvF,iBACnBsF,UAAUC,QAAQ,EAClBiN,aAAatG,QAAQ;YAEzB;YAEA1F,IAAIuK,UAAU,GAAG;YACjB,OAAO,MAAM,IAAI,CAAC0G,GAAG,CAACrS,KAAKoB,KAAKlB;QAClC,EAAE,OAAO+J,KAAU;YACjB,IAAIA,eAAehL,iBAAiB;gBAClC,MAAMgL;YACR;YAEA,IACE,AAACA,OAAO,OAAOA,QAAQ,YAAYA,IAAIqI,IAAI,KAAK,qBAChDrI,eAAezR,eACfyR,eAAe1R,gBACf;gBACA6I,IAAIuK,UAAU,GAAG;gBACjB,OAAO,IAAI,CAAC8E,WAAW,CAAC,MAAMzQ,KAAKoB,KAAK,WAAW,CAAC;YACtD;YAEA,IACE,IAAI,CAAC8C,WAAW,IAChB,IAAI,CAACiC,UAAU,CAAClC,GAAG,IAClBhI,eAAegO,QAAQA,IAAImI,MAAM,EAClC;gBACA,MAAMnI;YACR;YACA,IAAI,CAACU,QAAQ,CAACpQ,eAAe0P;YAC7B7I,IAAIuK,UAAU,GAAG;YACjBvK,IAAIoL,IAAI,CAAC,yBAAyBC,IAAI;QACxC;IACF;IAwDA;;GAEC,GACD,AAAO8F,8BACLC,IAAiB,EACkC;QACnD,MAAMC,UAAU,IAAI,CAACC,iBAAiB;QACtC,OAAO,CAAC1S,KAAKoB,KAAKlB;YAChBvF,eAAeqF,KAAKwS;YACpB,OAAOC,QAAQzS,KAAKoB,KAAKlB;QAC3B;IACF;IAEOwS,oBAGL;QACA,OAAO,IAAI,CAAC9H,aAAa,CAAC/B,IAAI,CAAC,IAAI;IACrC;IAQOa,eAAeiJ,MAAe,EAAQ;QAC3C,IAAI,CAACxM,UAAU,CAACf,WAAW,GAAGuN,SAASA,OAAOxF,OAAO,CAAC,OAAO,MAAM;IACrE;IAIA;;;GAGC,GACD,MAAatC,UAAyB;QACpC,IAAI,IAAI,CAACnH,QAAQ,EAAE;QAEnB,IAAI,IAAI,CAACC,eAAe,KAAK,MAAM;YACjC,6BAA6B;YAC7B,IAAI,CAACwG,eAAe,GAAG,MAAM,IAAI,CAACyI,yBAAyB;YAC3D,IAAI,CAACjP,eAAe,GAAG,IAAI,CAACkP,WAAW,GAAGC,IAAI,CAAC;gBAC7C,IAAI,CAACpP,QAAQ,GAAG;gBAChB,IAAI,CAACC,eAAe,GAAG;YACzB;QACF;QACA,OAAO,IAAI,CAACA,eAAe;IAC7B;IACA,MAAgBkP,cAA6B,CAAC;IAC9C,MAAgBD,4BAA0C,CAAC;IAE3D,MAAaG,QAAuB,CAAC;IAE3B3J,mBAA6C;QACrD,MAAMD,gBAA0C,CAAC;QAEjDpB,OAAOC,IAAI,CAAC,IAAI,CAACiB,gBAAgB,IAAI,CAAC,GAAG+J,OAAO,CAAC,CAACC;YAChD,MAAMC,iBAAiBrY,iBAAiBoY;YACxC,IAAI,CAAC9J,aAAa,CAAC+J,eAAe,EAAE;gBAClC/J,aAAa,CAAC+J,eAAe,GAAG,EAAE;YACpC;YACA/J,aAAa,CAAC+J,eAAe,CAAC9P,IAAI,CAAC6P;QACrC;QACA,OAAO9J;IACT;IAEA,MAAgBkJ,IACdrS,GAAkB,EAClBoB,GAAmB,EACnBlB,SAA6B,EACd;QACf,OAAOlE,YAAYiP,KAAK,CAAC9O,eAAekW,GAAG,EAAE,UAC3C,IAAI,CAACc,OAAO,CAACnT,KAAKoB,KAAKlB;IAE3B;IAEA,MAAciT,QACZnT,GAAkB,EAClBoB,GAAmB,EACnBlB,SAA6B,EACd;QACf,MAAM,IAAI,CAAC+C,2BAA2B,CAACjD,KAAKoB,KAAKlB;IACnD;IAEA,MAAckT,KACZC,EAEoC,EACpCC,cAGC,EACc;QACf,OAAOtX,YAAYiP,KAAK,CAAC9O,eAAeiX,IAAI,EAAE,UAC5C,IAAI,CAACG,QAAQ,CAACF,IAAIC;IAEtB;IAEA,MAAcC,SACZF,EAEoC,EACpCC,cAGC,EACc;QACf,MAAME,KAAKF,eAAetT,GAAG,CAACW,OAAO,CAAC,aAAa,IAAI;QACvD,MAAM8S,eAAe1Z,MAAMyZ;QAE3B,MAAMtJ,MAAqD;YACzD,GAAGoJ,cAAc;YACjBnN,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClBC,yBAAyB,CAACqN;gBAC1BC,SAAS5Z,WAAW0Z;gBACpBG,wBAAwB/U,6BACtB4U,IACA,IAAI,CAAChU,UAAU,CAAC0I,eAAe;YAEnC;QACF;QAEA,MAAM0L,UAAU,MAAMP,GAAGnJ;QACzB,IAAI0J,YAAY,MAAM;YACpB;QACF;QACA,MAAM,EAAE5T,GAAG,EAAEoB,GAAG,EAAE,GAAG8I;QACrB,MAAM2J,iBAAiBzS,IAAIuK,UAAU;QACrC,MAAM,EAAEa,IAAI,EAAEsH,IAAI,EAAE,GAAGF;QACvB,IAAI,EAAEG,YAAY,EAAE,GAAGH;QACvB,IAAI,CAACxS,IAAI4S,IAAI,EAAE;YACb,MAAM,EAAE3O,aAAa,EAAEiB,eAAe,EAAErC,GAAG,EAAE,GAAG,IAAI,CAACkC,UAAU;YAE/D,oDAAoD;YACpD,IAAIlC,KAAK;gBACP7C,IAAI6S,SAAS,CAAC,iBAAiB;gBAC/BF,eAAelU;YACjB;YAEA,IAAIkU,gBAAgBA,aAAaG,MAAM,KAAKrU,WAAW;gBACrDkU,aAAaG,MAAM,GAAG,IAAI,CAAC1U,UAAU,CAAC2I,UAAU;YAClD;YAEA,MAAM,IAAI,CAACgM,gBAAgB,CAACnU,KAAKoB,KAAK;gBACpCb,QAAQiM;gBACRsH;gBACAzO;gBACAiB;gBACAyN;YACF;YACA3S,IAAIuK,UAAU,GAAGkI;QACnB;IACF;IAEA,MAAcO,cACZf,EAEoC,EACpCC,cAGC,EACuB;QACxB,MAAMpJ,MAAqD;YACzD,GAAGoJ,cAAc;YACjBnN,YAAY;gBACV,GAAG,IAAI,CAACA,UAAU;gBAClBC,yBAAyB;YAC3B;QACF;QACA,MAAMwN,UAAU,MAAMP,GAAGnJ;QACzB,IAAI0J,YAAY,MAAM;YACpB,OAAO;QACT;QACA,OAAOA,QAAQpH,IAAI,CAAC6H,iBAAiB;IACvC;IAEA,MAAaC,OACXtU,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBuM,QAA4B,CAAC,CAAC,EAC9BxM,SAAkC,EAClCqU,iBAAiB,KAAK,EACP;QACf,OAAOvY,YAAYiP,KAAK,CAAC9O,eAAemY,MAAM,EAAE,UAC9C,IAAI,CAACE,UAAU,CAACxU,KAAKoB,KAAKjB,UAAUuM,OAAOxM,WAAWqU;IAE1D;IAEUE,eAAsC;QAC9C,MAAMC,wBAAwBxW;QAC9B,IAAIwW,uBAAuB;YACzB,2CAA2C;YAC3C,qEAAqE;YACrE,sCAAsC;YAEtC,uGAAuG;YACvG,OAAOA,sBAAsBC,SAAS;QACxC;QAEA,IAAI,IAAI,CAACzQ,WAAW,EAAE;YACpB,8EAA8E;YAC9E,4DAA4D;YAC5D,0DAA0D;YAC1D,kDAAkD;YAClD,EAAE;YACF,yEAAyE;YACzE,EAAE;YACF,wGAAwG;YACxG,wBAAwB;YACxB,OAAOrE;QACT;QAEA,OAAO,IAAI,CAAC+U,oBAAoB;IAClC;IAEUA,uBAA8C;QACtD,OAAO/U;IACT;IAEA,MAAc2U,WACZxU,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBuM,QAA4B,CAAC,CAAC,EAC9BxM,SAAkC,EAClCqU,iBAAiB,KAAK,EACP;YAyBZvU;QAxBH,IAAI,CAACG,SAAS0U,UAAU,CAAC,MAAM;YAC7BpK,QAAQ5G,IAAI,CACV,CAAC,8BAA8B,EAAE1D,SAAS,kBAAkB,EAAEA,SAAS,iFAAiF,CAAC;QAE7J;QAEA,IACE,IAAI,CAACkE,aAAa,CAACyQ,YAAY,IAC/B3U,aAAa,YACb,CAAE,MAAM,IAAI,CAAC4U,OAAO,CAAC,WACrB;YACA,qDAAqD;YACrD,wCAAwC;YACxC5U,WAAW;QACb;QAEA,sDAAsD;QACtD,2DAA2D;QAC3D,2DAA2D;QAC3D,kEAAkE;QAClE,IACE,CAACoU,kBACD,CAAC,IAAI,CAACrQ,WAAW,IACjB,CAACzJ,eAAeuF,KAAK,oBACpBA,CAAAA,EAAAA,WAAAA,IAAIiB,GAAG,qBAAPjB,SAASM,KAAK,CAAC,kBACb,IAAI,CAACuE,YAAY,IAAI7E,IAAIiB,GAAG,CAAEX,KAAK,CAAC,cAAc,GACrD;YACA,OAAO,IAAI,CAACsK,aAAa,CAAC5K,KAAKoB,KAAKlB;QACtC;QAEA,IAAIrG,cAAcsG,WAAW;YAC3B,OAAO,IAAI,CAAC0B,SAAS,CAAC7B,KAAKoB,KAAKlB;QAClC;QAEA,OAAO,IAAI,CAACkT,IAAI,CAAC,CAAClJ,MAAQ,IAAI,CAAC8K,gBAAgB,CAAC9K,MAAM;YACpDlK;YACAoB;YACAjB;YACAuM;QACF;IACF;IAEA,MAAgBuI,eAAe,EAC7B9U,QAAQ,EAMT,EAGE;YAGC;QAFF,+DAA+D;QAC/D,MAAM+U,iBACJ,oDAAA,IAAI,CAACxO,oBAAoB,GAAGyO,aAAa,CAAChV,SAAS,qBAAnD,kDAAqDuO,QAAQ;QAE/D,OAAO;YACL,oEAAoE;YACpE,uCAAuC;YACvC0G,aAAavV;YACbwV,cAAc7W,mBAAmB0W;QACnC;IACF;IAEA,MAAcI,+BACZC,cAA6D,EAC7DC,oBAA0C,EACT;QACjC,OAAOxZ,YAAYiP,KAAK,CACtB9O,eAAemZ,8BAA8B,EAC7C,UACE,IAAI,CAACG,kCAAkC,CACrCF,gBACAC;IAGR;IAEUE,uBAAuBC,gBAAwB,EAAW;QAClE,OACE/X,2BAA2B+X,qBAC3B,IAAI,CAACtM,yBAAyB,CAACuM,IAAI,CAAC,CAACC;YACnC,OAAOA,OAAOC,IAAI,CAACH;QACrB;IAEJ;IAEUI,cACR/V,GAAkB,EAClBoB,GAAmB,EACnB4U,SAAkB,EAClBL,gBAAwB,EAClB;QACN,MAAMM,iBAAiB,GAAGhb,WAAW,EAAE,EAAEM,8BAA8B,EAAE,EAAEJ,4BAA4B,EAAE,EAAEC,qCAAqC;QAChJ,MAAMqQ,eAAehR,eAAeuF,KAAK,mBAAmB;QAE5D,IAAIkW,qBAAqB;QAEzB,IAAIF,aAAa,IAAI,CAACN,sBAAsB,CAACC,mBAAmB;YAC9D,wEAAwE;YACxE,+FAA+F;YAC/FvU,IAAI+U,YAAY,CAAC,QAAQ,GAAGF,eAAe,EAAE,EAAE3a,UAAU;YACzD4a,qBAAqB;QACvB,OAAO,IAAIF,aAAavK,cAAc;YACpC,yHAAyH;YACzH,mGAAmG;YACnGrK,IAAI+U,YAAY,CAAC,QAAQF;QAC3B;QAEA,IAAI,CAACC,oBAAoB;YACvB,8GAA8G;YAC9G,sGAAsG;YACtG,OAAOlW,IAAIW,OAAO,CAACrF,SAAS;QAC9B;IACF;IAEA,MAAcma,mCACZ,EACEzV,GAAG,EACHoB,GAAG,EACHjB,QAAQ,EACRgG,YAAYiQ,IAAI,EAC8B,EAChD,EAAEC,UAAU,EAAE3J,KAAK,EAAwB,EACV;YAcJ2J,uBAoHzB,uBA4CAC,OAiHY,wBAi4BdC,mBAmCAA;QAlsCF,IAAIpW,aAAa7G,4BAA4B;YAC3C6G,WAAW;QACb;QACA,MAAMqW,kBAAkBrW,aAAa;QACrC,MAAMsW,YACJtW,aAAa,UAAWqW,mBAAmBpV,IAAIuK,UAAU,KAAK;QAChE,MAAM+K,YACJvW,aAAa,UAAWqW,mBAAmBpV,IAAIuK,UAAU,KAAK;QAChE,MAAMqK,YAAYK,WAAWL,SAAS,KAAK;QAE3C,MAAMW,iBAAiB,CAAC,CAACN,WAAWO,kBAAkB;QACtD,IAAIC,oBAAoB,CAAC,CAACR,WAAWpB,cAAc;QACnD,MAAM6B,yBAAyBnZ,0BAA0BqC;QACzD,MAAM+W,qBAAqB,CAAC,GAACV,wBAAAA,WAAWW,SAAS,qBAApBX,sBAAsBY,eAAe;QAClE,IAAIC,QAAQ,CAAC,CAACb,WAAWc,cAAc;QAEvC,0DAA0D;QAC1D,4DAA4D;QAC5D,wDAAwD;QACxD,IAAI3J,cAAc1U,SAASkH,IAAIiB,GAAG,IAAI,IAAId,QAAQ,IAAI;QAEtD,IAAIiX,sBAAsB3c,eAAeuF,KAAK,iBAAiBwN;QAE/D,IAAI,CAACuI,aAAa,CAAC/V,KAAKoB,KAAK4U,WAAWoB;QAExC,IAAIhC;QACJ,IAAIC;QACJ,IAAIgC,cAAc;QAElB,MAAMC,YAAY9d,eAAe6c,WAAWhI,IAAI;QAEhD,MAAMiI,oBAAoB,IAAI,CAAC5P,oBAAoB;QAEnD,IAAIsP,aAAasB,WAAW;YAC1B,MAAMC,cAAc,MAAM,IAAI,CAACtC,cAAc,CAAC;gBAC5C9U;gBACAkO,MAAMgI,WAAWhI,IAAI;gBACrB2H;gBACAjF,gBAAgB/Q,IAAIW,OAAO;YAC7B;YAEAyU,cAAcmC,YAAYnC,WAAW;YACrCC,eAAekC,YAAYlC,YAAY;YACvCgC,cAAc,OAAOhC,iBAAiB;YAEtC,IAAI,IAAI,CAAC7V,UAAU,CAAC0H,MAAM,KAAK,UAAU;gBACvC,MAAMmH,OAAOgI,WAAWhI,IAAI;gBAC5B,IAAI,CAAC+G,aAAa;oBAChB,MAAM,qBAEL,CAFK,IAAIlW,MACR,CAAC,MAAM,EAAEmP,KAAK,wGAAwG,CAAC,GADnH,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,MAAMmJ,uBAAuBvd,oBAAoBmd;gBACjD,IAAI,CAAChC,YAAYqC,QAAQ,CAACD,uBAAuB;oBAC/C,MAAM,qBAEL,CAFK,IAAItY,MACR,CAAC,MAAM,EAAEmP,KAAK,oBAAoB,EAAEmJ,qBAAqB,8EAA8E,CAAC,GADpI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAIH,aAAa;gBACfR,oBAAoB;YACtB;QACF;QAEA,IACEQ,gBACAjC,+BAAAA,YAAaqC,QAAQ,CAACL,yBACtB,mDAAmD;QACnD,+BAA+B;QAC/BpX,IAAIW,OAAO,CAAC,sBAAsB,EAClC;YACAuW,QAAQ;QACV,OAAO,IAAI,CAAC,IAAI,CAAC/Q,UAAU,CAAClC,GAAG,EAAE;YAC/BiT,UAAU,CAAC,CAACZ,kBAAkBoB,MAAM,CAAC7Z,QAAQsC,UAAU;QACzD;QAEA,+CAA+C;QAC/C,MAAMwX,oBACJ,CAAC,CACCld,CAAAA,eAAeuF,KAAK,oBACnBA,IAAIW,OAAO,CAAC,gBAAgB,IAC3B,AAAC,IAAI,CAAC0D,aAAa,CAASqM,eAAe,KAE9CwG,CAAAA,SAASP,cAAa;QAEzB;;;KAGC,GACD,MAAMiB,uBACJnd,eAAeuF,KAAK,2BAA2B;QAEjD,uFAAuF;QAEvF,MAAMyL,eAAehR,eAAeuF,KAAK,mBAAmB;QAE5D,4DAA4D;QAC5D,wDAAwD;QACxD,6BAA6B;QAC7B,IACE,CAACkX,SACDlX,IAAIW,OAAO,CAAC,wBAAwB,IACpC,CAAE8V,CAAAA,aAAatW,aAAa,SAAQ,GACpC;YACAiB,IAAI6S,SAAS,CAACtX,qBAAqBwD;YACnCiB,IAAI6S,SAAS,CAAC,qBAAqB;YACnC7S,IAAI6S,SAAS,CACX,iBACA;YAEF7S,IAAIoL,IAAI,CAAC,MAAMC,IAAI;YACnB,OAAO;QACT;QAEA,uDAAuD;QACvD,iEAAiE;QACjE,IACEyK,SACA,IAAI,CAAChT,WAAW,IAChBlE,IAAIW,OAAO,CAAChE,oBAAoB,IAChCqD,IAAIiB,GAAG,CAAC4T,UAAU,CAAC,gBACnB;YACA7U,IAAIiB,GAAG,GAAG,IAAI,CAAC6M,iBAAiB,CAAC9N,IAAIiB,GAAG;QAC1C;QAEA,MAAM4N,SAASpU,eAAeuF,KAAK;QACnC,MAAM2C,gBAAgBuU,SAClB,wBAAA,IAAI,CAAC1X,UAAU,CAACuF,IAAI,qBAApB,sBAAsBpC,aAAa,GACnClI,eAAeuF,KAAK;QAExB,IACE,CAAC,CAACA,IAAIW,OAAO,CAAC,gBAAgB,IAC7B,CAAA,CAACS,IAAIuK,UAAU,IAAIvK,IAAIuK,UAAU,KAAK,GAAE,GACzC;YACAvK,IAAI6S,SAAS,CACX,yBACA,GAAGpF,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAK1O,UAAU;QAE9C;QAEA,IAAI0X;QACJ,IAAIxB,WAAWwB,WAAW,EAAE;YAC1BA,cAAcxB,WAAWwB,WAAW;QACtC;QAEA;;;KAGC,GACD,MAAMC,kBACJ,IAAI,CAACpS,eAAe,IACpB,OAAOmS,gBAAgB,eACvBva,qBAAqBua;QAEvB,yEAAyE;QACzE,wCAAwC;QACxC,MAAME,2BACJrW,QAAQC,GAAG,CAACqW,0CAA0C,KAAK,OAC3D,OAAOtL,MAAMuL,aAAa,KAAK,eAC/BH;QAEF,sEAAsE;QACtE,6CAA6C;QAC7C,MAAMI,6BACJH,4BAA4BrL,MAAMuL,aAAa,KAAK;QAEtD,4EAA4E;QAC5E,8CAA8C;QAC9C,MAAME,oBACJL,mBACC,CAAA,EACCxB,QAAAA,kBAAkBoB,MAAM,CAACvX,SAAS,IAClCmW,kBAAkBnB,aAAa,CAAChV,SAAS,qBAF1C,AACCmW,MAEC8B,aAAa,MAAK,sBACnB,uEAAuE;QACvE,wEAAwE;QACxE,wEAAwE;QACxE,+BAA+B;QAC9BL,4BACE,CAAA,IAAI,CAAC5R,UAAU,CAAClC,GAAG,KAAK,QACvB,IAAI,CAACG,qBAAqB,KAAK,IAAG,CAAE;QAE5C,MAAMiU,qBACJN,4BAA4BI;QAE9B,oEAAoE;QACpE,iEAAiE;QACjE,MAAMG,yBACJD,sBAAsB,IAAI,CAAClS,UAAU,CAAClC,GAAG,KAAK;QAEhD,MAAMsU,uBAAuBL,8BAA8BC;QAE3D,2EAA2E;QAC3E,wEAAwE;QACxE,UAAU;QACV,MAAMK,mBAAmBL,oBACrB1d,eAAeuF,KAAK,eACpBH;QAEJ,0EAA0E;QAC1E,wEAAwE;QACxE,0DAA0D;QAC1D,MAAM4Y,sBACJN,qBAAqB1M,gBAAgB,CAACmM;QAExC,yEAAyE;QACzE,iEAAiE;QACjE,yEAAyE;QACzE,yEAAyE;QACzE,MAAMc,wBAAwBje,eAC5BuF,KACA;QAGF,MAAM2Y,YAAY9Z,iBAAiBmB;QACnC,IAAI2Y,aAAaR,mBAAmB;YAClCjB,QAAQ;YACR,IAAI,CAAC/Q,UAAU,CAACwN,sBAAsB,GAAG;QAC3C;QAEA,gEAAgE;QAChE,IAAI8C,aAAa,CAACkB,qBAAqB,CAAClM,cAAc;YACpDrK,IAAIuK,UAAU,GAAG;QACnB;QAEA,2DAA2D;QAC3D,qBAAqB;QACrB,IAAItS,oBAAoBoe,QAAQ,CAACtX,WAAW;YAC1CiB,IAAIuK,UAAU,GAAGiN,SAASzY,SAAS0Y,KAAK,CAAC,IAAI;QAC/C;QAEA,IACE,+CAA+C;QAC/C,CAAC/B,0BACD,uCAAuC;QACvC,CAAC0B,oBACD,CAAC/B,aACD,CAACC,aACDvW,aAAa,aACbH,IAAIqK,MAAM,KAAK,UACfrK,IAAIqK,MAAM,KAAK,SACd,CAAA,OAAOgM,WAAWW,SAAS,KAAK,YAAYE,KAAI,GACjD;YACA9V,IAAIuK,UAAU,GAAG;YACjBvK,IAAI6S,SAAS,CAAC,SAAS;gBAAC;gBAAO;aAAO;YACtC7S,IAAIoL,IAAI,CAAC,sBAAsBC,IAAI;YACnC,OAAO;QACT;QAEA,qBAAqB;QACrB,IAAI,OAAO4J,WAAWW,SAAS,KAAK,UAAU;YAC5C,OAAO;gBACLlD,MAAM;gBACN,0DAA0D;gBAC1DtH,MAAMxS,aAAa8e,UAAU,CAACzC,WAAWW,SAAS;YACpD;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,IAAI,SAAStK,SAAS,CAACA,MAAMlG,GAAG,EAAE,OAAOkG,MAAMlG,GAAG;QAElD,IAAI4P,KAAKhQ,uBAAuB,KAAK,MAAM;gBAIhCiQ;YAHT,MAAM7C,KAAKxT,IAAIW,OAAO,CAAC,aAAa,IAAI;YACxC,MAAM8S,eAAe1Z,MAAMyZ;YAC3B,MAAMuF,sBACJ,SAAO1C,uBAAAA,WAAW2C,QAAQ,qBAAnB3C,qBAAqBY,eAAe,MAAK,cAChD,oFAAoF;YACpF9d,yBAAyBkd,WAAW2C,QAAQ;YAE9C,oEAAoE;YACpE,gEAAgE;YAChE,2DAA2D;YAC3D,0DAA0D;YAC1D,kDAAkD;YAClD5C,KAAKhQ,uBAAuB,GAC1B,CAAC8Q,SAAS,CAACzD,gBAAgB,CAAC/G,MAAMlG,GAAG,IAAIuS;QAC7C;QAEA,2DAA2D;QAC3D,IAAI,CAACpB,qBAAqB3B,aAAaI,KAAKnS,GAAG,EAAE;YAC/CmS,KAAKhQ,uBAAuB,GAAG;QACjC;QAEA,MAAMpB,WAAU,yBAAA,IAAI,CAACxF,UAAU,CAACuF,IAAI,qBAApB,uBAAsBC,OAAO;QAE7C,IAAIiU;QACJ,IAAIC,gBAAgB;QAEpB,IAAIvC,kBAAkBO,SAASlB,WAAW;YACxC,8DAA8D;YAC9D,IAAItU,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;gBACvC,MAAM,EAAEuX,iBAAiB,EAAE,GACzB7U,QAAQ;gBACV2U,cAAcE,kBACZnZ,KACAoB,KACA,IAAI,CAAC+E,UAAU,CAACM,YAAY,EAC5B,CAAC,CAAC,IAAI,CAACjH,UAAU,CAACC,YAAY,CAAC2Z,kBAAkB;gBAEnDF,gBAAgBD,gBAAgB;YAClC;QACF;QAEA,2EAA2E;QAC3E,yEAAyE;QACzE,gCAAgC;QAChC,IACEjD,aACA,CAACI,KAAKnS,GAAG,IACT,CAACiV,iBACDhC,SACAzL,gBACA,CAACgN,uBACA,CAAA,CAACxf,cAAcmd,KAAKiD,OAAO,KAC1B,AAAC,IAAI,CAAChV,aAAa,CAASqM,eAAe,AAAD,GAC5C;YACArT,mBAAmB2C,IAAIW,OAAO;QAChC;QAEA,IAAI,EAAE2Y,oBAAoB,EAAEC,uBAAuB,EAAE,GACnD9f,0BAA0BuG,KAAK,IAAI,CAACmG,UAAU,CAACM,YAAY;QAE7D,IAAIyQ,SAAS,IAAI,CAAChT,WAAW,IAAIlE,IAAIW,OAAO,CAAChE,oBAAoB,EAAE;YACjE,uEAAuE;YACvEya,sBAAsB5J;QACxB;QAEAA,cAAcvT,oBAAoBuT;QAClC4J,sBAAsBnd,oBAAoBmd;QAC1C,IAAI,IAAI,CAACnS,gBAAgB,EAAE;YACzBmS,sBAAsB,IAAI,CAACnS,gBAAgB,CAACnE,SAAS,CAACsW;QACxD;QAEA,MAAMoC,iBAAiB,CAACC;YACtB,MAAMlN,WAAW;gBACfmN,aAAaD,SAASE,SAAS,CAACC,YAAY;gBAC5CjO,YAAY8N,SAASE,SAAS,CAACE,mBAAmB;gBAClD/S,UAAU2S,SAASE,SAAS,CAACG,sBAAsB;YACrD;YACA,MAAMnO,aAAa3S,kBAAkBuT;YACrC,MAAM,EAAEzF,QAAQ,EAAE,GAAG,IAAI,CAACtH,UAAU;YAEpC,IACEsH,YACAyF,SAASzF,QAAQ,KAAK,SACtByF,SAASmN,WAAW,CAAC7E,UAAU,CAAC,MAChC;gBACAtI,SAASmN,WAAW,GAAG,GAAG5S,WAAWyF,SAASmN,WAAW,EAAE;YAC7D;YAEA,IAAInN,SAASmN,WAAW,CAAC7E,UAAU,CAAC,MAAM;gBACxCtI,SAASmN,WAAW,GAAGjhB,yBAAyB8T,SAASmN,WAAW;YACtE;YAEAtY,IACGmL,QAAQ,CAACA,SAASmN,WAAW,EAAE/N,YAC/Ba,IAAI,CAACD,SAASmN,WAAW,EACzBjN,IAAI;QACT;QAEA,2DAA2D;QAC3D,8CAA8C;QAC9C,IAAIkL,mBAAmB;YACrBP,sBAAsB,IAAI,CAACtJ,iBAAiB,CAACsJ;YAC7C5J,cAAc,IAAI,CAACM,iBAAiB,CAACN;QACvC;QAEA,IAAIuM,cAA6B;QACjC,IACE,CAACb,iBACDhC,SACA,CAACd,KAAKhQ,uBAAuB,IAC7B,CAAC0Q,0BACD,CAAC0B,oBACD,CAACC,qBACD;YACAsB,cAAc,GAAGlL,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KACvC,AAAC1O,CAAAA,aAAa,OAAOiX,wBAAwB,GAAE,KAAMvI,SACjD,KACAuI,sBACH1K,MAAMlG,GAAG,GAAG,SAAS,IAAI;QAC9B;QAEA,IAAI,AAACiQ,CAAAA,aAAaC,SAAQ,KAAMQ,OAAO;YACrC6C,cAAc,GAAGlL,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAK1O,WAC5CuM,MAAMlG,GAAG,GAAG,SAAS,IACrB;QACJ;QAEA,IAAIuT,aAAa;YACfA,cAAc5c,iBAAiB4c;YAE/B,+CAA+C;YAC/CA,cACEA,gBAAgB,YAAY5Z,aAAa,MAAM,MAAM4Z;QACzD;QACA,IAAIpJ,WAA+B;QAEnC,IAAI;YACF,MAAMC,gBAAgB,IAAIrD,IACxB9S,eAAeuF,KAAK,cAAc,KAClC;YAEF2Q,WAAWC,cAAcD,QAAQ;QACnC,EAAE,OAAM,CAAC;QAET,sDAAsD;QACtD,MAAME,mBACJ,AAAClR,WAAmBuR,kBAAkB,IACrC,MAAM,IAAI,CAACJ,mBAAmB,CAAC;YAC9BC,gBAAgBhJ,OAAOgI,MAAM,CAAC,CAAC,GAAG/P,IAAIW,OAAO;YAC7CqQ,iBAAiBL,SAASvO,SAAS,CAAC,GAAGuO,SAAS3O,MAAM,GAAG;QAG3D;QAEF,0EAA0E;QAC1E6O,iBAAiBI,iBAAiB;QAoBlC,MAAM+I,WAAqB,OAAO,EAChCtM,SAAS,EACTuM,gBAAgB,KAAK,EACrBC,mBAAmB,EACpB;YACC,2DAA2D;YAC3D,IAAI9T,0BAGF,AAFA,uEAAuE;YACvE,6DAA6D;YAC5D,CAACuR,qBAAqBvB,KAAKnS,GAAG,KAAK,QACpC,qEAAqE;YACrE,gBAAgB;YACf,CAACiT,SAAS,CAACL,qBACZ,mEAAmE;YACnE,QAAQ;YACR,OAAOnJ,cAAc,YACrB,sEAAsE;YACtE,uBAAuB;YACvB+K;YAEF,MAAM0B,YAAYrhB,SAASkH,IAAIiB,GAAG,IAAI,IAAI,MAAMyL,KAAK;YAErD,mDAAmD;YACnD,kBAAkB;YAClB,IAAI0J,KAAK7U,MAAM,EAAE;gBACfwG,OAAOC,IAAI,CAACoO,KAAK7U,MAAM,EAAEyR,OAAO,CAAC,CAAC7D;oBAChC,OAAOgL,SAAS,CAAChL,IAAI;gBACvB;YACF;YACA,MAAMiL,mBACJ5M,gBAAgB,OAAO,IAAI,CAAChO,UAAU,CAAC2C,aAAa;YAEtD,MAAMkY,cAAczhB,UAAU;gBAC5BuH,UAAU,GAAGiX,sBAAsBgD,mBAAmB,MAAM,IAAI;gBAChE,uDAAuD;gBACvD1N,OAAOyN;YACT;YAEA,uEAAuE;YACvE,MAAMG,uBAAuB3B,aAAaR;YAE1C,MAAMhS,aAA+B;gBACnC,GAAGkQ,UAAU;gBACb,GAAGD,IAAI;gBACP,GAAIJ,YACA;oBACEnF;oBACA,gEAAgE;oBAChE,+DAA+D;oBAC/D,4DAA4D;oBAC5D,WAAW;oBACX0J,cAAcrD,SAAS,CAACxJ,aAAa,CAAC+K;oBACtC+B,eAAe,IAAI,CAAChb,UAAU,CAACC,YAAY,CAAC+a,aAAa;gBAC3D,IACA,CAAC,CAAC;gBACN7C;gBACA0C;gBACAxL;gBACA7J;gBACArC;gBACAyW,oBAAoB,IAAI,CAAC5Z,UAAU,CAACC,YAAY,CAAC2Z,kBAAkB;gBACnE,uFAAuF;gBACvF,8DAA8D;gBAC9D,SAAS;gBACTqB,gBACE9D,kBAAkBI,qBACdne,UAAU;oBACR,iEAAiE;oBACjE,UAAU;oBACVuH,UAAU,GAAGqN,cAAc4M,mBAAmB,MAAM,IAAI;oBACxD1N,OAAOyN;gBACT,KACAE;gBACN5a,cAAc;oBACZ,GAAG2W,KAAK3W,YAAY;oBACpB0Y;gBACF;gBACA/R;gBACAkU;gBACAhB;gBACAoB,aAAaxB;gBACbpC;gBACApJ;gBACAiH,WAAW,IAAI,CAACF,YAAY;gBAC5BkG,SAASvZ,IAAIuZ,OAAO,CAAC9R,IAAI,CAACzH;gBAC1BwZ,kBAAkB/a;gBAClB,wBAAwB;gBACxBgb,cAAc,AAAC,IAAI,CAASA,YAAY;YAC1C;YAEA,IAAIxC,sBAAsBC,wBAAwB;gBAChDlS,0BAA0B;gBAC1BD,WAAW2U,UAAU,GAAG;gBACxB3U,WAAWC,uBAAuB,GAAG;gBACrCD,WAAW4U,kBAAkB,GAAG;gBAChC5U,WAAWoU,YAAY,GAAG;gBAC1BpU,WAAWmS,sBAAsB,GAAGA;YACtC;YAEA,qEAAqE;YACrE,wBAAwB;YACxB,IAAI/X;YAEJ,IAAIsX,aAAa;gBACf,IAAIta,sBAAsBsa,cAAc;wBAuBf;oBAtBvB,IACE,qEAAqE;oBACrE,6DAA6D;oBAC7DnW,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B,CAAC9D,kBAAkBkC,QACnB,CAACjC,mBAAmBqD,MACpB;wBACA,MAAM,qBAEL,CAFK,IAAIlC,MACR,0EADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,MAAM8b,UAAuC;wBAC3CzZ,QAAQ6U,KAAK7U,MAAM;wBACnB+U;wBACAnQ,YAAY;4BACV1G,cAAc;gCACZ6I,WAAWnC,WAAW1G,YAAY,CAAC6I,SAAS;gCAC5CI,gBAAgBvC,WAAW1G,YAAY,CAACiJ,cAAc;4BACxD;4BACAtC;4BACAyK;4BACArJ,iBAAiB,GAAE,gCAAA,IAAI,CAAChI,UAAU,CAACC,YAAY,qBAA5B,8BAA8BgI,SAAS;4BAC1D8S,cAAcrD;4BACdvC,WAAW,IAAI,CAACF,YAAY;4BAC5BkG,SAASvZ,IAAIuZ,OAAO,CAAC9R,IAAI,CAACzH;4BAC1BwZ,kBAAkB/a;4BAClB8I,+BACE,IAAI,CAACxC,UAAU,CAACwC,6BAA6B;wBACjD;wBACAsS,eAAe;4BACbxZ,SAAS,IAAI,CAACA,OAAO;wBACvB;oBACF;oBAEA,IAAI;wBACF,MAAMyZ,UAAUne,mBAAmBoe,mBAAmB,CACpDnb,KACAhD,uBAAuBoE,IAAI+K,gBAAgB;wBAG7C,MAAM+F,WAAW,MAAM2F,YAAYuD,MAAM,CAACF,SAASF;wBAEjDhb,IAAYqb,YAAY,GAAG,AAC3BL,QAAQ7U,UAAU,CAClBkV,YAAY;wBAEd,MAAMC,YAAYN,QAAQ7U,UAAU,CAACoV,aAAa;wBAElD,mEAAmE;wBACnE,oBAAoB;wBACpB,IAAIrE,OAAO;4BACT,MAAMsE,OAAO,MAAMtJ,SAASsJ,IAAI;4BAEhC,sCAAsC;4BACtC,MAAM7a,UAAUnE,0BAA0B0V,SAASvR,OAAO;4BAE1D,IAAI2a,WAAW;gCACb3a,OAAO,CAAC/D,uBAAuB,GAAG0e;4BACpC;4BAEA,IAAI,CAAC3a,OAAO,CAAC,eAAe,IAAI6a,KAAK1H,IAAI,EAAE;gCACzCnT,OAAO,CAAC,eAAe,GAAG6a,KAAK1H,IAAI;4BACrC;4BAEA,MAAM2H,aACJ,OAAOT,QAAQ7U,UAAU,CAACuV,mBAAmB,KAAK,eAClDV,QAAQ7U,UAAU,CAACuV,mBAAmB,IAAIhf,iBACtC,QACAse,QAAQ7U,UAAU,CAACuV,mBAAmB;4BAE5C,MAAMxH,SACJ,OAAO8G,QAAQ7U,UAAU,CAACwV,eAAe,KAAK,eAC9CX,QAAQ7U,UAAU,CAACwV,eAAe,IAAIjf,iBAClCmD,YACAmb,QAAQ7U,UAAU,CAACwV,eAAe;4BAExC,2CAA2C;4BAC3C,MAAMpF,aAAiC;gCACrCnH,OAAO;oCACLjE,MAAM7S,gBAAgBsjB,SAAS;oCAC/BC,QAAQ3J,SAAS2J,MAAM;oCACvBrP,MAAMmB,OAAOmO,IAAI,CAAC,MAAMN,KAAKO,WAAW;oCACxCpb;gCACF;gCACAoT,cAAc;oCAAE0H;oCAAYvH;gCAAO;gCACnC8H,YAAY;4BACd;4BAEA,OAAOzF;wBACT;wBACA,IAAI0F,mBAAmBjB,QAAQ7U,UAAU,CAAC8V,gBAAgB;wBAE1D,gDAAgD;wBAChD,qDAAqD;wBACrD,IAAIA,kBAAkB;4BACpB,IAAIjB,QAAQ7U,UAAU,CAACwO,SAAS,EAAE;gCAChCqG,QAAQ7U,UAAU,CAACwO,SAAS,CAACsH;gCAC7BA,mBAAmBpc;4BACrB;wBACF;wBAEA,+DAA+D;wBAC/D,MAAMxD,aACJ2D,KACAoB,KACA8Q,UACA8I,QAAQ7U,UAAU,CAAC8V,gBAAgB;wBAErC,OAAO;oBACT,EAAE,OAAOhS,KAAK;wBACZ,MAAM,IAAI,CAACrB,6BAA6B,CAACqB,KAAKjK,KAAK;4BACjDkc,YAAY;4BACZC,WAAWhc;4BACXic,WAAW;4BACXC,kBAAkBhe,oBAAoB8H;wBACxC;wBAEA,8DAA8D;wBAC9D,IAAI+Q,OAAO,MAAMjN;wBAEjB9P,IAAIuQ,KAAK,CAACT;wBAEV,kCAAkC;wBAClC,MAAM5N,aAAa2D,KAAKoB,KAAK,IAAI+Q,SAAS,MAAM;4BAAE0J,QAAQ;wBAAI;wBAE9D,OAAO;oBACT;gBACF,OAAO,IACLre,mBAAmBqa,gBACnBva,qBAAqBua,cACrB;oBACA,mDAAmD;oBACnD,IAAI7X,IAAIqK,MAAM,KAAK,aAAa,CAACoM,WAAW;wBAC1C,MAAMpa,aAAa2D,KAAKoB,KAAK,IAAI+Q,SAAS,MAAM;4BAAE0J,QAAQ;wBAAI;wBAC9D,OAAO;oBACT;oBAEA,IAAIre,mBAAmBqa,cAAc;wBACnC,wEAAwE;wBACxE,sEAAsE;wBACtE,iCAAiC;wBACjC,4HAA4H;wBAC5H1R,WAAWJ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;wBACnDI,WAAWmW,uBAAuB,GAChCjG,WAAWiG,uBAAuB;wBAEpC,MAAMpB,UAAUpd,kBAAkBkC,OAAOA,IAAIiM,eAAe,GAAGjM;wBAC/D,MAAMkS,WAAWnU,mBAAmBqD,OAChCA,IAAI+K,gBAAgB,GACpB/K;wBAEJ,iDAAiD;wBACjD,IAAI;4BACFb,SAAS,MAAMsX,YAAYvD,MAAM,CAC/B4G,SACAhJ,UACA;gCACE7D,MAAMlO;gCACNoB,QAAQ6U,KAAK7U,MAAM;gCACnBmL;gCACAvG;gCACA8U,eAAe;oCACbxZ,SAAS,IAAI,CAACA,OAAO;oCACrByE,cAAc,IAAI,CAAC1G,UAAU,CAAC0G,YAAY;oCAC1C4O,cAAc,IAAI,CAACzQ,aAAa,CAACyQ,YAAY,IAAIjV;gCACnD;gCACA0c,eAAe;oCACbP,YAAY/B;oCACZS,aAAavU,WAAWuU,WAAW;oCACnC8B,+BAA+B/hB,eAC7BuF,KACA;gCAEJ;4BACF;wBAEJ,EAAE,OAAOiK,KAAK;4BACZ,MAAM,IAAI,CAACrB,6BAA6B,CAACqB,KAAKjK,KAAK;gCACjDkc,YAAY;gCACZC,WAAWhc;gCACXic,WAAW;gCACXC,kBAAkBhe,oBAAoB;oCACpCkc,cAAcrD;oCACdoC,sBAAsBnT,WAAWmT,oBAAoB;gCACvD;4BACF;4BACA,MAAMrP;wBACR;oBACF,OAAO;wBACL,MAAMwS,SAASpG,WAAWwB,WAAW;wBAErC,4EAA4E;wBAC5E,8DAA8D;wBAC9D,4HAA4H;wBAC5H1R,WAAWJ,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;wBAEnD,MAAMiV,UAAsC;4BAC1C3M,MAAMoI,YAAY,SAAStW;4BAC3BoB,QAAQ6U,KAAK7U,MAAM;4BACnBmL;4BACAwN;4BACA/T;4BACAzG,0BAA0B,IAAI,CAACH,2BAA2B;4BAC1D0b,eAAe;gCACbxZ,SAAS,IAAI,CAACA,OAAO;4BACvB;wBACF;wBAEA,4DAA4D;wBAC5D,iEAAiE;wBACjE,wCAAwC;wBACxC,IACE,IAAI,CAACjC,UAAU,CAACC,YAAY,CAAC6I,SAAS,IACtC,IAAI,CAACnC,UAAU,CAAClC,GAAG,IACnB,CAAC2T,wBACD,CAACd,wBACD;4BACA,MAAM4F,SAAS,MAAMD,OAAOC,MAAM,CAAC1c,KAAKoB,KAAK4Z;4BAE7C,6DAA6D;4BAC7D,yBAAyB;4BACzB,IAAI0B,OAAOC,QAAQ,CAACC,wBAAwB,EAAE;gCAC5CzW,WAAWyW,wBAAwB,GACjCF,OAAOC,QAAQ,CAACC,wBAAwB;4BAC5C;wBACF;wBAEA,iDAAiD;wBACjDrc,SAAS,MAAMkc,OAAOnI,MAAM,CAACtU,KAAKoB,KAAK4Z;oBACzC;gBACF,OAAO;oBACL,MAAM,qBAAiD,CAAjD,IAAI9b,MAAM,yCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAgD;gBACxD;YACF,OAAO;gBACL,oEAAoE;gBACpE,iBAAiB;gBACjBqB,SAAS,MAAM,IAAI,CAACsc,UAAU,CAAC7c,KAAKoB,KAAKjB,UAAUuM,OAAOvG;YAC5D;YAEA,MAAM,EAAEwW,QAAQ,EAAE,GAAGpc;YAErB,MAAM,EACJwT,YAAY,EACZpT,UAAU,CAAC,CAAC,EACZ,oEAAoE;YACpEmc,WAAWxB,SAAS,EACrB,GAAGqB;YAEJ,IAAIrB,WAAW;gBACb3a,OAAO,CAAC/D,uBAAuB,GAAG0e;YACpC;YAEA,2DAA2D;;YACzDtb,IAAYqb,YAAY,GAAGsB,SAAStB,YAAY;YAElD,0DAA0D;YAC1D,gEAAgE;YAChE,qDAAqD;YACrD,IACErF,aACAkB,SACAnD,CAAAA,gCAAAA,aAAc0H,UAAU,MAAK,KAC7B,CAAC,IAAI,CAACtV,UAAU,CAAClC,GAAG,IACpB,CAACkU,mBACD;gBACA,MAAM4E,oBAAoBJ,SAASI,iBAAiB;gBAEpD,MAAM9S,MAAM,qBAOX,CAPW,IAAI/K,MACd,CAAC,+CAA+C,EAAEsO,cAChDuP,CAAAA,qCAAAA,kBAAmBC,WAAW,IAC1B,CAAC,UAAU,EAAED,kBAAkBC,WAAW,EAAE,GAC5C,EAAE,EACN,GACA,CAAC,4EAA4E,CAAC,GANtE,qBAAA;2BAAA;gCAAA;kCAAA;gBAOZ;gBAEA,IAAID,qCAAAA,kBAAmBE,KAAK,EAAE;oBAC5B,MAAMA,QAAQF,kBAAkBE,KAAK;oBACrChT,IAAIgT,KAAK,GAAGhT,IAAIiT,OAAO,GAAGD,MAAM7a,SAAS,CAAC6a,MAAME,OAAO,CAAC;gBAC1D;gBAEA,MAAMlT;YACR;YAEA,uEAAuE;YACvE,iBAAiB;YAEjB,uBAAuB;YACvB,IAAI,gBAAgB0S,YAAYA,SAASS,UAAU,EAAE;gBACnD,OAAO;oBACLhO,OAAO;oBACP2E;oBACAiI,YAAY;gBACd;YACF;YAEA,uBAAuB;YACvB,IAAIW,SAASU,UAAU,EAAE;gBACvB,OAAO;oBACLjO,OAAO;wBACLjE,MAAM7S,gBAAgBglB,QAAQ;wBAC9BC,OAAOZ,SAASlD,QAAQ,IAAIkD,SAASa,UAAU;oBACjD;oBACAzJ;oBACAiI,YAAY;gBACd;YACF;YAEA,mBAAmB;YACnB,IAAIzb,OAAOkd,MAAM,EAAE;gBACjB,OAAO;YACT;YAEA,kEAAkE;YAClE,IAAIzH,WAAW;gBACb,OAAO;oBACL5G,OAAO;wBACLjE,MAAM7S,gBAAgBolB,QAAQ;wBAC9BC,MAAMpd;wBACNI;wBACAid,SAASjB,SAASa,UAAU;wBAC5B9P,WAAWiP,SAASjP,SAAS;wBAC7BmO,QAAQza,IAAIuK,UAAU;wBACtBkS,aAAalB,SAASkB,WAAW;oBACnC;oBACA9J;oBACAiI,YAAY,CAAC,CAAC9B;gBAChB;YACF;YAEA,OAAO;gBACL9K,OAAO;oBACLjE,MAAM7S,gBAAgBwlB,KAAK;oBAC3BH,MAAMpd;oBACNkZ,UAAUkD,SAASlD,QAAQ,IAAIkD,SAASa,UAAU;oBAClD7c;oBACAkb,QAAQ7F,YAAY5U,IAAIuK,UAAU,GAAG9L;gBACvC;gBACAkU;gBACAiI,YAAY/B;YACd;QACF;QAEA,IAAI8D,oBAAuC,OAAO,EAChDC,WAAW,EACXC,kBAAkB,EAClBC,cAAc,EACf;YACC,MAAMC,eAAe,CAAC,IAAI,CAAChY,UAAU,CAAClC,GAAG;YACzC,MAAMma,aAAaJ,eAAe5c,IAAI4S,IAAI;YAE1C,sEAAsE;YACtE,IAAI,CAACoB,eAAekC,WAAW;gBAC7B,IAAIT,mBAAmB;oBACrB,MAAMU,cAAc,MAAM,IAAI,CAACtC,cAAc,CAAC;wBAC5C9U;wBACA4Q,gBAAgB/Q,IAAIW,OAAO;wBAC3BqV;wBACA3H,MAAMgI,WAAWhI,IAAI;oBACvB;oBAEA+G,cAAcmC,YAAYnC,WAAW;oBACrCC,eAAekC,YAAYlC,YAAY;gBACzC,OAAO;oBACLD,cAAcvV;oBACdwV,eAAe9W,aAAa8f,SAAS;gBACvC;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,0BAA0B;YAC1B,IACEhJ,iBAAiB9W,aAAa+f,SAAS,IACvCvkB,MAAMiG,IAAIW,OAAO,CAAC,aAAa,IAAI,KACnC;gBACA0U,eAAe9W,aAAaggB,sBAAsB;YACpD;YAEA,wDAAwD;YACxD,iCAAiC;YACjC,IACEjF,wBACAC,2BACA,CAAC0E,sBACD,CAAC,IAAI,CAAC/Z,WAAW,EACjB;gBACA,MAAM,IAAI,CAACrC,SAAS,CAAC7B,KAAKoB;gBAC1B,OAAO;YACT;YAEA,IAAI6c,CAAAA,sCAAAA,mBAAoBO,OAAO,MAAK,CAAC,GAAG;gBACtClF,uBAAuB;YACzB;YAEA,sBAAsB;YACtB,8DAA8D;YAC9D,2CAA2C;YAC3C,IACEA,wBACCjE,CAAAA,iBAAiB9W,aAAa8f,SAAS,IAAIJ,kBAAiB,GAC7D;gBACA5I,eAAe9W,aAAaggB,sBAAsB;YACpD;YAEA,sEAAsE;YACtE,uDAAuD;YACvD,EAAE;YACF,sEAAsE;YACtE,8DAA8D;YAC9D,EAAE;YACF,sEAAsE;YACtE,0BAA0B;YAC1B,IAAIE,gBAAgB1E;YACpB,IAAI,CAAC0E,iBAAiBrI,KAAKnS,GAAG,IAAI+R,WAAW;gBAC3CyI,gBAAgBthB,iBAAiBia;YACnC;YACA,IAAIqH,iBAAiB/R,MAAMlG,GAAG,EAAE;gBAC9BiY,gBAAgBA,cAActR,OAAO,CAAC,UAAU;YAClD;YAEA,MAAMuR,8BACJD,kBAAiBrJ,+BAAAA,YAAaqC,QAAQ,CAACgH;YAEzC,qEAAqE;YACrE,kCAAkC;YAElC,kCAAkC;YAClC,IAAI,IAAI,CAACjf,UAAU,CAACC,YAAY,CAACwI,qBAAqB,EAAE;gBACtDoN,eAAe9W,aAAaggB,sBAAsB;YACpD;YAEA,oEAAoE;YACpE,kCAAkC;YAClC,EAAE;YACF,gCAAgC;YAChC,0CAA0C;YAC1C,wEAAwE;YACxE,iEAAiE;YACjE,yBAAyB;YACzB,iEAAiE;YACjE,qEAAqE;YACrE,EAAE;YACF,IACE7c,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7B,CAAC,IAAI,CAACsC,WAAW,IACjBmR,iBAAiB9W,aAAaggB,sBAAsB,IACpDE,iBACA,CAACL,cACD,CAAClF,iBACD5B,aACC6G,CAAAA,gBAAgB,CAAC/I,eAAe,CAACsJ,2BAA0B,GAC5D;gBACA,IAGE,AAFA,2DAA2D;gBAC3D,kBAAkB;gBACjBP,CAAAA,gBAAiB/I,eAAeA,CAAAA,+BAAAA,YAAapT,MAAM,IAAG,CAAC,KACxD,2DAA2D;gBAC3DqT,iBAAiB9W,aAAa8f,SAAS,EACvC;oBACA,MAAM,IAAIpf;gBACZ;gBAEA,IAAI0f;gBAEJ,kCAAkC;gBAClC,IAAInhB,mBAAmB6Y,WAAWwB,WAAW,KAAK,CAACF,mBAAmB;oBACpE,gEAAgE;oBAChE,oCAAoC;oBACpCgH,mBAAmB,MAAM,IAAI,CAAChV,aAAa,CAACmC,GAAG,CAC7CqS,eAAgBtP,SAAS,CAAC,CAAC,EAAEA,SAAS1O,UAAU,GAAGA,WAAY,MAC/D,yDAAyD;oBACzD,OAAO,EACL8d,oBAAoBW,6BAA6B,IAAI,EACtD;wBACC,2DAA2D;wBAC3D,8DAA8D;wBAC9D,gEAAgE;wBAChE,iEAAiE;wBACjE,YAAY;wBACZ,IAAIT,cAAc;4BAChB,OAAO1f,qBAAqBmgB;wBAC9B;wBAEA,kEAAkE;wBAClE,UAAU;wBACV,OAAO5E,SAAS;4BACdtM,WAAW7N;4BACX,2DAA2D;4BAC3D,+DAA+D;4BAC/D,qBAAqB;4BACrBoa,eAAe;4BACfC,qBAAqB;wBACvB;oBACF,GACA;wBACE2E,WAAWvgB,UAAUwf,KAAK;wBAC1BjN;wBACAsH;wBACA6D,YAAY;oBACd;gBAEJ,OAGK,IACH7D,qBACA7a,qBAAqB+Y,WAAWwB,WAAW,KAC3C,CAACpM,cACD;oBACA,gEAAgE;oBAChE,oCAAoC;oBACpCkT,mBAAmB,MAAM,IAAI,CAAChV,aAAa,CAACmC,GAAG,CAC7CqS,eAAehe,WAAW,MAC1B,yDAAyD;oBACzD,UACE6Z,SAAS;4BACP,4DAA4D;4BAC5D,QAAQ;4BACRtM,WAAW7N;4BACXoa,eAAepa;4BACfqa,qBACE,yDAAyD;4BACzD,wDAAwD;4BACxD,YAAY;4BACZiE,gBAAgB5F,uBACZlgB,uBAAuB8H,YACvB;wBACR,IACF;wBACE0e,WAAWvgB,UAAUof,QAAQ;wBAC7B7M;wBACAsH;wBACA6D,YAAY;oBACd;gBAEJ;gBAEA,wEAAwE;gBACxE,IAAI2C,qBAAqB,MAAM,OAAO;gBAEtC,qEAAqE;gBACrE,IAAIA,kBAAkB;oBACpB,sEAAsE;oBACtE,iCAAiC;oBACjC,OAAOA,iBAAiB5K,YAAY;oBAEpC,OAAO4K;gBACT;YACF;YAEA,wEAAwE;YACxE,oEAAoE;YACpE,MAAMjR,YACJ,CAAC4L,wBAAwB,CAAC4E,kBAAkB1F,mBACxCA,mBACA3Y;YAEN,yEAAyE;YACzE,wEAAwE;YACxE,IACE,AAACwY,CAAAA,sBAAsBC,sBAAqB,KAC5C,OAAO5K,cAAc,aACrB;gBACA,OAAO;oBACLqG,cAAc;wBAAE0H,YAAY;wBAAGvH,QAAQrU;oBAAU;oBACjDmc,YAAY;oBACZ5M,OAAO;wBACLjE,MAAM7S,gBAAgBwlB,KAAK;wBAC3BH,MAAM3jB,aAAa8e,UAAU,CAAC;wBAC9BW,UAAU,CAAC;wBACX9Y,SAASd;wBACTgc,QAAQhc;oBACV;gBACF;YACF;YAEA,oEAAoE;YACpE,qEAAqE;YACrE,2DAA2D;YAC3D,MAAMqa,sBACJ5C,aACAa,qBACC1d,CAAAA,eAAeuF,KAAK,gCACnBuY,oBAAmB,IACjBlgB,uBAAuB8H,YACvB;YAEN,sBAAsB;YACtB,OAAO6Z,SAAS;gBACdtM;gBACAuM,eAAepa;gBACfqa;YACF;QACF;QAEA,MAAM3D,aAAa,MAAM,IAAI,CAAC5M,aAAa,CAACmC,GAAG,CAC7CiO,aACAgE,mBACA;YACEc,WACE,sEAAsE;YACtE,qCAAqC;YACrChH,CAAAA,+BAAAA,YAAa1J,UAAU,CAAChD,IAAI,KAC3B6K,CAAAA,YAAY1X,UAAUof,QAAQ,GAAGpf,UAAUwf,KAAK,AAAD;YAClDjN;YACAyI;YACAwF,YAAY9e,IAAIW,OAAO,CAACoe,OAAO,KAAK;YACpC5G;QACF;QAGF,IAAIe,eAAe;YACjB9X,IAAI6S,SAAS,CACX,iBACA;QAEJ;QAEA,IAAI,CAACsC,YAAY;YACf,IAAIwD,eAAe,CAAET,CAAAA,wBAAwBC,uBAAsB,GAAI;gBACrE,gEAAgE;gBAChE,oEAAoE;gBACpE,kEAAkE;gBAClE,mEAAmE;gBACnE,yBAAyB;gBACzB,MAAM,qBAA8D,CAA9D,IAAIra,MAAM,sDAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6D;YACrE;YACA,OAAO;QACT;QAEA,2EAA2E;QAC3E,4EAA4E;QAC5E,IACE6a,eACA,CAAC,IAAI,CAAC7V,WAAW,IACjBiU,qBACA5B,EAAAA,oBAAAA,WAAWnH,KAAK,qBAAhBmH,kBAAkBpL,IAAI,MAAK7S,gBAAgBolB,QAAQ,IACnDnH,WAAWyF,UAAU,IACrB,CAAC1C,wBACD,uEAAuE;QACvE,mBAAmB;QACnB,CAACf,wBACD7W,QAAQC,GAAG,CAACqd,8BAA8B,KAAK,QAC/C;YACAtgB,mBAAmB;gBACjB,IAAI;oBACF,MAAM,IAAI,CAACiL,aAAa,CAACmC,GAAG,CAC1BiO,aACA,IACEC,SAAS;4BACP,8DAA8D;4BAC9D,uBAAuB;4BACvBE,qBAAqB;4BACrBD,eAAepa;4BACf6N,WAAW7N;wBACb,IACF;wBACEgf,WAAWvgB,UAAUof,QAAQ;wBAC7B7M;wBACAyI,sBAAsB;wBACtBwF,YAAY;wBACZ3G,mBAAmB;oBACrB;gBAEJ,EAAE,OAAOlO,KAAK;oBACZQ,QAAQC,KAAK,CAAC,gDAAgDT;gBAChE;YACF;QACF;QAEA,MAAMgV,cACJ1I,EAAAA,qBAAAA,WAAWnH,KAAK,qBAAhBmH,mBAAkBpL,IAAI,MAAK7S,gBAAgBolB,QAAQ,IACnD,OAAOnH,WAAWnH,KAAK,CAAC1B,SAAS,KAAK;QAExC,IACEwJ,SACA,yEAAyE;QACzE,kEAAkE;QAClE,gDAAgD;QAChD,CAACuB,uBACA,CAAA,CAACwG,eAAerH,oBAAmB,GACpC;YACA,IAAI,CAAC,IAAI,CAAC1T,WAAW,EAAE;gBACrB,gDAAgD;gBAChD,iCAAiC;gBACjC9C,IAAI6S,SAAS,CACX,kBACAqF,uBACI,gBACA/C,WAAW2I,MAAM,GACf,SACA3I,WAAWiI,OAAO,GAChB,UACA;YAEZ;YACA,0EAA0E;YAC1E,yDAAyD;YACzDpd,IAAI6S,SAAS,CAACzY,0BAA0B;QAC1C;QAEA,MAAM,EAAE4T,OAAO+P,UAAU,EAAE,GAAG5I;QAE9B,yDAAyD;QACzD,IAAI4I,CAAAA,8BAAAA,WAAYhU,IAAI,MAAK7S,gBAAgB8mB,KAAK,EAAE;YAC9C,MAAM,qBAAgE,CAAhE,IAAItgB,eAAe,+CAAnB,qBAAA;uBAAA;4BAAA;8BAAA;YAA+D;QACvE;QAEA,sDAAsD;QACtD,IAAIiV;QAEJ,0EAA0E;QAC1E,oCAAoC;QACpC,IAAIyE,kBAAkB;YACpBzE,eAAe;gBAAE0H,YAAY;gBAAGvH,QAAQrU;YAAU;QACpD,OAKK,IACH,IAAI,CAACqE,WAAW,IAChBuH,gBACA,CAACmM,wBACDO,mBACA;YACApE,eAAe;gBAAE0H,YAAY;gBAAGvH,QAAQrU;YAAU;QACpD,OAAO,IAAI,CAAC,IAAI,CAACsG,UAAU,CAAClC,GAAG,IAAK0S,kBAAkB,CAACgB,mBAAoB;YACzE,2DAA2D;YAC3D,IAAIuB,eAAe;gBACjBnF,eAAe;oBAAE0H,YAAY;oBAAGvH,QAAQrU;gBAAU;YACpD,OAIK,IAAI,CAACqX,OAAO;gBACf,IAAI,CAAC9V,IAAIie,SAAS,CAAC,kBAAkB;oBACnCtL,eAAe;wBAAE0H,YAAY;wBAAGvH,QAAQrU;oBAAU;gBACpD;YACF,OAQK,IAAI4W,WAAW;gBAClB,MAAM6I,qBAAqB7kB,eAAeuF,KAAK;gBAE/C+T,eAAe;oBACb0H,YACE,OAAO6D,uBAAuB,cAAc,IAAIA;oBAClDpL,QAAQrU;gBACV;YACF,OAAO,IAAI6W,WAAW;gBACpB3C,eAAe;oBAAE0H,YAAY;oBAAGvH,QAAQrU;gBAAU;YACpD,OAAO,IAAI0W,WAAWxC,YAAY,EAAE;gBAClC,wEAAwE;gBACxE,oBAAoB;gBACpB,IAAI,OAAOwC,WAAWxC,YAAY,CAAC0H,UAAU,KAAK,UAAU;wBAUtDlF;oBATJ,IAAIA,WAAWxC,YAAY,CAAC0H,UAAU,GAAG,GAAG;wBAC1C,MAAM,qBAEL,CAFK,IAAIvc,MACR,CAAC,2CAA2C,EAAEqX,WAAWxC,YAAY,CAAC0H,UAAU,CAAC,IAAI,CAAC,GADlF,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA1H,eAAe;wBACb0H,YAAYlF,WAAWxC,YAAY,CAAC0H,UAAU;wBAC9CvH,QACEqC,EAAAA,2BAAAA,WAAWxC,YAAY,qBAAvBwC,yBAAyBrC,MAAM,KAAI,IAAI,CAAC1U,UAAU,CAAC2I,UAAU;oBACjE;gBACF,OAGK;oBACH4L,eAAe;wBAAE0H,YAAYhf;wBAAgByX,QAAQrU;oBAAU;gBACjE;YACF;QACF;QAEA0W,WAAWxC,YAAY,GAAGA;QAE1B,IACE,OAAO2E,0BAA0B,YACjCyG,CAAAA,8BAAAA,WAAYhU,IAAI,MAAK7S,gBAAgBolB,QAAQ,IAC7CyB,WAAWtB,WAAW,EACtB;gBAeasB;YAdb,uEAAuE;YACvE,sEAAsE;YACtE,sEAAsE;YAEtE,oEAAoE;YACpE,uEAAuE;YACvE,wEAAwE;YACxE,sEAAsE;YACtE,sEAAsE;YACtE,wDAAwD;YACxD/d,IAAI6S,SAAS,CAAC5Y,0BAA0B;YAExC,sEAAsE;YACtE,8CAA8C;YAC9C,MAAMkkB,QAAOJ,sBAAAA,WAAWxe,OAAO,qBAAlBwe,mBAAoB,CAACviB,uBAAuB;YACzD,IAAI,IAAI,CAACsH,WAAW,IAAIgT,SAASqI,QAAQ,OAAOA,SAAS,UAAU;gBACjEne,IAAI6S,SAAS,CAACrX,wBAAwB2iB;YACxC;YAEA,MAAMC,iBAAiBL,WAAWtB,WAAW,CAAC/R,GAAG,CAAC4M;YAClD,IAAI8G,mBAAmB3f,WAAW;gBAChC,YAAY;gBACZ,OAAO;oBACLiU,MAAM;oBACNtH,MAAMxS,aAAa8e,UAAU,CAAC0G;oBAC9B,mEAAmE;oBACnE,+BAA+B;oBAC/BzL,cAAcwC,WAAWxC,YAAY;gBACvC;YACF;YAEA,yEAAyE;YACzE,yEAAyE;YACzE,sEAAsE;YACtE,qEAAqE;YACrE,oEAAoE;YACpE,gCAAgC;YAChC3S,IAAIuK,UAAU,GAAG;YACjB,OAAO;gBACLmI,MAAM;gBACNtH,MAAMxS,aAAa8e,UAAU,CAAC;gBAC9B/E,YAAY,EAAEwC,8BAAAA,WAAYxC,YAAY;YACxC;QACF;QAEA,yEAAyE;QACzE,8BAA8B;QAC9B,MAAM0L,eAAehlB,eAAeuF,KAAK;QACzC,IAAIyf,cAAc;gBASRlJ,oBAEIA;YAVZ,MAAMhT,WAAW,MAAMkc,aACrB;gBACE,GAAGlJ,UAAU;gBACb,0CAA0C;gBAC1C,wCAAwC;gBACxCnH,OAAO;oBACL,GAAGmH,WAAWnH,KAAK;oBACnBjE,MACEoL,EAAAA,qBAAAA,WAAWnH,KAAK,qBAAhBmH,mBAAkBpL,IAAI,MAAK7S,gBAAgBolB,QAAQ,GAC/C,UACAnH,qBAAAA,WAAWnH,KAAK,qBAAhBmH,mBAAkBpL,IAAI;gBAC9B;YACF,GACA;gBACElK,KAAKxG,eAAeuF,KAAK;YAC3B;YAEF,IAAIuD,UAAU;gBACZ,0CAA0C;gBAC1C,OAAO;YACT;QACF;QAEA,IAAI,CAAC4b,YAAY;gBAQb5I;YAPF,oDAAoD;YACpD,qDAAqD;YACrD,4DAA4D;YAC5D,2BAA2B;YAC3B/b,eACEwF,KACA,uBACAuW,4BAAAA,WAAWxC,YAAY,qBAAvBwC,0BAAyBkF,UAAU;YAGrC,2DAA2D;YAC3D,6DAA6D;YAC7D,IAAIlF,WAAWxC,YAAY,IAAI,CAAC3S,IAAIie,SAAS,CAAC,kBAAkB;gBAC9Dje,IAAI6S,SAAS,CACX,iBACAta,sBAAsB4c,WAAWxC,YAAY;YAEjD;YACA,IAAI4D,mBAAmB;gBACrBvW,IAAIuK,UAAU,GAAG;gBACjBvK,IAAIoL,IAAI,CAAC,qBAAqBC,IAAI;gBAClC,OAAO;YACT;YAEA,IAAI,IAAI,CAACtG,UAAU,CAAClC,GAAG,EAAE;gBACvBzJ,eAAewF,KAAK,iCAAiCG;YACvD;YACA,MAAM,IAAI,CAAC0B,SAAS,CAAC7B,KAAKoB,KAAK;gBAAEjB;gBAAUuM;YAAM,GAAG;YACpD,OAAO;QACT,OAAO,IAAIyS,WAAWhU,IAAI,KAAK7S,gBAAgBglB,QAAQ,EAAE;YACvD,2DAA2D;YAC3D,6DAA6D;YAC7D,IAAI/G,WAAWxC,YAAY,IAAI,CAAC3S,IAAIie,SAAS,CAAC,kBAAkB;gBAC9Dje,IAAI6S,SAAS,CACX,iBACAta,sBAAsB4c,WAAWxC,YAAY;YAEjD;YAEA,IAAI4D,mBAAmB;gBACrB,OAAO;oBACL7D,MAAM;oBACNtH,MAAMxS,aAAa8e,UAAU,CAC3B,6BAA6B;oBAC7B4G,KAAKC,SAAS,CAACR,WAAW5B,KAAK;oBAEjCxJ,cAAcwC,WAAWxC,YAAY;gBACvC;YACF,OAAO;gBACL,MAAMyF,eAAe2F,WAAW5B,KAAK;gBACrC,OAAO;YACT;QACF,OAAO,IAAI4B,WAAWhU,IAAI,KAAK7S,gBAAgBsjB,SAAS,EAAE;YACxD,MAAMjb,UAAUrE,4BAA4B6iB,WAAWxe,OAAO;YAE9D,IAAI,CAAE,CAAA,IAAI,CAACuD,WAAW,IAAIgT,KAAI,GAAI;gBAChCvW,QAAQif,MAAM,CAAChjB;YACjB;YAEA,2DAA2D;YAC3D,6DAA6D;YAC7D,IACE2Z,WAAWxC,YAAY,IACvB,CAAC3S,IAAIie,SAAS,CAAC,oBACf,CAAC1e,QAAQmL,GAAG,CAAC,kBACb;gBACAnL,QAAQkf,GAAG,CACT,iBACAlmB,sBAAsB4c,WAAWxC,YAAY;YAEjD;YAEA,MAAM1X,aACJ2D,KACAoB,KACA,IAAI+Q,SAASgN,WAAW3S,IAAI,EAAE;gBAC5B7L;gBACAkb,QAAQsD,WAAWtD,MAAM,IAAI;YAC/B;YAEF,OAAO;QACT,OAAO,IAAIsD,WAAWhU,IAAI,KAAK7S,gBAAgBolB,QAAQ,EAAE;gBAkC1CyB;YAjCb,oEAAoE;YACpE,gBAAgB;YAChB,IAAIF,eAAezG,kBAAkB;gBACnC,MAAM,qBAEL,CAFK,IAAItZ,MACR,yEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIigB,WAAWxe,OAAO,EAAE;gBACtB,MAAMA,UAAU;oBAAE,GAAGwe,WAAWxe,OAAO;gBAAC;gBAExC,IAAI,CAAC,IAAI,CAACuD,WAAW,IAAI,CAACgT,OAAO;oBAC/B,OAAOvW,OAAO,CAAC/D,uBAAuB;gBACxC;gBAEA,KAAK,IAAI,CAACuS,KAAKC,MAAM,IAAIrH,OAAOwC,OAAO,CAAC5J,SAAU;oBAChD,IAAI,OAAOyO,UAAU,aAAa;oBAElC,IAAIE,MAAMC,OAAO,CAACH,QAAQ;wBACxB,KAAK,MAAMK,KAAKL,MAAO;4BACrBhO,IAAI+U,YAAY,CAAChH,KAAKM;wBACxB;oBACF,OAAO,IAAI,OAAOL,UAAU,UAAU;wBACpCA,QAAQA,MAAMpC,QAAQ;wBACtB5L,IAAI+U,YAAY,CAAChH,KAAKC;oBACxB,OAAO;wBACLhO,IAAI+U,YAAY,CAAChH,KAAKC;oBACxB;gBACF;YACF;YAEA,sEAAsE;YACtE,8CAA8C;YAC9C,MAAMmQ,QAAOJ,uBAAAA,WAAWxe,OAAO,qBAAlBwe,oBAAoB,CAACviB,uBAAuB;YACzD,IAAI,IAAI,CAACsH,WAAW,IAAIgT,SAASqI,QAAQ,OAAOA,SAAS,UAAU;gBACjEne,IAAI6S,SAAS,CAACrX,wBAAwB2iB;YACxC;YAEA,0EAA0E;YAC1E,0EAA0E;YAC1E,oCAAoC;YACpC,IAAIJ,WAAWtD,MAAM,IAAK,CAAA,CAACpQ,gBAAgB,CAAC0M,iBAAgB,GAAI;gBAC9D/W,IAAIuK,UAAU,GAAGwT,WAAWtD,MAAM;YACpC;YAEA,sCAAsC;YACtC,IAAIoD,aAAa;gBACf7d,IAAI6S,SAAS,CAAC5Y,0BAA0B;YAC1C;YAEA,2DAA2D;YAC3D,oEAAoE;YACpE,0EAA0E;YAC1E,+BAA+B;YAC/B,IAAIoQ,gBAAgB,CAACyN,eAAe;gBAClC,8DAA8D;gBAC9D,IAAI,OAAOiG,WAAWvB,OAAO,KAAK,aAAa;oBAC7C,IAAIuB,WAAWzR,SAAS,EAAE;wBACxB,MAAM,qBAA0D,CAA1D,IAAIxO,MAAM,kDAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyD;oBACjE;oBAEA,OAAO;wBACL4U,MAAM;wBACNtH,MAAM2S,WAAWxB,IAAI;wBACrB,0DAA0D;wBAC1D,2DAA2D;wBAC3D,+DAA+D;wBAC/D,mBAAmB;wBACnB,+EAA+E;wBAC/E5J,cAAc0E,sBACV;4BAAEgD,YAAY;4BAAGvH,QAAQrU;wBAAU,IACnC0W,WAAWxC,YAAY;oBAC7B;gBACF;gBAEA,sEAAsE;gBACtE,QAAQ;gBACR,OAAO;oBACLD,MAAM;oBACNtH,MAAMxS,aAAa8e,UAAU,CAACqG,WAAWvB,OAAO;oBAChD7J,cAAcwC,WAAWxC,YAAY;gBACvC;YACF;YAEA,mCAAmC;YACnC,IAAIvH,OAAO2S,WAAWxB,IAAI;YAE1B,qEAAqE;YACrE,sEAAsE;YACtE,oDAAoD;YACpD,IAAI,CAACsB,eAAe,IAAI,CAAC/a,WAAW,EAAE;gBACpC,OAAO;oBACL4P,MAAM;oBACNtH;oBACAuH,cAAcwC,WAAWxC,YAAY;gBACvC;YACF;YAEA,sEAAsE;YACtE,uEAAuE;YACvE,sEAAsE;YACtE,4BAA4B;YAC5B,IAAIsE,sBAAsBC,wBAAwB;gBAChD,mEAAmE;gBACnE,mDAAmD;gBACnD9L,KAAKsT,KAAK,CACR,IAAIC,eAAe;oBACjBC,OAAMC,UAAU;wBACdA,WAAWC,OAAO,CAAC/hB,aAAagiB,MAAM,CAACC,aAAa;wBACpDH,WAAWlN,KAAK;oBAClB;gBACF;gBAGF,OAAO;oBACLe,MAAM;oBACNtH;oBACAuH,cAAc;wBAAE0H,YAAY;wBAAGvH,QAAQrU;oBAAU;gBACnD;YACF;YAEA,yEAAyE;YACzE,wEAAwE;YACxE,mBAAmB;YACnB,MAAMwgB,cAAc,IAAIC;YACxB9T,KAAKsT,KAAK,CAACO,YAAYE,QAAQ;YAE/B,wEAAwE;YACxE,wEAAwE;YACxE,yEAAyE;YACzEvG,SAAS;gBACPtM,WAAWyR,WAAWzR,SAAS;gBAC/BuM,eAAepa;gBACf,sEAAsE;gBACtE,YAAY;gBACZqa,qBAAqB;YACvB,GACGpH,IAAI,CAAC,OAAOvS;oBAKPA;gBAJJ,IAAI,CAACA,QAAQ;oBACX,MAAM,qBAAwD,CAAxD,IAAIrB,MAAM,gDAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAuD;gBAC/D;gBAEA,IAAIqB,EAAAA,gBAAAA,OAAO6O,KAAK,qBAAZ7O,cAAc4K,IAAI,MAAK7S,gBAAgBolB,QAAQ,EAAE;wBAELnd;oBAD9C,MAAM,qBAEL,CAFK,IAAIrB,MACR,CAAC,yCAAyC,GAAEqB,iBAAAA,OAAO6O,KAAK,qBAAZ7O,eAAc4K,IAAI,EAAE,GAD5D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,6CAA6C;gBAC7C,MAAM5K,OAAO6O,KAAK,CAACuO,IAAI,CAAC6C,MAAM,CAACH,YAAYI,QAAQ;YACrD,GACCC,KAAK,CAAC,CAACzW;gBACN,iEAAiE;gBACjE,0DAA0D;gBAC1DoW,YAAYI,QAAQ,CAACE,KAAK,CAAC1W,KAAKyW,KAAK,CAAC,CAACE;oBACrCnW,QAAQC,KAAK,CAAC,8BAA8BkW;gBAC9C;YACF;YAEF,OAAO;gBACL9M,MAAM;gBACNtH;gBACA,uEAAuE;gBACvE,wEAAwE;gBACxE,qCAAqC;gBACrCuH,cAAc;oBAAE0H,YAAY;oBAAGvH,QAAQrU;gBAAU;YACnD;QACF,OAAO,IAAI8X,mBAAmB;YAC5B,OAAO;gBACL7D,MAAM;gBACNtH,MAAMxS,aAAa8e,UAAU,CAAC4G,KAAKC,SAAS,CAACR,WAAW1F,QAAQ;gBAChE1F,cAAcwC,WAAWxC,YAAY;YACvC;QACF,OAAO;YACL,OAAO;gBACLD,MAAM;gBACNtH,MAAM2S,WAAWxB,IAAI;gBACrB5J,cAAcwC,WAAWxC,YAAY;YACvC;QACF;IACF;IAEQjG,kBAAkBtM,IAAY,EAAEqf,cAAc,IAAI,EAAE;QAC1D,IAAIrf,KAAKiW,QAAQ,CAAC,IAAI,CAAChW,OAAO,GAAG;YAC/B,MAAMqf,YAAYtf,KAAKY,SAAS,CAC9BZ,KAAK2b,OAAO,CAAC,IAAI,CAAC1b,OAAO,IAAI,IAAI,CAACA,OAAO,CAACO,MAAM;YAGlDR,OAAOtH,oBAAoB4mB,UAAU3T,OAAO,CAAC,WAAW;QAC1D;QAEA,IAAI,IAAI,CAAClI,gBAAgB,IAAI4b,aAAa;YACxC,OAAO,IAAI,CAAC5b,gBAAgB,CAACnE,SAAS,CAACU;QACzC;QACA,OAAOA;IACT;IAEA,0CAA0C;IAChCuf,oBAAoBhV,KAAa,EAAE;QAC3C,IAAI,IAAI,CAACvI,kBAAkB,CAACmC,GAAG,EAAE;gBACP;YAAxB,MAAMqb,mBAAkB,sBAAA,IAAI,CAAC7X,aAAa,qBAAlB,mBAAoB,CAAC4C,MAAM;YAEnD,IAAI,CAACiV,iBAAiB;gBACpB,OAAO;YACT;YAEA,OAAOA;QACT;QACA,OAAO;IACT;IAEA,MAAgBC,oBACd/W,GAAkD,EAClDgX,gBAAyB,EACzB;YAkBgB;QAjBhB,MAAM,EAAExU,KAAK,EAAEvM,QAAQ,EAAE,GAAG+J;QAE5B,MAAMiX,WAAW,IAAI,CAACJ,mBAAmB,CAAC5gB;QAC1C,MAAM6V,YAAY1G,MAAMC,OAAO,CAAC4R;QAEhC,IAAI9S,OAAOlO;QACX,IAAI6V,WAAW;YACb,4EAA4E;YAC5E3H,OAAO8S,QAAQ,CAACA,SAASnf,MAAM,GAAG,EAAE;QACtC;QAEA,MAAMzB,SAAS,MAAM,IAAI,CAAC6gB,kBAAkB,CAAC;YAC3CvS,QAAQpU,eAAeyP,IAAIlK,GAAG,EAAE;YAChCqO;YACA3B;YACAnL,QAAQ2I,IAAI/D,UAAU,CAAC5E,MAAM,IAAI,CAAC;YAClCyU;YACAqL,YAAY,CAAC,GAAC,oCAAA,IAAI,CAAC7hB,UAAU,CAACC,YAAY,CAAC6hB,GAAG,qBAAhC,kCAAkCC,SAAS;YACzDJ;YACA,sEAAsE;YACtEK,cAAc;QAChB;QACA,IAAIjhB,QAAQ;YACVvE,YAAYylB,oBAAoB,CAAC,cAActhB;YAC/C,IAAI;gBACF,OAAO,MAAM,IAAI,CAACmV,8BAA8B,CAACpL,KAAK3J;YACxD,EAAE,OAAO0J,KAAK;gBACZ,MAAMyX,oBAAoBzX,eAAehL;gBAEzC,IAAI,CAACyiB,qBAAsBA,qBAAqBR,kBAAmB;oBACjE,MAAMjX;gBACR;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAc+K,iBACZ9K,GAAkD,EACjB;QACjC,OAAOlO,YAAYiP,KAAK,CACtB9O,eAAe6Y,gBAAgB,EAC/B;YACE9J,UAAU,CAAC,cAAc,CAAC;YAC1BG,YAAY;gBACV,cAAcnB,IAAI/J,QAAQ;YAC5B;QACF,GACA;YACE,OAAO,IAAI,CAACwhB,oBAAoB,CAACzX;QACnC;IAEJ;IAQA,MAAcyX,qBACZzX,GAAkD,EACjB;YAQzB;QAPR,MAAM,EAAElK,GAAG,EAAEoB,GAAG,EAAEsL,KAAK,EAAEvM,QAAQ,EAAE,GAAG+J;QACtC,IAAImE,OAAOlO;QACX,MAAM+gB,mBACJzmB,eAAeyP,IAAIlK,GAAG,EAAE,uBAAuB;QACjD,OAAO0M,KAAK,CAACxR,qBAAqB;QAElC,MAAM4E,UAAwB;YAC5BiF,IAAI,GAAE,qBAAA,IAAI,CAAC1C,YAAY,qBAAjB,mBAAmBuf,WAAW,CAAC5hB,KAAKG;QAC5C;QAEA,IAAI;YACF,WAAW,MAAMG,SAAS,IAAI,CAACiJ,QAAQ,CAACsY,QAAQ,CAAC1hB,UAAUL,SAAU;gBACnE,uDAAuD;gBACvD,0DAA0D;gBAC1D,MAAMgiB,eAAernB,eAAeyP,IAAIlK,GAAG,EAAE;gBAC7C,IACE,CAAC,IAAI,CAACkE,WAAW,IACjB,OAAO4d,iBAAiB,YACxBtoB,eAAesoB,gBAAgB,OAC/BA,iBAAiBxhB,MAAM6N,UAAU,CAAChO,QAAQ,EAC1C;oBACA;gBACF;gBAEA,MAAMI,SAAS,MAAM,IAAI,CAAC0gB,mBAAmB,CAC3C;oBACE,GAAG/W,GAAG;oBACN/J,UAAUG,MAAM6N,UAAU,CAAChO,QAAQ;oBACnCgG,YAAY;wBACV,GAAG+D,IAAI/D,UAAU;wBACjB5E,QAAQjB,MAAMiB,MAAM;oBACtB;gBACF,GACA2f;gBAEF,IAAI3gB,WAAW,OAAO,OAAOA;YAC/B;YAEA,+DAA+D;YAC/D,6DAA6D;YAC7D,4DAA4D;YAC5D,mBAAmB;YACnB,sDAAsD;YACtD,IAAI,IAAI,CAAC8D,aAAa,CAACqM,eAAe,EAAE;gBACtC,sDAAsD;gBACtDxG,IAAI/J,QAAQ,GAAG,IAAI,CAACkE,aAAa,CAACqM,eAAe,CAACrC,IAAI;gBACtD,MAAM9N,SAAS,MAAM,IAAI,CAAC0gB,mBAAmB,CAAC/W,KAAKgX;gBACnD,IAAI3gB,WAAW,OAAO,OAAOA;YAC/B;QACF,EAAE,OAAOmK,OAAO;YACd,MAAMT,MAAM1P,eAAemQ;YAE3B,IAAIA,iBAAiBhS,mBAAmB;gBACtC+R,QAAQC,KAAK,CACX,yCACAgV,KAAKC,SAAS,CACZ;oBACEtR;oBACApN,KAAKiJ,IAAIlK,GAAG,CAACiB,GAAG;oBAChBqM,aAAapD,IAAIlK,GAAG,CAACW,OAAO,CAAChE,oBAAoB;oBACjDolB,SAAStnB,eAAeyP,IAAIlK,GAAG,EAAE;oBACjCiP,YAAY,CAAC,CAACxU,eAAeyP,IAAIlK,GAAG,EAAE;oBACtCgiB,YAAYvnB,eAAeyP,IAAIlK,GAAG,EAAE;gBACtC,GACA,MACA;gBAGJ,MAAMiK;YACR;YAEA,IAAIA,eAAehL,mBAAmBiiB,kBAAkB;gBACtD,MAAMjX;YACR;YACA,IAAIA,eAAezR,eAAeyR,eAAe1R,gBAAgB;gBAC/D6I,IAAIuK,UAAU,GAAG;gBACjB,OAAO,MAAM,IAAI,CAACsW,qBAAqB,CAAC/X,KAAKD;YAC/C;YAEA7I,IAAIuK,UAAU,GAAG;YAEjB,mDAAmD;YACnD,qDAAqD;YACrD,IAAI,MAAM,IAAI,CAACoJ,OAAO,CAAC,SAAS;gBAC9Bva,eAAe0P,IAAIlK,GAAG,EAAE,qBAAqB;gBAC7C,MAAM,IAAI,CAACiiB,qBAAqB,CAAC/X,KAAKD;gBACtCvP,kBAAkBwP,IAAIlK,GAAG,EAAE;YAC7B;YAEA,MAAMkiB,iBAAiBjY,eAAe9K;YAEtC,IAAI,CAAC+iB,gBAAgB;gBACnB,IACE,AAAC,IAAI,CAAChe,WAAW,IAAIxC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAClD,IAAI,CAACuE,UAAU,CAAClC,GAAG,EACnB;oBACA,IAAI3J,QAAQ2P,MAAMA,IAAIoE,IAAI,GAAGA;oBAC7B,MAAMpE;gBACR;gBACA,IAAI,CAACU,QAAQ,CAACpQ,eAAe0P;YAC/B;YACA,MAAMiI,WAAW,MAAM,IAAI,CAAC+P,qBAAqB,CAC/C/X,KACAgY,iBAAiB,AAACjY,IAA0B5K,UAAU,GAAG4K;YAE3D,OAAOiI;QACT;QAEA,MAAM7Q,aAAa,MAAM,IAAI,CAACC,aAAa;QAC3C,IACED,cACA,CAAC,CAAC6I,IAAIlK,GAAG,CAACW,OAAO,CAAC,gBAAgB,IACjC,CAAA,CAACS,IAAIuK,UAAU,IAAIvK,IAAIuK,UAAU,KAAK,OAAOvK,IAAIuK,UAAU,KAAK,GAAE,GACnE;YACA,MAAMkD,SAASpU,eAAeuF,KAAK;YAEnCoB,IAAI6S,SAAS,CACX,yBACA,GAAGpF,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAK1O,UAAU;YAE5CiB,IAAIuK,UAAU,GAAG;YACjBvK,IAAI6S,SAAS,CAAC,gBAAgB;YAC9B7S,IAAIoL,IAAI,CAAC;YACTpL,IAAIqL,IAAI;YACR,OAAO;QACT;QAEArL,IAAIuK,UAAU,GAAG;QACjB,OAAO,IAAI,CAACsW,qBAAqB,CAAC/X,KAAK;IACzC;IAEA,MAAaiY,aACXniB,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBuM,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO1Q,YAAYiP,KAAK,CAAC9O,eAAegmB,YAAY,EAAE;YACpD,OAAO,IAAI,CAACC,gBAAgB,CAACpiB,KAAKoB,KAAKjB,UAAUuM;QACnD;IACF;IAEA,MAAc0V,iBACZpiB,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBuM,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAAC0H,aAAa,CAAC,CAAClK,MAAQ,IAAI,CAAC8K,gBAAgB,CAAC9K,MAAM;YAC7DlK;YACAoB;YACAjB;YACAuM;QACF;IACF;IAEA,MAAa+D,YACXxG,GAAiB,EACjBjK,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBuM,QAA4B,CAAC,CAAC,EAC9B2V,aAAa,IAAI,EACF;QACf,OAAOrmB,YAAYiP,KAAK,CAAC9O,eAAesU,WAAW,EAAE;YACnD,OAAO,IAAI,CAAC6R,eAAe,CAACrY,KAAKjK,KAAKoB,KAAKjB,UAAUuM,OAAO2V;QAC9D;IACF;IAEA,MAAcC,gBACZrY,GAAiB,EACjBjK,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBuM,QAA4B,CAAC,CAAC,EAC9B2V,aAAa,IAAI,EACF;QACf,IAAIA,YAAY;YACdjhB,IAAI6S,SAAS,CACX,iBACA;QAEJ;QAEA,OAAO,IAAI,CAACb,IAAI,CACd,OAAOlJ;YACL,MAAMgI,WAAW,MAAM,IAAI,CAAC+P,qBAAqB,CAAC/X,KAAKD;YACvD,IAAI,IAAI,CAAC/F,WAAW,IAAI9C,IAAIuK,UAAU,KAAK,KAAK;gBAC9C,MAAM1B;YACR;YACA,OAAOiI;QACT,GACA;YAAElS;YAAKoB;YAAKjB;YAAUuM;QAAM;IAEhC;IAQA,MAAcuV,sBACZ/X,GAAkD,EAClDD,GAAiB,EACgB;QACjC,OAAOjO,YAAYiP,KAAK,CAAC9O,eAAe8lB,qBAAqB,EAAE;YAC7D,OAAO,IAAI,CAACM,yBAAyB,CAACrY,KAAKD;QAC7C;IACF;IAEA,MAAgBsY,0BACdrY,GAAkD,EAClDD,GAAiB,EACgB;QACjC,wGAAwG;QACxG,+DAA+D;QAC/D,IAAI,IAAI,CAAC9D,UAAU,CAAClC,GAAG,IAAIiG,IAAI/J,QAAQ,KAAK,gBAAgB;YAC1D,OAAO;gBACL2T,MAAM;gBACNtH,MAAMxS,aAAa8e,UAAU,CAAC;YAChC;QACF;QACA,MAAM,EAAE1X,GAAG,EAAEsL,KAAK,EAAE,GAAGxC;QAEvB,IAAI;YACF,IAAI3J,SAAsC;YAE1C,MAAMiiB,QAAQphB,IAAIuK,UAAU,KAAK;YACjC,IAAI8W,eAAe;YAEnB,IAAID,OAAO;gBACT,IAAI,IAAI,CAAChf,kBAAkB,CAACmC,GAAG,EAAE;oBAC/B,2CAA2C;oBAC3CpF,SAAS,MAAM,IAAI,CAAC6gB,kBAAkB,CAAC;wBACrCvS,QAAQpU,eAAeyP,IAAIlK,GAAG,EAAE;wBAChCqO,MAAM9U;wBACNmT;wBACAnL,QAAQ,CAAC;wBACTyU,WAAW;wBACXwL,cAAc;wBACdvgB,KAAKiJ,IAAIlK,GAAG,CAACiB,GAAG;oBAClB;oBACAwhB,eAAeliB,WAAW;gBAC5B;gBAEA,IAAI,CAACA,UAAW,MAAM,IAAI,CAACwU,OAAO,CAAC,SAAU;oBAC3CxU,SAAS,MAAM,IAAI,CAAC6gB,kBAAkB,CAAC;wBACrCvS,QAAQpU,eAAeyP,IAAIlK,GAAG,EAAE;wBAChCqO,MAAM;wBACN3B;wBACAnL,QAAQ,CAAC;wBACTyU,WAAW;wBACX,qEAAqE;wBACrEwL,cAAc;wBACdvgB,KAAKiJ,IAAIlK,GAAG,CAACiB,GAAG;oBAClB;oBACAwhB,eAAeliB,WAAW;gBAC5B;YACF;YACA,IAAImiB,aAAa,CAAC,CAAC,EAAEthB,IAAIuK,UAAU,EAAE;YAErC,IACE,CAAClR,eAAeyP,IAAIlK,GAAG,EAAE,wBACzB,CAACO,UACDlH,oBAAoBoe,QAAQ,CAACiL,aAC7B;gBACA,0DAA0D;gBAC1D,8BAA8B;gBAC9B,IAAIA,eAAe,UAAU,CAAC,IAAI,CAACvc,UAAU,CAAClC,GAAG,EAAE;oBACjD1D,SAAS,MAAM,IAAI,CAAC6gB,kBAAkB,CAAC;wBACrCvS,QAAQpU,eAAeyP,IAAIlK,GAAG,EAAE;wBAChCqO,MAAMqU;wBACNhW;wBACAnL,QAAQ,CAAC;wBACTyU,WAAW;wBACX,8DAA8D;wBAC9D,SAAS;wBACTwL,cAAc;wBACdvgB,KAAKiJ,IAAIlK,GAAG,CAACiB,GAAG;oBAClB;gBACF;YACF;YAEA,IAAI,CAACV,QAAQ;gBACXA,SAAS,MAAM,IAAI,CAAC6gB,kBAAkB,CAAC;oBACrCvS,QAAQpU,eAAeyP,IAAIlK,GAAG,EAAE;oBAChCqO,MAAM;oBACN3B;oBACAnL,QAAQ,CAAC;oBACTyU,WAAW;oBACX,iEAAiE;oBACjE,SAAS;oBACTwL,cAAc;oBACdvgB,KAAKiJ,IAAIlK,GAAG,CAACiB,GAAG;gBAClB;gBACAyhB,aAAa;YACf;YAEA,IACEhhB,QAAQC,GAAG,CAACghB,QAAQ,KAAK,gBACzB,CAACF,gBACA,MAAM,IAAI,CAAC1N,OAAO,CAAC,cACpB,CAAE,MAAM,IAAI,CAACA,OAAO,CAAC,SACrB;gBACA,IAAI,CAACnR,oBAAoB;YAC3B;YAEA,IAAI,CAACrD,QAAQ;gBACX,iEAAiE;gBACjE,wDAAwD;gBACxD,IAAI,IAAI,CAAC4F,UAAU,CAAClC,GAAG,EAAE;oBACvB,OAAO;wBACL6P,MAAM;wBACN,mDAAmD;wBACnDtH,MAAMxS,aAAa8e,UAAU,CAC3B,CAAC;;;;;;;;;;;;;uBAaQ,CAAC;oBAEd;gBACF;gBAEA,MAAM,IAAI3Z,kBACR,qBAA8C,CAA9C,IAAID,MAAM,sCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6C;YAEjD;YAEA,0EAA0E;YAC1E,yCAAyC;YACzC,IAAIqB,OAAO8V,UAAU,CAACwB,WAAW,EAAE;gBACjCrd,eAAe0P,IAAIlK,GAAG,EAAE,SAAS;oBAC/BmO,YAAY5N,OAAO8V,UAAU,CAACwB,WAAW,CAAC1J,UAAU;oBACpD5M,QAAQ1B;gBACV;YACF,OAAO;gBACLnF,kBAAkBwP,IAAIlK,GAAG,EAAE;YAC7B;YAEA,IAAI;gBACF,OAAO,MAAM,IAAI,CAACsV,8BAA8B,CAC9C;oBACE,GAAGpL,GAAG;oBACN/J,UAAUuiB;oBACVvc,YAAY;wBACV,GAAG+D,IAAI/D,UAAU;wBACjB8D;oBACF;gBACF,GACA1J;YAEJ,EAAE,OAAOqiB,oBAAoB;gBAC3B,IAAIA,8BAA8B3jB,iBAAiB;oBACjD,MAAM,qBAAmD,CAAnD,IAAIC,MAAM,2CAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAkD;gBAC1D;gBACA,MAAM0jB;YACR;QACF,EAAE,OAAOlY,OAAO;YACd,MAAMmY,oBAAoBtoB,eAAemQ;YACzC,MAAMwX,iBAAiBW,6BAA6B1jB;YACpD,IAAI,CAAC+iB,gBAAgB;gBACnB,IAAI,CAACvX,QAAQ,CAACkY;YAChB;YACAzhB,IAAIuK,UAAU,GAAG;YACjB,MAAMmX,qBAAqB,MAAM,IAAI,CAACC,0BAA0B,CAC9D7Y,IAAIlK,GAAG,CAACiB,GAAG;YAGb,IAAI6hB,oBAAoB;gBACtB,mEAAmE;gBACnE,mCAAmC;gBACnCtoB,eAAe0P,IAAIlK,GAAG,EAAE,SAAS;oBAC/BmO,YAAY2U,mBAAmBjL,WAAW,CAAE1J,UAAU;oBACtD5M,QAAQ1B;gBACV;gBAEA,OAAO,IAAI,CAACyV,8BAA8B,CACxC;oBACE,GAAGpL,GAAG;oBACN/J,UAAU;oBACVgG,YAAY;wBACV,GAAG+D,IAAI/D,UAAU;wBACjB,sDAAsD;wBACtD,sCAAsC;wBACtC8D,KAAKiY,iBACDW,kBAAkBxjB,UAAU,GAC5BwjB;oBACN;gBACF,GACA;oBACEnW;oBACA2J,YAAYyM;gBACd;YAEJ;YACA,OAAO;gBACLhP,MAAM;gBACNtH,MAAMxS,aAAa8e,UAAU,CAAC;YAChC;QACF;IACF;IAEA,MAAakK,kBACX/Y,GAAiB,EACjBjK,GAAkB,EAClBoB,GAAmB,EACnBjB,QAAgB,EAChBuM,QAAwB,CAAC,CAAC,EACF;QACxB,OAAO,IAAI,CAAC0H,aAAa,CAAC,CAAClK,MAAQ,IAAI,CAAC+X,qBAAqB,CAAC/X,KAAKD,MAAM;YACvEjK;YACAoB;YACAjB;YACAuM;QACF;IACF;IAEA,MAAa7K,UACX7B,GAAkB,EAClBoB,GAAmB,EACnBlB,SAA8D,EAC9DmiB,aAAa,IAAI,EACF;QACf,MAAM,EAAEliB,QAAQ,EAAEuM,KAAK,EAAE,GAAGxM,YAAYA,YAAYpH,SAASkH,IAAIiB,GAAG,EAAG;QAEvE,uDAAuD;QACvD,IAAI,IAAI,CAACzB,UAAU,CAACuF,IAAI,EAAE;YACxB,IAAI,CAACtK,eAAeuF,KAAK,WAAW;gBAClCxF,eAAewF,KAAK,UAAU,IAAI,CAACR,UAAU,CAACuF,IAAI,CAACpC,aAAa;YAClE;YACAnI,eAAewF,KAAK,iBAAiB,IAAI,CAACR,UAAU,CAACuF,IAAI,CAACpC,aAAa;QACzE;QAEAvB,IAAIuK,UAAU,GAAG;QACjB,OAAO,IAAI,CAAC8E,WAAW,CAAC,MAAMzQ,KAAKoB,KAAKjB,UAAWuM,OAAO2V;IAC5D;AACF"}