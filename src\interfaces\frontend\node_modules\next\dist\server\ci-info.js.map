{"version": 3, "sources": ["../../src/server/ci-info.ts"], "sourcesContent": ["import ciEnvironment from 'next/dist/compiled/ci-info'\n\nconst { isCI: _isCI, name: _name } = ciEnvironment\n\nconst isZeitNow = !!process.env.NOW_BUILDER\n\nconst envStack = process.env.STACK\nconst isHeroku =\n  typeof envStack === 'string' && envStack.toLowerCase().includes('heroku')\n\nexport const isCI = isZeitNow || isHeroku || _isCI\nexport const name = isZeitNow ? 'ZEIT Now' : isHeroku ? 'Heroku' : _name\n\n// This boolean indicates if the CI platform has first-class Next.js support,\n// which allows us to disable certain messages which do not require their\n// action.\nexport const hasNextSupport = Boolean(isZeitNow)\n"], "names": ["hasNextSupport", "isCI", "name", "_isCI", "_name", "ciEnvironment", "isZeitNow", "process", "env", "NOW_BUILDER", "envStack", "STACK", "isHeroku", "toLowerCase", "includes", "Boolean"], "mappings": ";;;;;;;;;;;;;;;;IAgBaA,cAAc;eAAdA;;IANAC,IAAI;eAAJA;;IACAC,IAAI;eAAJA;;;+DAXa;;;;;;AAE1B,MAAM,EAAED,MAAME,KAAK,EAAED,MAAME,KAAK,EAAE,GAAGC,eAAa;AAElD,MAAMC,YAAY,CAAC,CAACC,QAAQC,GAAG,CAACC,WAAW;AAE3C,MAAMC,WAAWH,QAAQC,GAAG,CAACG,KAAK;AAClC,MAAMC,WACJ,OAAOF,aAAa,YAAYA,SAASG,WAAW,GAAGC,QAAQ,CAAC;AAE3D,MAAMb,OAAOK,aAAaM,YAAYT;AACtC,MAAMD,OAAOI,YAAY,aAAaM,WAAW,WAAWR;AAK5D,MAAMJ,iBAAiBe,QAAQT"}