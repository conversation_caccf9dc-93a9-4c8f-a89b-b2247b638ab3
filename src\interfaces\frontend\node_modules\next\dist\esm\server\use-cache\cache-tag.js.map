{"version": 3, "sources": ["../../../src/server/use-cache/cache-tag.ts"], "sourcesContent": ["import { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport { validateTags } from '../lib/patch-fetch'\n\nexport function cacheTag(...tags: string[]): void {\n  if (!process.env.__NEXT_USE_CACHE) {\n    throw new Error(\n      'cacheTag() is only available with the experimental.useCache config.'\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (!workUnitStore || workUnitStore.type !== 'cache') {\n    throw new Error(\n      'cacheTag() can only be called inside a \"use cache\" function.'\n    )\n  }\n\n  const validTags = validateTags(tags, 'cacheTag()')\n\n  if (!workUnitStore.tags) {\n    workUnitStore.tags = validTags\n  } else {\n    workUnitStore.tags.push(...validTags)\n  }\n}\n"], "names": ["workUnitAsyncStorage", "validateTags", "cacheTag", "tags", "process", "env", "__NEXT_USE_CACHE", "Error", "workUnitStore", "getStore", "type", "validTags", "push"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,iDAAgD;AACrF,SAASC,YAAY,QAAQ,qBAAoB;AAEjD,OAAO,SAASC,SAAS,GAAGC,IAAc;IACxC,IAAI,CAACC,QAAQC,GAAG,CAACC,gBAAgB,EAAE;QACjC,MAAM,qBAEL,CAFK,IAAIC,MACR,wEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,gBAAgBR,qBAAqBS,QAAQ;IACnD,IAAI,CAACD,iBAAiBA,cAAcE,IAAI,KAAK,SAAS;QACpD,MAAM,qBAEL,CAFK,IAAIH,MACR,iEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMI,YAAYV,aAAaE,MAAM;IAErC,IAAI,CAACK,cAAcL,IAAI,EAAE;QACvBK,cAAcL,IAAI,GAAGQ;IACvB,OAAO;QACLH,cAAcL,IAAI,CAACS,IAAI,IAAID;IAC7B;AACF"}