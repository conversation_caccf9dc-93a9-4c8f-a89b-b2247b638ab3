{"version": 3, "sources": ["../../../../src/server/lib/experimental/create-env-definitions.ts"], "sourcesContent": ["import type { LoadedEnvFiles } from '@next/env'\nimport { join } from 'node:path'\nimport { writeFile } from 'node:fs/promises'\n\nexport async function createEnvDefinitions({\n  distDir,\n  loadedEnvFiles,\n}: {\n  distDir: string\n  loadedEnvFiles: LoadedEnvFiles\n}) {\n  const envLines = []\n  const seenKeys = new Set()\n  // env files are in order of priority\n  for (const { path, env } of loadedEnvFiles) {\n    for (const key in env) {\n      if (!seenKeys.has(key)) {\n        envLines.push(`      /** Loaded from \\`${path}\\` */`)\n        envLines.push(`      ${key}?: string`)\n        seenKeys.add(key)\n      }\n    }\n  }\n  const envStr = envLines.join('\\n')\n\n  const definitionStr = `// Type definitions for Next.js environment variables\ndeclare global {\n  namespace NodeJS {\n    interface ProcessEnv {\n${envStr}\n    }\n  }\n}\nexport {}`\n\n  if (process.env.NODE_ENV === 'test') {\n    return definitionStr\n  }\n\n  try {\n    // we expect the types directory to already exist\n    const envDtsPath = join(distDir, 'types', 'env.d.ts')\n    // do not await, this is not essential for further process\n    writeFile(envDtsPath, definitionStr, 'utf-8')\n  } catch (e) {\n    console.error('Failed to write env.d.ts:', e)\n  }\n}\n"], "names": ["join", "writeFile", "createEnvDefinitions", "distDir", "loadedEnvFiles", "envLines", "<PERSON><PERSON><PERSON><PERSON>", "Set", "path", "env", "key", "has", "push", "add", "envStr", "definitionStr", "process", "NODE_ENV", "envDtsPath", "e", "console", "error"], "mappings": "AACA,SAASA,IAAI,QAAQ,YAAW;AAChC,SAASC,SAAS,QAAQ,mBAAkB;AAE5C,OAAO,eAAeC,qBAAqB,EACzCC,OAAO,EACPC,cAAc,EAIf;IACC,MAAMC,WAAW,EAAE;IACnB,MAAMC,WAAW,IAAIC;IACrB,qCAAqC;IACrC,KAAK,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAE,IAAIL,eAAgB;QAC1C,IAAK,MAAMM,OAAOD,IAAK;YACrB,IAAI,CAACH,SAASK,GAAG,CAACD,MAAM;gBACtBL,SAASO,IAAI,CAAC,CAAC,wBAAwB,EAAEJ,KAAK,KAAK,CAAC;gBACpDH,SAASO,IAAI,CAAC,CAAC,MAAM,EAAEF,IAAI,SAAS,CAAC;gBACrCJ,SAASO,GAAG,CAACH;YACf;QACF;IACF;IACA,MAAMI,SAAST,SAASL,IAAI,CAAC;IAE7B,MAAMe,gBAAgB,CAAC;;;;AAIzB,EAAED,OAAO;;;;SAIA,CAAC;IAER,IAAIE,QAAQP,GAAG,CAACQ,QAAQ,KAAK,QAAQ;QACnC,OAAOF;IACT;IAEA,IAAI;QACF,iDAAiD;QACjD,MAAMG,aAAalB,KAAKG,SAAS,SAAS;QAC1C,0DAA0D;QAC1DF,UAAUiB,YAAYH,eAAe;IACvC,EAAE,OAAOI,GAAG;QACVC,QAAQC,KAAK,CAAC,6BAA6BF;IAC7C;AACF"}